package com.facishare.crm.si.erpdss.common;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.lang.func.Func0;
import com.fxiaoke.erpdss.connector.core.api.ErpdssApi;
import com.fxiaoke.erpdss.connector.core.model.SystemParams;
import com.fxiaoke.erpdss.connector.core.util.FsContext;
import com.google.common.base.Joiner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-11-21
 */
@Service
public class ErpHelper {
    //单位毫秒
    TimedCache<String, SystemParams> systemParamsTimedCache = CacheUtil.newTimedCache(120000);
    TimedCache<String, String> tokenAccessCache = CacheUtil.newTimedCache(1800_000);

    @Autowired
    private ErpdssApi erpdssApi;


    public SystemParams getSystemParams() {
        String dcId = FsContext.get().getDcId();
        //本地缓存
        return systemParamsTimedCache.get(dcId, false,
                () -> erpdssApi.getSystemParams());
    }

    public String getAccessToken(Func0<String> supplier, String type, String... subKeys) {

        String key = Joiner.on("_").skipNulls().join(type, Joiner.on("_").useForNull("null").join(subKeys));

        return tokenAccessCache.get(key, false, supplier);

    }
}
