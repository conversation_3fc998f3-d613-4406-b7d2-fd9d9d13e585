package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.paas.appframework.core.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BehaviorRecordWebDetailController extends SFAWebDetailController {

    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_780)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
    }
}
