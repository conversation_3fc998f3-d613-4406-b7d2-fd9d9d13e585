package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;

import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;


@Slf4j
public class EnterpriseInfoWebDetailController extends SFAWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        handleLayout(result);
        return result;
    }

    private void handleLayout(Result result){
        if (ObjectUtils.isEmpty(result.getLayout())){
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("EquityRelationshipObj_root_node_path_related_list"));
        if(RequestUtil.isMobileOrH5Request()){
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("business_insights"));
        }
    }


}
