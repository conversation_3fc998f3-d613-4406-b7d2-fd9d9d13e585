package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class BomAttributeConstraintLinesWebDetailController extends StandardWebDetailController {
    private  List<String> removeActionList = Lists.newArrayList(
            ObjectAction.INVALID.getActionCode(),
            ObjectAction.LOCK.getActionCode(),
            ObjectAction.UNLOCK.getActionCode(),
            ObjectAction.CONFIRM.getActionCode(),
            ObjectAction.REJECT.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.CLONE.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.CREATE.getActionCode(),
            ObjectAction.PRINT.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (Objects.nonNull(result.getLayout())) {
            specialLogicForLayout(result.getLayout().toLayout());
        }
        return result;
    }

    private void specialLogicForLayout(ILayout layout) {
        WebDetailLayout.of(layout).removeButtonsByActionCode(removeActionList);
    }
}
