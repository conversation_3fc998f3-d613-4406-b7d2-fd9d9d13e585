package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.Objects;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-07-30
 */
public class BomRelatedListController extends StandardRelatedListController {

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        query.setPermissionType(0);
        return query;
    }
}
