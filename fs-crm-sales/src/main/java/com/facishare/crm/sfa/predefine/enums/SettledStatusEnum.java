package com.facishare.crm.sfa.predefine.enums;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum SettledStatusEnum {
    No_Settled("no", "未结算"),
    Part_Settled("part", "部分结算"),
    All_Settled("all", "已结算");
    ;
    private final String value;
    private final String label;

    SettledStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static SettledStatusEnum getByCode(String status) {
        for (SettledStatusEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static List<String> getAllStatus() {
        List<String> allStatus = Lists.newArrayList();
        for (SettledStatusEnum d : SettledStatusEnum.values()) {
            allStatus.add(d.getValue());
        }
        return allStatus;
    }
}
