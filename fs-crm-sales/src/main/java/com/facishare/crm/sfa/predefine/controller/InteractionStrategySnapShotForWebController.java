package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.InteractionStrategyConstants;
import com.facishare.crm.sfa.predefine.service.InteractionStrategyRenderService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetSnapShotForWeb;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

/**
 * InteractionStrategySnapShotForWebController
 *
 * <AUTHOR>
 */
public class InteractionStrategySnapShotForWebController extends StandardSnapShotForWebController {
    private final InteractionStrategyRenderService interactionStrategyRenderService = SpringUtil.getContext().getBean(InteractionStrategyRenderService.class);

    @Override
    protected GetSnapShotForWeb.Result doService(GetSnapShotForWeb.Arg arg) {
        GetSnapShotForWeb.Result result = super.doService(arg);
        render(result);
        return result;
    }

    private void render(GetSnapShotForWeb.Result result) {
        Optional.ofNullable(result)
                .map(GetSnapShotForWeb.Result::getData)
                .map(x -> ObjectDataExt.of(x).getObjectData())
                .ifPresent(data -> {
                            interactionStrategyRenderService.render(data, User.systemUser(controllerContext.getTenantId()), InteractionStrategyConstants.CONDITION);
                        }
                );
    }
}