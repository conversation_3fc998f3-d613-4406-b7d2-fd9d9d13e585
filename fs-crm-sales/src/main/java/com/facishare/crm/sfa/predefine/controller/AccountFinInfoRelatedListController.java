package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.AccountFinInfoUtil;

public class AccountFinInfoRelatedListController extends SFARelatedListController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //AccountAddrUtil.changeButton(result);
        if(AccountAddrUtil.setButtonsOfDataCheckArg(arg)) {
            AccountAddrUtil.setButtonsOfData(controllerContext.getUser(), result, SFAPreDefineObject.AccountFinInfo.getApiName(), null);
        }
        return result;
    }
}
