package com.facishare.crm.sfa.predefine.enums;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum MatchStatusEnum {
    No("no", "未核销"),
    Part("part", "部分核销"),
    All("all", "全部核销");
    ;
    private final String value;
    private final String label;

    MatchStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static MatchStatusEnum getByCode(String status) {
        for (MatchStatusEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static List<String> getAllStatus() {
        List<String> allStatus = Lists.newArrayList();
        for (MatchStatusEnum d : MatchStatusEnum.values()) {
            allStatus.add(d.getValue());
        }
        return allStatus;
    }
}
