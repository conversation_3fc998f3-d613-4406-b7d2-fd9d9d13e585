package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 市场活动详情 class
 *
 * <AUTHOR>
 * @date 2019/2/27
 */

@Slf4j
public class MarketingEventDetailController extends SFADetailController {
    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    @Override
    protected ILayout getLayout() {

        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(Utils.MARKETING_EVENT_API_NAME),
                Lists.newArrayList(ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));
        ILayout layout = super.getLayout();
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> childComponents = x.getChildComponents();
                if (!objApiNameAndActionCodePrivilegeMapping.get(Utils.MARKETING_EVENT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                    childComponents.removeIf(c -> ATTACH_COMPONENT.equals(c.getName()));
                    x.setChildComponents(childComponents);
                } else {
                    Optional<IComponent> attachComponent = childComponents.stream().filter(c -> ATTACH_COMPONENT.equals(c.getName())).findFirst();
                    if(attachComponent.isPresent()) {
                        if (objApiNameAndActionCodePrivilegeMapping.get(Utils.MARKETING_EVENT_API_NAME).get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
                            List<IButton> buttons = getButtons();
                            attachComponent.get().setButtons(buttons);
                        }
                    }
                }
            } catch (MetadataServiceException e) {
                log.error("getChildComponents error", e);
            }

        });

        return layout;
    }

    private List<IButton> getButtons() {
        List<IButton> buttons = Lists.newArrayList();
        IButton addButton = new Button();
        addButton.setAction("Add");
        addButton.setActionType("default");
        addButton.setLabel(I18N.text(I18NKey.action_upload));
        addButton.setName("MarketingEventAttObj_Add_button_default");
        buttons.add(addButton);
        IButton deleteButton = new Button();
        deleteButton.setAction("Delete");
        deleteButton.setActionType("default");
        deleteButton.setLabel(I18N.text(I18NKey.action_delete));
        deleteButton.setName("MarketingEventAttObj_Delete_button_default");
        buttons.add(deleteButton);
        return buttons;
    }
}
