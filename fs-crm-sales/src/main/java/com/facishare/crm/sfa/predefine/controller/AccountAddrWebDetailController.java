package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class AccountAddrWebDetailController extends SFAWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null || newResult.getData() == null) {
            return newResult;
        }
        IObjectData accountData = arg.isFromRecycleBin() ? this.serviceFacade.findObjectDataIncludeDeleted(this.controllerContext.getUser(),
                String.valueOf(newResult.getData().get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName())), Utils.ACCOUNT_API_NAME) :
                this.serviceFacade.findObjectData(this.controllerContext.getUser(),
                        String.valueOf(newResult.getData().get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName())), Utils.ACCOUNT_API_NAME);
        if (accountData == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_CONTACTNOTNULL, I18N.text("AccountObj.attribute.self.display_name")));
        }

        LayoutExt layoutExt = LayoutExt.of(newResult.getLayout());
        Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
        if (!headInfoComponentOp.isPresent()) {
            return newResult;
        }

        IComponent headInfoComponent = headInfoComponentOp.get();
        List<IButton> buttons = headInfoComponent.getButtons();
        //用客户的生命状态和业务状态过滤一遍按钮
        List<IButton> hasPrivilegeButtons = buttons.stream().filter((x) -> {
            return AccountAddrUtil.checkLifeStatus(ObjectAction.of(x.getAction()), ObjectDataExt.of(accountData))
                    && AccountAddrUtil.checkLockStatus(ObjectAction.of(x.getAction()), ObjectDataExt.of(accountData), controllerContext.getUser());
        }).collect(Collectors.toList());
        WebDetailLayout.of(newResult.getLayout().toLayout()).setButtons(hasPrivilegeButtons, "head_info");
        return newResult;
    }
}
