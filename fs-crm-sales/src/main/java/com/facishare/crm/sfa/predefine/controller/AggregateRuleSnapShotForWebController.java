package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleService;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants.Field;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 聚合值修改记录明细
 */
public class AggregateRuleSnapShotForWebController extends StandardSnapShotForWebController {

    AggregateRuleService aggregateRuleService = SpringUtil.getContext().getBean(AggregateRuleService.class);

    @Override
    protected void fillFieldInfo(User user, IObjectDescribe objectDescribe, Map<String, Object> objData) {
        super.fillFieldInfo(user, objectDescribe, objData);
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.AGGREGATE_RULE_API_NAME);
        describe.getFieldDescribes().stream().filter(fieldDescribes -> Field.CONDITION.equals(fieldDescribes.getApiName())).forEach(fieldDescribes -> {
            if (null != objData.get(fieldDescribes.getApiName())) {
                String condition = aggregateRuleService.resolveCondition(controllerContext.getUser(), objData.get(Field.AGGREGATE_OBJECT).toString(), objData.get(fieldDescribes.getApiName()).toString());
                Map<String,Object> conditionMap = new HashMap<>();
                conditionMap.put("type","CONDITION");
                conditionMap.put("value",condition);
                objData.put(fieldDescribes.getApiName(), JSONObject.toJSONString(conditionMap));
            }
        });
    }
}
