package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by zhangtao on 2019/1/2.
 */
public class BehaviorRecordListHeaderController extends StandardListHeaderController {
    private final static List<String> whatApiNameList = Lists.newArrayList("related_api_names", "related_object_data");

    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_780)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (result != null && RequestUtil.isMobileRequest()) {
            List<CommonFilterField.FilterField> filterFieldList = result.getFilterFields();
            if (CollectionUtils.notEmpty(filterFieldList)) {
                filterFieldList.removeIf(m -> whatApiNameList.contains(m.getFieldName()));
                result.setFilterFields(filterFieldList);
            }
        }
        return result;
    }
}
