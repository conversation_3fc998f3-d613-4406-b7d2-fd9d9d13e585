package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * Created by py on 2022/6/30
 */
public class BomAttributeConstraintLinesRelatedListController extends StandardRelatedListController {

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.getQueryResult(query);
        List<IObjectData> dataList = queryResult.getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            ProductConstraintUtil.handleData(dataList,controllerContext.getUser());
        }
        return queryResult;
    }

    @Override
    protected List<IButton> getButtons(ILayout layout) {
        List<IButton> buttons = super.getButtons(layout);
        buttons.removeIf(x -> "Add_button_default".equals(x.getName()));
        return buttons;
    }

}
