package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.config.CouponProductConditionConfig;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Optional;

public class CouponPlanListHeaderController extends StandardListHeaderController {
    private final CouponProductConditionConfig couponProductConditionConfig = SpringUtil.getContext().getBean(CouponProductConditionConfig.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        removeButton(result);
        Optional<List<SelectOption>> selectOptionOpt = couponProductConditionConfig.getSelectOption(controllerContext.getTenantId());
        if (!selectOptionOpt.isPresent()) {
            return result;
        }
        SelectOneFieldDescribe typeSelectOneFieldDescribe = (SelectOneFieldDescribe) result.copyFieldToDescribeExt(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName());
        if(typeSelectOneFieldDescribe != null){
            for (SelectOption selectOption : selectOptionOpt.get()) {
                typeSelectOneFieldDescribe.addSelectOption(selectOption);
            }
        }
        return result;
    }


    private void removeButton(Result result) {
        CouponUtils.removeButton(result);
    }


}
