package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.ObjectUtils;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.common.util.ObjectAction.INVALID;
import static com.facishare.paas.appframework.common.util.ObjectAction.UPDATE;

/**
 * <AUTHOR> lik
 * @date : 2024/8/21 18:47
 */

public class MCRExpansionPlanRelatedListController extends StandardRelatedListController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        setUpdButton(result);
        return result;
    }

    private void setUpdButton(Result result){
        if(CollectionUtil.isEmpty(result.getDataList())){
            return;
        }
        //先判断用户是否有编辑权限
        boolean objApiNameFuncPrivilege= serviceFacade.funPrivilegeCheck(controllerContext.getUser(),
                controllerContext.getObjectApiName(),UPDATE.getActionCode());
        if(!objApiNameFuncPrivilege){
            log.warn("setUpdButton UPDATE is not Privilege");
            return;
        }
        //判断数据是否有编辑权限
        Map<String, Permissions> privMap = serviceFacade.checkPrivilege(controllerContext.getUser(), ObjectDataDocument.ofDataList(result.getDataList()), objectDescribe, UPDATE.getActionCode());
        if(ObjectUtils.allIsEmpty(privMap)){
            log.warn("setUpdButton privMap is null");
            return;
        }
        ButtonInfo buttonInfo = result.getButtonInfo();
        List<ButtonDocument> buttons = buttonInfo.getButtons();
        buttons.add(ButtonDocument.fromButton(PreDefLayoutUtil.createButton(ObjectAction.UPDATE)));
        buttonInfo.setButtons(buttons);
        Map<String, List<String>> buttonMap = buttonInfo.getButtonMap();
        result.getDataList().stream().forEach(d->{
            List<String> funcCodeList = new ArrayList<>();
            if(buttonMap.containsKey(d.getId())){
                funcCodeList = buttonMap.get(d.getId());
            }
            if(privMap.containsKey(d.getId()) && Permissions.READ_WRITE.getValue().equals(privMap.get(d.getId()).getValue())){
                funcCodeList.add(UPDATE.getButtonApiName());
            }
            buttonMap.put(d.getId(),funcCodeList);
        });
        buttonInfo.setButtonMap(buttonMap);
        result.setButtonInfo(buttonInfo);
    }
}
