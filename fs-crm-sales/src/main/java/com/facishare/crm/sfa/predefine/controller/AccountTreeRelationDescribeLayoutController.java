package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;

public class AccountTreeRelationDescribeLayoutController extends StandardDescribeLayoutController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        WebDetailLayout webDetailLayout = WebDetailLayout.of(layout);
        String layoutType = arg.getLayout_type();
        if (LayoutTypes.ADD.equals(layoutType) || LayoutTypes.EDIT.equals(layoutType)) {
            webDetailLayout.removeFields(Lists.newArrayList(IObjectData.NAME));
        }
        return result;
    }
}
