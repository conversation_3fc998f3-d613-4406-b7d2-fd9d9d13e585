package com.facishare.crm.sfa.predefine.service.resource;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.privilege.model.valueobject.CrmResult;
import com.facishare.crm.sfa.model.ProductCategoryTree;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.ProductCategoryInsertImportDataAction;
import com.facishare.crm.sfa.predefine.enums.CategoryFilterEnum;
import com.facishare.crm.sfa.predefine.service.*;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.CategoryObject;
import com.facishare.crm.sfa.predefine.service.model.CheckDeleteCategoryModel;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryModel;
import com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.prm.platform.utils.DataUtils;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.proxy.CRMMetaDataServiceProxy;
import com.facishare.crm.sfa.utilities.util.ProductCategoryUtils;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryV2Validator;
import com.facishare.crm.sfa.utilities.validator.ProductCategoryValidator;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ProductCategoryServiceImpl;
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.ProductCategoryObject.*;
import static com.facishare.crm.sfa.utilities.util.ProductCategoryUtils.NEW_CATEGORY_LIMIT;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.*;

@ServiceModule("product_category")
@Component
@Slf4j
@Data
public class ProductCategoryServiceResource {

    @Resource
    private DescribeEnhancer describeEnhancer;

    @Resource
    private ServiceFacadeProxy serviceFacadeProxy;

    @Autowired
    private ModuleCtrlConfigService moduleCtrlConfigService;

    @Autowired
    private SpuSkuService spuSkuService;

    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private DhtPriceBookService dhtPriceBookService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private ProductCategoryValidator productCategoryValidator;
    @Autowired
    private ProductCategoryV2Validator productCategoryV2Validator;
    @Autowired
    private ProductCategoryBizService productCategoryBizService;
    @Autowired
    private ProductCategoryServiceImpl categoryService;
    private ThreadPoolExecutor executorPool;
    @Autowired
    private CRMMetaDataServiceProxy crmMetaDataServiceProxy;
    @Resource
    private CategoryStaticUtilService categoryStaticUtilService;
    @Resource
    private AsyncBootstrap asyncBootstrap;
    @Resource
    private ProductCategoryUtils productCategoryUtils;

    @PostConstruct
    public void initPool() {
        int corePoolSize = 50;
        try {
            corePoolSize = Runtime.getRuntime().availableProcessors();
        } catch (Exception e) {
            log.error("获取 availableProcessors 失败", e);
        }
        executorPool = new ThreadPoolExecutor(corePoolSize, corePoolSize << 1, 10, TimeUnit.SECONDS, new LinkedBlockingQueue<>(300), new ThreadFactoryBuilder().setNameFormat("product-category-pool-%d").setUncaughtExceptionHandler((thread, throwable) -> log.error("ThreadPool {} got exception", thread, throwable)).build(), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @PreDestroy
    public void destroyPool() {
        if (executorPool != null && !executorPool.isShutdown()) {
            log.info("正在关闭产品分类线程池...");
            executorPool.shutdown();
            try {
                // 等待60秒让正在执行的任务完成
                if (!executorPool.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("线程池未能在60秒内正常关闭，强制关闭");
                    executorPool.shutdownNow();
                    // 再等待30秒
                    if (!executorPool.awaitTermination(30, TimeUnit.SECONDS)) {
                        log.error("线程池强制关闭失败");
                    }
                }
            } catch (InterruptedException e) {
                log.error("等待线程池关闭时被中断", e);
                executorPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @ServiceMethod("add")
    public ProductCategoryObject.SingleResult add(ServiceContext context, ObjectDataDocument objectDataDocument) throws MetadataServiceException {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), ProductCategoryModel.Metadata.API_NAME, SystemConstants.ActionCode.Add.getActionCode());
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(objectDataDocument);
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(false);
        optionInfo.setUseValidationRule(false);
        optionInfo.setSkipFuncValidate(true);
        arg.setOptionInfo(optionInfo);
        BaseObjectSaveAction.Result saveResult = serviceFacadeProxy.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        return ProductCategoryObject.SingleResult.builder().result(getDocument(saveResult)).build();
    }

    public ObjectDataDocument getDocument(BaseObjectSaveAction.Result saveResult) {
        return ObjectDataDocument.of(saveResult.getObjectData());
    }

    @ServiceMethod("search_category_data")
    public List<ObjectDataDocument> searchCategory(ServiceContext context, SearchArg searchArg) {
        if (StringUtils.isBlank(searchArg.getFieldApiName())) {
            return Lists.newArrayList();

        }
        if (CollectionUtils.isEmpty(searchArg.getFieldValues())) {
            return Lists.newArrayList();
        }
        String fieldApiName = searchArg.getFieldApiName();
        if ("id".equals(fieldApiName)) {
            fieldApiName = "_id";
        }
        if (!categoryStaticUtilService.includeField(fieldApiName)) {
            throw new ValidateException(categoryStaticUtilService.text(SFA_NOT_SUPPORT_SEARCH_FIELD, fieldApiName));
        }
        int searchCategoryValueSize = categoryStaticUtilService.getSearchCategoryValueSize(context.getTenantId());
        if (searchArg.getFieldValues().size() > searchCategoryValueSize) {
            throw new ValidateException(categoryStaticUtilService.text(SFA_SEARCH_CATEGORY_VALUE_SIZE_EXCEED_LIMIT, searchCategoryValueSize, searchArg.getFieldValues().size()));
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), fieldApiName, searchArg.getFieldValues());
        query.setLimit(searchCategoryValueSize);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryIgnoreAll(context.getUser(), API_NAME, query).getData();
        return Lists.newArrayList(ObjectDataDocument.ofList(dataList));
    }

    @ServiceMethod("add_to_db")
    public ProductCategoryObject.SingleResult add2db(ServiceContext context, ObjectDataDocument objectDataDocument) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), ProductCategoryModel.Metadata.API_NAME, SystemConstants.ActionCode.Add.getActionCode());
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(objectDataDocument);
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(false);
        optionInfo.setUseValidationRule(false);
        optionInfo.setSkipFuncValidate(true);
        arg.setOptionInfo(optionInfo);
        objectDataDocument.toObjectData().set("dataFrom", "add_to_db");
        BaseObjectSaveAction.Result saveResult = serviceFacadeProxy.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        return ProductCategoryObject.SingleResult.builder().result(getDocument(saveResult)).build();
    }

    public void savaCategoryCheckAsync(User user, String action, ObjectDataDocument objectDataDocument, List<IObjectData> allCategory) {
        String pid = DataUtils.getValue(objectDataDocument, PID, String.class, "");
        IObjectDescribe categoryDescribe = describeEnhancer.fetchObject(user, SFAPreDefineObject.ProductCategory.getApiName());
        CompletableFuture<Void> f1 = CompletableFuture.runAsync(() -> productCategoryValidator.checkPartnerCategoryId(allCategory, pid), executorPool);
        CompletableFuture<Void> f2 = CompletableFuture.runAsync(() -> productCategoryValidator.checkNameUnique(objectDataDocument, allCategory), executorPool);
        CompletableFuture<Void> f3;
        if (StringUtils.isBlank(objectDataDocument.getId()) || "Add".equals(action)) {
            f3 = CompletableFuture.runAsync(() -> productCategoryValidator.checkDepth(objectDataDocument, allCategory), executorPool);
        } else {
            List<IObjectData> tempDataList = Lists.newArrayList(allCategory);
            tempDataList.add(objectDataDocument.toObjectData());
            f3 = CompletableFuture.runAsync(() -> productCategoryValidator.checkAllDepths(tempDataList), executorPool);
        }
        CompletableFuture<Void> f4 = CompletableFuture.runAsync(() -> productCategoryValidator.checkCodeUnique(allCategory, objectDataDocument), executorPool);
        CompletableFuture<Void> f5 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCategoryTotalLimit(user, NEW_CATEGORY_LIMIT), executorPool);
        CompletableFuture<Void> f6 = CompletableFuture.runAsync(() -> productCategoryV2Validator.checkCategoryIsRelated(user, pid, categoryDescribe), executorPool);
        asyncBootstrap.composed(user, 15L, f1, f2, f3, f4, f5, f6);
    }


    @ServiceMethod("bulk_add")
    public ProductCategoryObject.Result bulkAdd(ServiceContext context, BulkAddArg arg) {
        Map<String, String> categoryPidMap = Maps.newHashMap();
        List<ProductCategoryTree> categoryList = arg.getCategoryList();
        List<ProductCategoryTree> result = Lists.newArrayList();
        Set<String> rootNameSet = Sets.newHashSet();
        ProductCategoryInsertImportDataAction.Recorder recorder = new ProductCategoryInsertImportDataAction.Recorder();
        categoryList.forEach(root -> {
            rootNameSet.add(root.getName());
            productCategoryValidator.checkLevelAboutBulk(root);
            List<ProductCategoryTree> list = productCategoryBizService.bulkAddTreeHandler(root, context.getUser(), recorder);
            result.addAll(list);
        });
        productCategoryValidator.checkRootName(context, rootNameSet, categoryList);
        List<IObjectData> dataList = productCategoryBizService.bulkAddDataFill(context.getUser(), result, categoryPidMap);
        productCategoryValidator.checkBulkAddLimit(dataList);
        serviceFacadeProxy.bulkSaveObjectData(dataList, context.getUser());
        productCategoryBizService.fillPidAndPath(context.getUser(), Sets.newHashSet(), categoryPidMap);
        productCategoryBizService.sendSynchronizeDescribeMqByGray(context.getUser());
        return ProductCategoryObject.Result.builder().result(true).build();
    }

    @ServiceMethod("list")
    public ProductCategoryObject.ListResult list(ServiceContext context, ProductCategoryListArg arg) throws MetadataServiceException {
        StopWatch stopWatch = StopWatch.create("category_list");
        if (arg == null) {
            arg = ProductCategoryListArg.builder().build();
        }
        productCategoryBizService.checkCategoryListArg(context.getUser(), arg);
        stopWatch.lap("checkCategoryListArg");
        boolean preCategoryField = productCategoryBizService.isPreCategoryField(arg.getRelatedFieldApiName());
        SearchTemplateQuery searchQuery = productCategoryBizService.buildListSearchQuery(context.getUser(), arg, preCategoryField);
        stopWatch.lap("buildListSearchQuery-execute-function");
        List<IObjectData> allCategoryDataFromDB = productCategoryBizService.findListCategoryData(context.getUser(), arg.getNodeConditions(), preCategoryField, searchQuery, arg.getReturnFields());
        stopWatch.lap("findListCategoryData");
        if (categoryStaticUtilService.ignoreAvailableRangeCategory(context.getTenantId()) || !preCategoryField) {
            stopWatch.logSlow(1000);
            return ProductCategoryObject.ListResult.builder().result(ObjectDataDocument.ofList(allCategoryDataFromDB)).build();
        }
        List<IObjectData> filterByAvailableRangeCategoryData = productCategoryBizService.filterByAvailableRange(context, arg, allCategoryDataFromDB);
        stopWatch.lap("filterByAvailableRange");
        stopWatch.logSlow(1000);
        return ProductCategoryObject.ListResult.builder().result(ObjectDataDocument.ofList(filterByAvailableRangeCategoryData)).build();
    }

    @ServiceMethod("list_children")
    public ProductCategoryObject.ListChildrenResult listChildren(ServiceContext context, ListChildrenArg arg) {
        if (arg == null) {
            return ProductCategoryObject.ListChildrenResult.builder().result(Maps.newHashMap()).build();
        }
        if (Boolean.TRUE.equals(arg.getFilterByShopCategory())) {
            Map<String, Set<String>> childMap = queryShopCategoryChildMapping(context.getUser(), arg);
            return ProductCategoryObject.ListChildrenResult.builder().result(childMap).build();
        }
        Map<String, Set<String>> childMap = queryProductCategoryMapping(context.getUser(), arg);
        return ProductCategoryObject.ListChildrenResult.builder().result(childMap).build();
    }

    public Map<String, Set<String>> queryShopCategoryChildMapping(User user, ListChildrenArg arg) {
        List<String> shopCategoryIds = arg.getCodes();
        if (CollectionUtils.isEmpty(shopCategoryIds)) {
            return Maps.newHashMap();
        }
        Map<String, Set<String>> childMap = Maps.newHashMap();
        List<IObjectData> allShopCategoryData = productCategoryUtils.findAllSortedCategory(user, Lists.newArrayList(), false, CategoryFilterEnum.SHOP).getData();
        if (CollectionUtils.isEmpty(allShopCategoryData)) {
            return Maps.newHashMap();
        }
        List<ProductAllCategoriesModel.CategoryPojo> categoryList = productCategoryBizService.queryProductAllCategories(allShopCategoryData);
        if (CollectionUtils.isEmpty(categoryList)) {
            return Maps.newHashMap();
        }
        Map<String, String> idCodeMapping = allShopCategoryData.stream()
                .filter(x -> StringUtils.isNotBlank(x.get(CODE, String.class)))
                .collect(Collectors.toMap(DBRecord::getId,
                        x -> x.get(CODE, String.class),
                        (k1, k2) -> k1));
        Map<String, String> codeIdMapping = allShopCategoryData.stream()
                .filter(x -> StringUtils.isNotBlank(x.get(CODE, String.class)))
                .collect(Collectors.toMap(x -> x.get(CODE, String.class),
                        DBRecord::getId,
                        (k1, k2) -> k1));
        for (String shopCategoryId : shopCategoryIds) {
            String code = idCodeMapping.get(shopCategoryId);
            if (StringUtils.isBlank(code)) {
                continue;
            }
            String id = codeIdMapping.get(code);
            Set<String> categoryChildrenCategoryCodes = categoryService.getCategoryChildrenCategoryCodesContainSelf(code, categoryList);
            Set<String> idsByCodes = getIdsByCodes(codeIdMapping, categoryChildrenCategoryCodes);
            childMap.put(id, idsByCodes);
        }
        return childMap;
    }

    public Set<String> getIdsByCodes(Map<String, String> codeIdMapping, Set<String> categoryChildrenCategoryCodes) {
        if (CollectionUtils.isEmpty(categoryChildrenCategoryCodes)) {
            return Sets.newHashSet();
        }
        return categoryChildrenCategoryCodes.stream().map(codeIdMapping::get).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    }

    public Map<String, Set<String>> queryProductCategoryMapping(User user, ListChildrenArg arg) {
        Map<String, Set<String>> childMap = Maps.newHashMap();
        if (arg == null || CollectionUtils.isEmpty(arg.getCodes())) {
            return childMap;
        }
        List<ProductAllCategoriesModel.CategoryPojo> allCategoryList = categoryService.getProductAllCategories(user.getTenantId(), user.getUserId());
        for (String code : arg.getCodes()) {
            Set<String> categoryChildrenCategoryCodes = categoryService.getCategoryChildrenCategoryCodesContainSelf(code, allCategoryList);
            childMap.put(code, categoryChildrenCategoryCodes);
        }
        return childMap;
    }

    @ServiceMethod("get_tree")
    public ProductCategoryObject.CategoryResult getTree(ServiceContext context, ChildCategoryArg arg) {
        ProductCategoryTree categoryTree = productCategoryBizService.getCategoryTree(context.getUser(), arg);
        if (categoryTree == null) {
            return ProductCategoryObject.CategoryResult.builder().build();
        }
        return convertTreeResult(categoryTree, false);
    }

    /**
     * 提供给函数使用的获取分类树的接口，区别于get_tree，返回值多了categoryCode
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("get_tree4function")
    public ProductCategoryObject.CategoryResult getTree4Function(ServiceContext context, ChildCategoryArg arg) {
        ProductCategoryTree categoryTree = productCategoryBizService.getCategoryTree(context.getUser(), arg);
        if (categoryTree == null) {
            return ProductCategoryObject.CategoryResult.builder().build();
        }
        return convertTreeResult(categoryTree, true);
    }


    private CategoryResult convertTreeResult(ProductCategoryTree root, boolean includeCategoryCode) {
        Map<String, List<CategoryResult>> map = Maps.newHashMap();
        CategoryResult result = buildCategory(root, includeCategoryCode);
        Queue<ProductCategoryTree> queue = new ArrayDeque<>();
        queue.offer(root);
        while (!queue.isEmpty()) {
            ProductCategoryTree item = queue.poll();
            List<ProductCategoryTree> children = item.getChildren();
            map.put(item.getCode(), Lists.newArrayList());
            if (CollectionUtils.isEmpty(children)) {
                continue;
            }
            for (ProductCategoryTree child : children) {
                CategoryResult childResult = buildCategory(child, includeCategoryCode);
                map.get(item.getCode()).add(childResult);
                queue.offer(child);
            }
        }
        AtomicInteger counter = new AtomicInteger(0);
        convertChild(result, map, counter);
        return result;
    }

    private CategoryResult buildCategory(ProductCategoryTree root, boolean includeCategoryCode) {
        CategoryResult result = CategoryResult.builder().id(root.getId()).name(root.getName()).code(root.getCode()).orderField(root.getOrderField()).children(Lists.newArrayList()).build();
        if (includeCategoryCode) {
            result.setCategoryCode(root.getCategoryCode());
        }
        return result;
    }

    private void convertChild(CategoryResult result, Map<String, List<CategoryResult>> map, AtomicInteger counter) {
        counter.incrementAndGet();
        result.setChildren(map.get(result.getCode()));
        List<CategoryResult> children = result.getChildren();
        if (CollectionUtils.isNotEmpty(children) && counter.get() <= map.keySet().size()) {
            for (CategoryResult child : children) {
                convertChild(child, map, counter);
            }
        }
    }


    @ServiceMethod("update")
    public ProductCategoryObject.Result update(ServiceContext context, ObjectDataDocument objectDataDocument) throws MetadataServiceException {
        if (!productCategoryUtils.isCloseOldProductCategory(context.getTenantId())) {
            updateOldCategory(context, objectDataDocument);
        } else {
            ActionContext actionContext = new ActionContext(context.getRequestContext(), ProductCategoryModel.Metadata.API_NAME, SystemConstants.ActionCode.Edit.getActionCode());
            BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
            arg.setObjectData(objectDataDocument);
            BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
            optionInfo.setIsDuplicateSearch(false);
            optionInfo.setUseValidationRule(false);
            optionInfo.setSkipFuncValidate(true);
            arg.setOptionInfo(optionInfo);
            arg.setOptionInfo(optionInfo);
            serviceFacadeProxy.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
        }
        List<IObjectData> dataList = productCategoryUtils.findAllSortedCategory(context.getUser()).getData();
        return ProductCategoryObject.Result.builder().result(true).data(getObjectDataDocumentList(dataList)).build();
    }

    public List<ObjectDataDocument> getObjectDataDocumentList(List<IObjectData> dataList) {
        return ObjectDataDocument.ofList(dataList);
    }

    public void updateOldCategory(ServiceContext context, ObjectDataDocument objectDataDocument) {
        StopWatch stopWatch = StopWatch.create("productCategoryUpdate");
        List<IObjectData> allCategory = productCategoryUtils.findAllCategory(context.getUser()).getData();
        stopWatch.lap("find-all-category");
        Optional<IObjectData> any = allCategory.stream().filter(x -> x.getId().equals(objectDataDocument.getId())
                        && x.getName().equals(objectDataDocument.toObjectData().getName())
                        && x.get(CATEGORY_CODE).equals(objectDataDocument.toObjectData().get(CATEGORY_CODE))
                        && x.get(ORDER_FIELD).equals(objectDataDocument.toObjectData().get(ORDER_FIELD))
                        && Optional.ofNullable(x.get(PID)).orElse("").toString().equals(Optional.ofNullable(objectDataDocument.toObjectData().get(PID)).orElse("").toString()))
                .findAny();
        if (any.isPresent()) {
            stopWatch.lap("find-all-sorted-category");
            stopWatch.logSlow(1000);
            return;
        }
        List<IObjectData> categoryExceptSelf = allCategory.stream().filter(a -> !a.getId().equals(objectDataDocument.getId())).collect(Collectors.toList());
        Object orderField = objectDataDocument.get(ORDER_FIELD);
        if (orderField == null) {
            IObjectData originalData = metaDataFindServiceExt.findObjectData(context.getUser(), objectDataDocument.getId(), "ProductCategoryObj");
            if (originalData == null) {
                throw new ValidateException(categoryStaticUtilService.text(SO_DATA_INVALID_DELETED));
            }
            objectDataDocument.toObjectData().set(ORDER_FIELD, originalData.get(ORDER_FIELD));
        }
        savaCategoryCheckAsync(context.getUser(), "Update", objectDataDocument, categoryExceptSelf);
        Object pid = objectDataDocument.get(PID);
        serviceFacadeProxy.updateObjectData(context.getUser(), objectDataDocument.toObjectData());
        stopWatch.lap("update-object-data");
        if (Objects.nonNull(orderField)) {
            productCategoryBizService.bulkUpdateOrderField(context.getUser(), orderField.toString(), pid, objectDataDocument.getId());
        }
        stopWatch.lap("bulk-update-order-field");
        productCategoryBizService.sendSynchronizeDescribeMqByGray(context.getUser(), "update");
        stopWatch.lap("synchronized-describe");
        stopWatch.logSlow(1000);
    }

    @ServiceMethod("delete")
    public ProductCategoryObject.Result delete(ServiceContext context, ProductCategoryObject.Arg arg) throws MetadataServiceException {
        String code = arg.getCode();
        if (Strings.isNullOrEmpty(code)) {
            List<IObjectData> objectDataByIds = serviceFacadeProxy.findObjectDataByIds(context.getTenantId(), Lists.newArrayList(arg.getId()), API_NAME);
            if (CollectionUtils.isEmpty(objectDataByIds)) {
                return ProductCategoryObject.Result.builder().result(true).build();
            }
            code = objectDataByIds.get(0).get(CODE).toString();
        }
        Set<String> categoryChildren = productCategoryUtils.findCategoryChildren(context.getUser(), code);
        CheckDeleteCategoryModel.Result result = checkDeleteCategory(new CheckDeleteCategoryModel.Arg(Lists.newArrayList(categoryChildren)), context);
        if (!result.getResult()) {
            throw new ValidateException(categoryStaticUtilService.text(SO_CHECK_CATEGORY_DELETE));
        }
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(categoryChildren);
        QueryResult<IObjectData> bySearchQuery = metaDataFindServiceExt.findBySearchQueryIgnoreAll(context.getUser(), API_NAME, searchTemplateQuery);
        serviceFacadeProxy.bulkInvalidAndDeleteWithSuperPrivilege(bySearchQuery.getData(), context.getUser());
        productCategoryBizService.sendSynchronizeDescribeMqByGray(context.getUser(), "delete");
        return ProductCategoryObject.Result.builder().result(true).build();
    }

    public SearchTemplateQuery getSearchTemplateQuery(Set<String> categoryChildren) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(categoryChildren.size());
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), CODE, categoryChildren);
        return searchTemplateQuery;
    }

    public CheckDeleteCategoryModel.Result checkDeleteCategory(CheckDeleteCategoryModel.Arg arg, ServiceContext context) {
        return spuSkuService.checkDeleteCategory(arg, context);
    }

    @ServiceMethod("tree_list")
    public CategoryObject treeList(ServiceContext context, @Nullable TreeArg arg) {
        Boolean filterByShopCategory = arg == null ? Boolean.FALSE : arg.getFilterByShopCategory();
        CategoryFilterEnum categoryFilter = null;
        if (arg != null && Boolean.TRUE.equals(arg.getFilterByShopCategory())) {
            categoryFilter = CategoryFilterEnum.SHOP;
        } else if (arg != null && Boolean.FALSE.equals(arg.getFilterByShopCategory())) {
            categoryFilter = CategoryFilterEnum.PRODUCT;
        }
        if (!categoryStaticUtilService.isDhtOrAccessoriesMallRequest(context.getPeerName(), context.getAppId())) {
            return productCategoryValidator.getCategoryTreeList(context.getUser(), false, categoryFilter);
        }
        return getCategoryForCPQ(context, categoryFilter, filterByShopCategory);
    }

    public CategoryObject getCategoryForCPQ(ServiceContext context, CategoryFilterEnum categoryFilter, Boolean filterByShopCategory) {
        String categoryByPriceBookIdSql;
        QueryResult<IObjectData> productCategoryObj = productCategoryUtils.findAllSortedCategory(context.getUser(), Lists.newArrayList(), false, categoryFilter);
        CategoryObject categoryTreeList = productCategoryValidator.buildCategoryTree(productCategoryObj.getData());
        if (Boolean.TRUE.equals(filterByShopCategory)) {
            categoryByPriceBookIdSql = categoryStaticUtilService.getUsedShopCategoryCode(context.getTenantId());
        } else {
            categoryByPriceBookIdSql = categoryStaticUtilService.getUsedCategoryCode(context.getTenantId());
        }
        productCategoryBizService.handleCategoryForCPQ(context, productCategoryObj.getData(), categoryTreeList, categoryByPriceBookIdSql, filterByShopCategory);
        return categoryTreeList;
    }

    @ServiceMethod("synchronize")
    public CrmResult synchronizeCategory(List<String> tenantIds) {
        CrmResult crmResult = new CrmResult();
        crmResult.setResult("ok");
        return crmResult;
    }

    @ServiceMethod("sync_describe")
    public Boolean syncDescribe(ServiceContext context) {
        try {
            productCategoryBizService.asyncSyncDescribe(context.getUser(), 5);
        } catch (Exception e) {
            log.error("sync describe error, tenant:{}", context.getTenantId(), e);
            return false;
        }
        return true;
    }

    @ServiceMethod("filter_by_price_book")
    public CategoryTree filterByPriceBook(ServiceContext context, GetCategoryByPriceBookArg arg) {
        Set<String> availableProductIds = Sets.newHashSet();
        if (categoryStaticUtilService.supportFilterCategory(context.getTenantId()) && bizConfigThreadLocalCacheService.isAvailableRangeEnabled(context.getTenantId())) {
            if (Strings.isNullOrEmpty(arg.getAccountId())) {
                return CategoryTree.builder().result(Lists.newArrayList()).build();
            }
            Map<String, List<IObjectData>> detailDataList = Maps.newHashMap();
            dhtPriceBookService.detailDataDocument2detailData(arg.getDetailDataList(), detailDataList);
            Tuple<Boolean, Set<String>> availableTuple = dhtPriceBookService.matchAvailableProduct(context, arg.getAccountId(), arg.getPriceBookId(),
                    arg.getAvailableRangeId(), null == arg.getMasterData() ? null : arg.getMasterData().toObjectData(), detailDataList);
            if (!availableTuple.getKey()) {
                return CategoryTree.builder().result(Lists.newArrayList()).build();
            }
            availableProductIds.addAll(availableTuple.getValue());
        }
        String sql = productCategoryBizService.getCategoryCodeSQL(context.getUser(), availableProductIds, arg.getPriceBookId(), arg.getFilterByShopCategory(), arg.isIgnoreProductStatus());
        if (Strings.isNullOrEmpty(sql)) {
            return CategoryTree.builder().result(Lists.newArrayList()).build();
        }
        CategoryObject categoryTreeList;
        List<IObjectData> productCategoryDataOfDB = Lists.newArrayList();
        if (Boolean.TRUE.equals(arg.getFilterByShopCategory())) {
            QueryResult<IObjectData> productCategoryObj = productCategoryUtils.findAllSortedCategory(context.getUser(), Lists.newArrayList(), false, CategoryFilterEnum.SHOP);
            categoryTreeList = productCategoryValidator.buildCategoryTree(productCategoryObj.getData());
            productCategoryDataOfDB = productCategoryObj.getData();
        } else {
            categoryTreeList = productCategoryValidator.getCategoryTreeList(context.getUser());
        }
        productCategoryBizService.handleCategoryForCPQ(context, productCategoryDataOfDB, categoryTreeList, sql, arg.getFilterByShopCategory());
        return CategoryTree.builder().result(categoryTreeList.getChildren()).build();
    }

    @ServiceMethod("category_nodeList")
    public ProductCategoryObject.CategoryListResult categoryNodeList(ServiceContext serviceContext, ProductCategoryObject.CategoryArg arg) {
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getQueryInfo());
        return productCategoryBizService.categoryNodeList(serviceContext.getUser(), searchTemplateQuery);
    }

    @ServiceMethod("have_child_node")
    public ProductCategoryObject.Result havaChildNode(ServiceContext serviceContext, ProductCategoryObject.CheckChildArg arg) {
        List<String> objectIdList = arg.getObjectIdList();
        if (CollectionUtils.isEmpty(objectIdList)) {
            log.warn("have_child_node param is empty!, tenant:{}", serviceContext.getTenantId());
            return ProductCategoryObject.Result.builder().result(true).build();
        }
        boolean havaChildNode = productCategoryBizService.havaChildNode(serviceContext.getUser(), objectIdList);
        return ProductCategoryObject.Result.builder().result(havaChildNode).build();
    }


    @ServiceMethod("drag_order")
    public ProductCategoryObject.ListResult dragOrder(ServiceContext serviceContext, ObjectDataDocument objectDataDocument) {
        if (!productCategoryUtils.isCloseOldProductCategory(serviceContext.getTenantId())) {
            throw new ValidateException(categoryStaticUtilService.text(TENANT_NOT_GRAY_THIS_ABILITY));
        }
        IObjectData newData = objectDataDocument.toObjectData();
        String id = newData.getId();
        IObjectData originalData = metaDataFindServiceExt.findObjectData(serviceContext.getUser(), id, ProductCategoryModel.Metadata.API_NAME);
        String newPid = getString(newData, PID, "");
        String originalPid = getString(originalData, PID, "");
        String newOrderField = getString(newData, ORDER_FIELD, "");
        String originalOrderField = getString(originalData, ORDER_FIELD, "");
        boolean needUpdateDragData = needUpdateDragData(newPid, originalPid, newOrderField, originalOrderField);
        if (needUpdateDragData && productCategoryV2Validator.passParamCheck(serviceContext.getUser(), newData)) {
            IObjectDescribe categoryDescribe = describeEnhancer.fetchObject(serviceContext.getUser(), SFAPreDefineObject.ProductCategory.getApiName());
            productCategoryV2Validator.checkCategoryIsRelated(serviceContext.getUser(), newPid, categoryDescribe);
            ProductCategoryServiceResource productCategoryServiceResource = getProxyProductCategoryServiceResource();
            productCategoryServiceResource.updateDragData(serviceContext, originalData, newPid, newOrderField, newData);
        }
        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
        List<IFilter> filters = templateQuery.getFilters();
        if (StringUtils.isBlank(newPid)) {
            SearchUtil.fillFilterIsNull(filters, PID);
        } else {
            SearchUtil.fillFilterEq(filters, PID, newPid);
        }
        templateQuery.setLimit(0);
        List<IObjectData> dataList = metaDataFindServiceExt.findBySearchQueryWithFields(serviceContext.getUser(), ProductCategoryModel.Metadata.API_NAME, templateQuery, Lists.newArrayList(DBRecord.ID, NAME, ORDER_FIELD, "version", CODE, CATEGORY_CODE), true).getData();
        return ProductCategoryObject.ListResult.builder().result(ObjectDataDocument.ofList(dataList)).build();
    }

    public ProductCategoryServiceResource getProxyProductCategoryServiceResource() {
        return (ProductCategoryServiceResource) AopContext.currentProxy();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDragData(ServiceContext serviceContext, IObjectData originalData, String newPid, String newOrderField, IObjectData newData) {
        originalData.set(PID, newPid);
        originalData.set(ORDER_FIELD, newOrderField);
        serviceFacadeProxy.batchUpdateByFields(serviceContext.getUser(), Lists.newArrayList(originalData), Lists.newArrayList(PID, ORDER_FIELD));
        String orderField = getString(newData, ProductCategoryModel.Filed.ORDER_FIELD, "1");
        String pid = getString(newData, ProductCategoryModel.Filed.PID, "");
        productCategoryBizService.bulkUpdateOrderField(serviceContext.getUser(), orderField, pid, newData.getId());
        productCategoryBizService.changePathByParentId(serviceContext.getUser(), newData.getId(), pid);
        productCategoryBizService.sendSynchronizeDescribeMq(serviceContext.getUser());
    }

    public String getString(IObjectData newData, String key, String defaultValue) {
        return ObjectDataUtils.getValueOrDefault(newData, key, defaultValue);
    }

    public boolean needUpdateDragData(String newPid, String originalPid, String newOrderField, String originalOrderField) {
        return !newPid.equals(originalPid) || !newOrderField.equals(originalOrderField);
    }

    @ServiceMethod("gray_product_category")
    public ProductCategoryObject.GrayCategoryResult categoryProductCategory(ServiceContext serviceContext, List<String> tenantIdList) {
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return GrayCategoryResult.builder().success(false).message("tenantIdList is empty").build();
        }
        if (tenantIdList.size() > 5) {
            return GrayCategoryResult.builder().success(false).message("tenantIdList Can't be greater than 5").build();
        }
        if (!categoryStaticUtilService.allowGrayProductCategory()) {
            return GrayCategoryResult.builder().success(false).message("category gray service circuit breaker!").build();
        }
        List<String> needGrayCategoryTenant = Lists.newArrayList();
        for (String tenantId : tenantIdList) {
            if (!productCategoryUtils.isCloseOldProductCategory(tenantId)) {
                needGrayCategoryTenant.add(tenantId);
            }
        }
        if (CollectionUtils.isEmpty(needGrayCategoryTenant)) {
            return GrayCategoryResult.builder().success(false).message("needGrayCategoryTenant is empty").build();
        }
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> grayCategory(needGrayCategoryTenant));
            task.run();
        } catch (Exception e) {
            log.error("gray_product_category error, tenant:{}", needGrayCategoryTenant, e);
            return GrayCategoryResult.builder().success(false).message("category gray error").build();
        }
        return GrayCategoryResult.builder().success(true).message("category gray service is background running").build();
    }

    private void grayCategory(List<String> tenantIdList) {
        crmMetaDataServiceProxy.grayProductCategory(tenantIdList);
    }

    @ServiceMethod("send_gray_category_object_mq")
    public ProductCategoryObject.GrayCategoryResult sendGrayCategoryObjectMq(ServiceContext serviceContext) {
        if (StringUtils.isBlank(serviceContext.getTenantId())) {
            return GrayCategoryResult.builder().success(false).message("分类灰度成功后发送 MQ，但是 tenantId 为空").build();//ignoreI18n
        }
        try {
            productCategoryBizService.sendGrayCategoryObjectMq(serviceContext.getUser());
            return GrayCategoryResult.builder().success(true).build();
        } catch (Exception e) {
            log.error("发送产品分类对象化成功 MQ 失败, tenant:{}", serviceContext.getTenantId(), e);
            return GrayCategoryResult.builder().success(false).message("发送分类灰度 MQ 失败").build();//ignoreI18n
        }
    }
}