package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObjectList;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ObjectImportInitProvider;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Optional;

public class AccountFinInfoImportObjectController extends StandardImportObjectController {

    @Override
    protected GetImportObjectList.ImportObjectDTO getImportObject(String objectCode) {
        GetImportObjectList.ImportObjectDTO result = super.getImportObject(objectCode);
        if (GrayUtil.isAccountFinInfoImportWorkflow(RequestContextManager.getContext().getTenantId())) {
            result.setIsOpenWorkFlow(true);
        }
        return result;
        }
}
