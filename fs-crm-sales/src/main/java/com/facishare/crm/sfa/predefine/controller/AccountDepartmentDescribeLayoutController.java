package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lik
 * @date : 2024/7/11 9:59
 */
@Slf4j
public class AccountDepartmentDescribeLayoutController extends SFADescribeLayoutController {
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout (arg, result);
        ILayout layout = new Layout(result.getLayout());
        LayoutExt layoutExt = LayoutExt.of(layout);
        layoutExt.removeFields(Lists.newArrayList("relation_template_id"));
    }
}
