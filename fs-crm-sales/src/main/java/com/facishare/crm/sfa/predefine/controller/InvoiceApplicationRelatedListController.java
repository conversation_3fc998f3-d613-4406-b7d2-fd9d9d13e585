package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Created by renlb on 2018/11/28.
 */
public class InvoiceApplicationRelatedListController extends SFARelatedListController {
    private final InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);
    private boolean isReturnEmpty = false;
    private boolean isSalesOrderMulti = false;

    @Override
    protected void modifyQueryByRefFieldName(SearchTemplateQuery query) {
        // 使用salesOrderId 查询出来 开票明细,返回 开票id列表,然后查询
        if (Objects.equals("sales_order_invoice_app_multi_related_list", arg.getRelatedListName())) {
            isSalesOrderMulti = true;

            List<String> invoiceIds = invoiceService.findInvoiceAppIdBySalesOrderId(controllerContext.getTenantId(), arg.getTargetObjectDataId());
            if (CollectionUtils.isNotEmpty(invoiceIds)) {
                SearchTemplateQueryExt.of(query).addFilter(Operator.IN, DBRecord.ID, invoiceIds);
            } else {
                isReturnEmpty = true;
            }
        } else {
            super.modifyQueryByRefFieldName(query);
        }
    }


    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        if (isReturnEmpty) {
            return new QueryResult();
        }
        return super.findData(query);
    }

    @Override
    protected List<IButton> getButtons(ILayout layout) {
        if (isSalesOrderMulti) {
            return Collections.EMPTY_LIST;
        }
        return super.getButtons(layout);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if(result.getLayout() != null){
            invoiceService.removeCloneButtonOnOpenInvoiceLine(controllerContext.getUser(), new Layout(result.getLayout()));
        }
        return super.after(arg, result);
    }
}
