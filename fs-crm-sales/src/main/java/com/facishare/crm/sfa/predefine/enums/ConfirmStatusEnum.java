package com.facishare.crm.sfa.predefine.enums;

/**
 * Created by renlb on 2019/6/26.
 */
public enum ConfirmStatusEnum {
    CONFIRM(3, "确认"),
    REJECT(4, "驳回"),
    RECALL(5, "撤回");


    private final Integer value;
    private final String desc;

    ConfirmStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return desc;
    }
}
