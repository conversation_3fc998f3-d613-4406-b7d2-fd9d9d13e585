package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AttributeValueWebDetailController extends StandardWebDetailController {

    private List<String> removeActionList = Lists.newArrayList(ObjectAction.UPDATE.getActionCode());

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(x -> removeActionList.contains(x.getAction()));
        layout.setButtons(buttons);
        WebDetailLayout.of(layout).removeButtonsByActionCode(removeActionList);
        return layout;
    }
}
