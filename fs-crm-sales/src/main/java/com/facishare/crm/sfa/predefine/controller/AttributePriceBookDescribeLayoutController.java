package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.AttributePriceBookConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
public class AttributePriceBookDescribeLayoutController extends SFADescribeLayoutController {

    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (bizConfigThreadLocalCacheService.isOpenIncrementalPricing(controllerContext.getTenantId())
                && LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type()) && formComponent != null) {
            //属性定价方式改成只读
            PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, Lists.newArrayList(AttributePriceBookConstants.AttributePriceBookField.PRICING_MODE));
        }
    }
}
