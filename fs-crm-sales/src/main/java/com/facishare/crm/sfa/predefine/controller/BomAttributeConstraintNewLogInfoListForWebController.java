package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.model.BomConstraintLineModel;
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

public class BomAttributeConstraintNewLogInfoListForWebController extends StandardNewLogInfoListForWebController {

    private static final List<String> fields = Lists.newArrayList("condition_range", "result_range");

    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        List<LogInfo.DiffObjectData> objectDataList = logRecord.getObjectData();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return logRecord;
        }

        objectDataList.stream().filter(x -> fields.contains(x.getFieldApiName())).forEach(x -> {
            String fieldApiName = x.getFieldApiName();
            handleData(x, x.getValue().get(fieldApiName),true);
            handleData(x, x.getOldValue().get(fieldApiName),false);
        });
        logRecord.setObjectData(objectDataList);
        return logRecord;
    }

    private void handleData(LogInfo.DiffObjectData x, Object obj,boolean flag) {
        if (Objects.nonNull(obj)) {
            List<BomConstraintLineModel> bomConstraint = JSON.parseArray(obj.toString(), BomConstraintLineModel.class);
            if (CollectionUtils.isNotEmpty(bomConstraint)) {
                String str = ProductConstraintUtil.constraintProcess(bomConstraint);
                if(flag){
                    x.getValue().put(x.getFieldApiName(), str);
                }else{
                    x.getOldValue().put(x.getFieldApiName(), str);
                }
            }
        }
    }

}

