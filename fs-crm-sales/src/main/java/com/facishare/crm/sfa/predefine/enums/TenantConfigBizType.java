package com.facishare.crm.sfa.predefine.enums;

public enum TenantConfigBizType {

	LEADS_TRANSFER("leads_transfer", "LeadsObj","线索转换后台配置"),

	;

	private String bizType;
	private String objectApiName;
	private String desc;
	TenantConfigBizType(String bizType, String objectApiName, String desc) {
		this.objectApiName = objectApiName;
		this.bizType = bizType;
		this.desc = desc;
	}

	public String getBizType() {
		return bizType;
	}

	public void setBizType(String bizType) {
		this.bizType = bizType;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getObjectApiName() {
		return objectApiName;
	}

	public void setObjectApiName(String objectApiName) {
		this.objectApiName = objectApiName;
	}
}
