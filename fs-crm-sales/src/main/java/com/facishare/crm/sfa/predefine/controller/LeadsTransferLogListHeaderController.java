package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.Safes;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class LeadsTransferLogListHeaderController extends StandardListHeaderController {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected StandardListHeaderController.Result after(StandardListHeaderController.Arg arg, StandardListHeaderController.Result result) {
        StandardListHeaderController.Result res = super.after(arg, result);
        removeFields(res, Lists.newArrayList("name", "owner", "leads_name", "company",
                "owner_department", "owner_department", "account_life_status", "record_type", "out_owner", "data_own_department", "last_modified_time", "last_modified_by"));
        return res;
    }
    // @Override
    // protected ObjectDescribeDocument buildDescribeExt() {
    //     if (!bizConfigThreadLocalCacheService.isPartnerEnabled(controllerContext.getTenantId())) {
    //         List<IFieldDescribe> fieldDescribes = objectDescribeExt.getFieldDescribes();
    //         fieldDescribes.removeIf(f -> "partner_id".equals(f.getApiName()));
    //         objectDescribeExt.setFieldDescribes(fieldDescribes);
    //     }
    //     return super.buildDescribeExt();
    // }

    // @Override
    // protected ObjectDescribeDocument buildDescribeExt() {
    //     if (!bizConfigThreadLocalCacheService.isPartnerEnabled(controllerContext.getTenantId())) {
    //         List<IFieldDescribe> fieldDescribes = objectDescribeExt.getFieldDescribes();
    //         fieldDescribes.removeIf(f -> "partner_id".equals(f.getApiName()));
    //         objectDescribeExt.setFieldDescribes(fieldDescribes);
    //     }
    //     return super.buildDescribeExt();
    // }

    private void removeFields(Result result, List<String> fieldList) {
        List<DocumentBaseEntity> fields = result.getFieldList();
        if (CollectionUtils.notEmpty(fields)) {
            fields.removeIf(f -> {
                for (String field : fieldList) {
                    if (f.containsKey(field)) {
                        return true;
                    } else if (f.containsKey("partner_id") && !bizConfigThreadLocalCacheService.isPartnerEnabled(controllerContext.getTenantId())) {
                        return true;
                    }
                }
                return false;
            });
            List<DocumentBaseEntity> frontFields = fields.stream().
                    filter(f -> !f.containsKey("is_account_main_leads") && !f.containsKey("is_contact_main_leads")
                            && !f.containsKey("created_by") && !f.containsKey("create_time") &&
                            !f.containsKey("account_id") && !f.containsKey("contact_id")
                    ).collect(Collectors.toList());
            List<DocumentBaseEntity> midFields = fields.stream().
                    filter(f -> f.containsKey("account_id") || f.containsKey("is_account_main_leads") || f.containsKey("contact_id")
                            || f.containsKey("is_contact_main_leads")).collect(Collectors.toList());
            List<DocumentBaseEntity> endFields = fields.stream().
                    filter(f -> f.containsKey("created_by") || f.containsKey("create_time")).collect(Collectors.toList());
            fields.clear();
            fields.addAll(frontFields);
            fields.addAll(midFields);
            fields.addAll(endFields);

            List<IFieldDescribe> fieldDescribes = result.getObjectDescribe().toObjectDescribe().getFieldDescribes();
            Optional<IFieldDescribe> opField = fieldDescribes.stream().filter(f -> "created_by".equals(f.getApiName())).findFirst();
            if (opField.isPresent()) {
                //转换者
                opField.get().setLabel(I18N.text(SFAI18NKeyUtil.SFA_CONVERTER));
            }
            opField = fieldDescribes.stream().filter(f -> "create_time".equals(f.getApiName())).findFirst();
            if (opField.isPresent()) {
                //转换时间
                opField.get().setLabel(I18N.text("sfa.ContactBusiness.2957.3"));
            }
        }
        LayoutExt layoutExt = LayoutExt.of(result.getLayout().toLayout());
        List<FormComponentExt> formComponents = layoutExt.getFormComponents();
        for (FormComponentExt component : Safes.of(formComponents)) {
            List<IFieldSection> fieldSections = component.getFieldSections();
            handleFieldList(fieldList, fieldSections);
        }
        Optional<TableComponent> tableComponentOpt = layoutExt.getTableComponent();
        tableComponentOpt.ifPresent(tableComponent -> {
            List<IFieldSection> fieldSections = tableComponent.getFieldSections();
            handleFieldList(fieldList, fieldSections);
        });
    }

    private void handleFieldList(List<String> fieldList, List<IFieldSection> fieldSections) {
        List<IFormField> changeFields, formFields;
        changeFields = Lists.newArrayList();
        for (IFieldSection fieldSection : fieldSections) {
            formFields = fieldSection.getFields();
            if ("base_field_section__c".equals(fieldSection.getName())) {
                changeFields = formFields.stream().filter(f -> "is_account_main_leads".equals(f.getFieldName())
                        || "is_contact_main_leads".equals(f.getFieldName())).collect(Collectors.toList());
            }
            formFields.removeIf(f -> {
                for (String field : fieldList) {
                    if (f.getFieldName().equals(field)) {
                        return true;
                    }
                    if ("base_field_section__c".equals(fieldSection.getName()) &&
                            ("is_account_main_leads".equals(f.getFieldName()) || "is_contact_main_leads".equals(f.getFieldName()))) {
                        return true;
                    }
                }
                return false;
            });
            if ("leads_fields_section__c".equals(fieldSection.getName())) {
                formFields.addAll(changeFields);
            }
            fieldSection.setFields(formFields);
        }
    }
}
