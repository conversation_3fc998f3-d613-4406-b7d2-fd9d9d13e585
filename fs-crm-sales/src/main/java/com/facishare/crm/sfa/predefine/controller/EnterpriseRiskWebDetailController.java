package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.MemberService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.Map;

public class EnterpriseRiskWebDetailController extends StandardWebDetailController {

    private static final MemberService memberService = SpringUtil.getContext().getBean(MemberService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataDocument document = result.getData();
        String tenantId = controllerContext.getTenantId();

        Map<String, String> describeNameMap = serviceFacade.findDisplayNameByApiNames(tenantId, Lists.newArrayList("AccountObj", "PartnerObj", "SupplierObj"));
        String descApiName = (String) document.get("refer_to_obj");
        document.put("refer_to_obj_name", describeNameMap.getOrDefault(descApiName, descApiName));

        memberService.setMemberName(tenantId, Lists.newArrayList(document), "member_list", "member_list_name");
        return result;
    }
}
