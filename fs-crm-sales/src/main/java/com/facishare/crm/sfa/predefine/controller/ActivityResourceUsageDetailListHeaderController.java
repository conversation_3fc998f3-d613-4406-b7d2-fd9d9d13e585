package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

import static com.facishare.crm.sfa.utilities.constant.ActivityResourceUsageDetailConstants.*;

@EqualsAndHashCode(callSuper = true)
public class ActivityResourceUsageDetailListHeaderController extends StandardListHeaderController {
    private Scene scene;

    @Override
    protected void before(StandardListHeaderController.Arg arg) {
        super.before(arg);
        if (arg.getExtraParams() != null) {
            JSONObject extraParams = new JSONObject(arg.getExtraParams());
            Scene.from(extraParams.getString(PREDEFINE_SCENE)).ifPresent(scene -> this.scene = scene);
        }
    }

    @Override
    protected List<String> getAuthorizedFields() {
        if (Scene.USER == scene) {
            List<String> list = new ArrayList<>();
            list.add(USER_ID); // 用户
            list.add(SUM_RESOURCE_USAGE); // 使用时长 - 资源消耗小计
            list.add(USAGE_PERCENTAGE); // 占使用的百分比 - 资源消耗占比
            return list;
        }
        if (Scene.LIST == scene) {
            List<String> list = new ArrayList<>();
            list.add(USER_ID); // 用户
            list.add(RESOURCE_USAGE); // 使用时长 - 资源消耗
            list.add(BIZ_DATA); // 销售记录 - 业务数据
            return list;
        }
        if (Scene.TOTAL == scene) {
            List<String> list = new ArrayList<>();
            list.add(SUM_RESOURCE_USAGE); // 总时长 - 资源消耗小计
            list.add(RESOURCE_USAGE); // 使用时长 - 资源消耗
            list.add(USAGE_PERCENTAGE); // 使用百分比 - 资源消耗占比
            return list;
        }
        return super.getAuthorizedFields();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        after.setViewInfo(null);
        if (Scene.USER == scene) {
            IObjectDescribe describe = after.getObjectDescribe().toObjectDescribe().copy();
            describe.getFieldDescribe(SUM_RESOURCE_USAGE).setLabel(I18N.text("sfa.ActivityResourceUsageDetailObj.usage.hours.label"));  // 使用时长 - 资源消耗小计
            describe.getFieldDescribe(USAGE_PERCENTAGE).setLabel(I18N.text("sfa.ActivityResourceUsageDetailObj.user.usage.percent.label")); // 占使用的百分比 - 资源消耗占比
            after.setObjectDescribe(ObjectDescribeDocument.of(describe));
        }
        if (Scene.LIST == scene) {
            IObjectDescribe describe = after.getObjectDescribe().toObjectDescribe().copy();
            describe.getFieldDescribe(RESOURCE_USAGE).setLabel(I18N.text("sfa.ActivityResourceUsageDetailObj.usage.hours.label"));  // 使用时长 - 资源消耗小计
            describe.getFieldDescribe(BIZ_DATA).setLabel(I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Utils.ACTIVE_RECORD_API_NAME))); // 销售记录 - 业务数据
            after.setObjectDescribe(ObjectDescribeDocument.of(describe));
        }
        if (Scene.TOTAL == scene) {
            IObjectDescribe describe = after.getObjectDescribe().toObjectDescribe().copy();
            describe.getFieldDescribe(SUM_RESOURCE_USAGE).setLabel(I18N.text("sfa.ActivityResourceUsageDetailObj.total.usage.hours.label"));  // 总时长 - 资源消耗小计
            describe.getFieldDescribe(RESOURCE_USAGE).setLabel(I18N.text("sfa.ActivityResourceUsageDetailObj.usage.hours.label")); // 使用时长 - 资源消耗
            describe.getFieldDescribe(USAGE_PERCENTAGE).setLabel(I18N.text("sfa.ActivityResourceUsageDetailObj.total.usage.percent.label")); // 使用百分比 - 资源消耗占比
            after.setObjectDescribe(ObjectDescribeDocument.of(describe));
        }
        return after;
    }
}