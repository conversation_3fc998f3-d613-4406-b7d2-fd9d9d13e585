package com.facishare.crm.sfa.predefine.enums;

/**
 * <AUTHOR> gongchun<PERSON>
 * @date : 2024/4/22 20:53
 * @description:
 */
public enum StockTypeEnum {
    UNLIMITED(-1, "不限"),
    NON_H_STOCK(0, "非H股"),
    RED_CHIP_STOCK(1, "红筹股"),
    SHANGHAI_MAIN_BOARD_A_SHARES(2, "上交所主板A股"),
    SHENZHEN_MAIN_BOARD_A_SHARES(3, "深交所主板A股"),
    SHENZHEN_GEM_A_SHARES(4, "深交所创业板A股"),
    NEW_THIRD_BOARD(5, "新三板"),
    H_STOCK(6, "H股"),
    SHANGHAI_SCIENCE_AND_TECHNOLOGY_BOARD_A_SHARES(7, "上交所科创板A股"),
    SHANGHAI_RISK_WARNING_BOARD_A_SHARES(8, "上交所风险警示板A股"),
    SHENZHEN_RISK_WARNING_BOARD_A_SHARES(9, "深交所风险警示板A股"),
    BEIJING_STOCK_EXCHANGE_A_SHARES(10, "北京证券交易所A股"),
    JOINT_STOCK(11, "合订证券"),
    HONGKONG_STOCK(12, "港股"),
    DEPOSITORY_RECEIPTS(13, "预托证券");

    private Integer value;
    private String label;

    StockTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static StockTypeEnum fromValue(Integer value) {
        for (StockTypeEnum stockTypeEnum : values()) {
            if (stockTypeEnum.value.equals(value)) {
                return stockTypeEnum;
            }
        }
        return null;
    }

}
