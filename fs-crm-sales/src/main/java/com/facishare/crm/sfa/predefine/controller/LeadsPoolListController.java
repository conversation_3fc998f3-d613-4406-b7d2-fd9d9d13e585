package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.PoolEmptyRule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;
import java.util.stream.Collectors;

public class LeadsPoolListController extends StandardListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        query.setDataRightsParameter(null);
        return query;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        List<ObjectDataDocument> documents = rst.getDataList();
        List<IObjectData> dataList = documents.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        PoolEmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(dataList).build();
        emptyRule.fillEmptyRule();
        List<ObjectDataDocument> newDocuments = ObjectDataDocument.ofList(dataList);
        rst.setDataList(newDocuments);
        return rst;
    }
}
