package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.LogInfoRecordService;
import com.facishare.crm.sfa.utilities.util.InteractionStrategyNewLogInfoUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetNewLogInfoListForMod;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * InteractionStrategyNewLogInfoListForMobController
 *
 * <AUTHOR>
 */
public class InteractionStrategyNewLogInfoListForMobController extends StandardNewLogInfoListForMobController {
    private static final LogInfoRecordService LOG_INFO_RECORD_SERVICE = SpringUtil.getContext().getBean(LogInfoRecordService.class);

    @Override
    protected GetNewLogInfoListForMod.Result doService(GetNewLogInfoListForMod.Arg arg) {
        GetNewLogInfoListForMod.Result result = super.doService(arg);
        List<LogRecord> recordList = result.getModifyRecordList();
        LOG_INFO_RECORD_SERVICE.renderRecordList(recordList, controllerContext.getUser(), arg.getApiName(), arg.getObjectId(), InteractionStrategyNewLogInfoUtil.consumer);
        return result;
    }
}