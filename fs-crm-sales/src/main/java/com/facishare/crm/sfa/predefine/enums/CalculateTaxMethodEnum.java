package com.facishare.crm.sfa.predefine.enums;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum CalculateTaxMethodEnum {
    Tax_With_The_Price("taxWithThePrice", "价内税"), //默认
    Off_Price_Tax("offPriceTax", "价外税");
    ;
    private final String value;
    private final String label;

    CalculateTaxMethodEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static CalculateTaxMethodEnum getByCode(String status) {
        for (CalculateTaxMethodEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static List<String> getAllStatus() {
        List<String> allStatus = Lists.newArrayList();
        for (CalculateTaxMethodEnum d : CalculateTaxMethodEnum.values()) {
            allStatus.add(d.getValue());
        }
        return allStatus;
    }
}
