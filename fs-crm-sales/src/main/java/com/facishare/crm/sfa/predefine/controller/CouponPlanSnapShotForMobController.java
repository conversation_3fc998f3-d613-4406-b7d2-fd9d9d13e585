package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateManager;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetSnapShotForMod;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

public class CouponPlanSnapShotForMobController extends StandardSnapShotForMobController {

    private final TranslateManager translateManager = SpringUtil.getContext().getBean(TranslateManager.class);

    @Override
    protected GetSnapShotForMod.Result doService(GetSnapShotForMod.Arg arg) {
        GetSnapShotForMod.Result result = super.doService(arg);
        render(result);
        return result;
    }

    private void render(GetSnapShotForMod.Result result) {
        Optional.ofNullable(result)
                .map(GetSnapShotForMod.Result::getData)
                .map(x -> ObjectDataExt.of(x).getObjectData())
                .ifPresent(data ->
                        translateManager.getTranslateService(data.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName(), String.class))
                                .render(data, controllerContext.getUser())
                );
    }
}