package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

/**
 * Created by luohl on 2017/11/14.
 */
@Slf4j
public class LeadsDetailController extends SFADetailController {

    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_720)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        handleObjectDescribe(result);
        handleObjectData(result);
        return result;
    }

    private void handleObjectDescribe(Result result) {
        if (result.getDescribe() == null) {
            return;
        }
        IObjectDescribe objectDescribe = ObjectDescribeExt.of(result.getDescribe().toObjectDescribe()).copyOnWrite();
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        String description = I18N.text("AccountObj.attribute.self.display_name");
        if (Strings.isBlank(description)) {
            description = "客户名称"; // ignoreI18n
        }
        IFieldDescribe fieldDescribe = constructFieldDescribe(description, "AccountObj", "account_id");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("OpportunityObj.attribute.self.display_name");
        if (Strings.isBlank(description)) {
            description = "商机名称"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, "OpportunityObj", "opportunity_id");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("PartnerObj.attribute.self.display_name");
        if (Strings.isBlank(description)) {
            description = "合作伙伴"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, "PartnerObj", "transfer_partner_id");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("ContactObj.attribute.self.display_name");
        if (Strings.isBlank(description)) {
            description = "联系人名称"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, "ContactObj", "contact_id");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("NewOpportunityObj.attribute.self.display_name");
        if (Strings.isBlank(description)) {
            description = "商机2.0名称"; // ignoreI18n
        }
        fieldDescribe = constructFieldDescribe(description, "NewOpportunityObj", "new_opportunity_id");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("OpportunityObj.field.biz_status.label");
        if (Strings.isBlank(description)) {
            description = "商机状态"; // ignoreI18n
        }
        fieldDescribe = constructQuoteFieldDescribe(description, "OpportunityObj", "opportunity_status", "biz_status");
        fieldDescribes.add(fieldDescribe);

        description = I18N.text("NewOpportunityObj.field.sales_status.label");
        if (Strings.isBlank(description)) {
            description = "商机2.0状态"; // ignoreI18n
        }
        fieldDescribe = constructQuoteFieldDescribe(description, "NewOpportunityObj", "new_opportunity_status", "sales_status");
        fieldDescribes.add(fieldDescribe);
        objectDescribe.setFieldDescribes(fieldDescribes);
        result.setDescribe(ObjectDescribeDocument.of(objectDescribe));
    }

    private void handleObjectData(Result result) {
        IObjectData objectData = result.getData().toObjectData();
        if (!LeadsBizStatusEnum.TRANSFORMED.getCode().equals(objectData.get(LeadsConstants.Field.BIZ_STATUS.getApiName(), String.class))) {
            return;
        }
        List<IObjectData> accountDataList = getRelatedObjectDataList(arg.getObjectDataId(), "AccountObj");
        setRelatedObject(objectData, accountDataList, "AccountObj");
        List<IObjectData> contactDataList = getRelatedObjectDataList(arg.getObjectDataId(), "ContactObj");
        setRelatedObject(objectData, contactDataList, "ContactObj");
        List<IObjectData> opportunityDataList = getOpportunityDataList(arg.getObjectDataId(), "OpportunityObj");
        setRelatedObject(objectData, opportunityDataList, "OpportunityObj");
        List<IObjectData> newOpportunityDataList = getOpportunityDataList(arg.getObjectDataId(), "NewOpportunityObj");
        setRelatedObject(objectData, newOpportunityDataList, "NewOpportunityObj");
        List<IObjectData> partnerDataList = getRelatedObjectDataList(arg.getObjectDataId(), "PartnerObj");
        setRelatedObject(objectData, partnerDataList, "PartnerObj");
    }

    private void setRelatedObject(IObjectData leadsData, List<IObjectData> dataList, String apiName) {
        String id = "";
        String name = "";
        String status = "";
        if (dataList.size() > 0) {
            IObjectData data = dataList.get(0);
            if (data != null) {
                id = data.getId();
                name = data.getName();
                if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
                    status = AccountUtil.getStringValue(data, "biz_status", "invalid");
                }
                if (apiName.equals(SFAPreDefineObject.NewOpportunity.getApiName())) {
                    status = AccountUtil.getStringValue(data, "sales_status", "");
                }
            }
        }
        if (apiName.equals(SFAPreDefineObject.Account.getApiName())) {
            leadsData.set("account_id", id);
            leadsData.set("account_id__r", name);
        }
        if (apiName.equals(SFAPreDefineObject.Contact.getApiName())) {
            leadsData.set("contact_id", id);
            leadsData.set("contact_id__r", name);
        }
        if (apiName.equals(SFAPreDefineObject.Opportunity.getApiName())) {
            leadsData.set("opportunity_id", id);
            leadsData.set("opportunity_id__r", name);
            leadsData.set("opportunity_status", status);
        }
        if (apiName.equals(SFAPreDefineObject.NewOpportunity.getApiName())) {
            leadsData.set("new_opportunity_id", id);
            leadsData.set("new_opportunity_id__r", name);
            leadsData.set("new_opportunity_status", status);
        }
        if (apiName.equals(SFAPreDefineObject.Partner.getApiName())) {
            leadsData.set("transfer_partner_id", id);
            leadsData.set("transfer_partner_id__r", name);
        }
    }

    private List<IObjectData> getOpportunityDataList(String leadsId, String apiName) {
        List<IObjectData> result = Lists.newArrayList();
        List<Map> list = LeadsUtils.getOpportunityDataById(controllerContext.getTenantId(), apiName, leadsId);
        for (Map m : list) {
            result.add(ObjectDataDocument.of(m).toObjectData());
        }
        return result;
    }

    private List<IObjectData> getRelatedObjectDataList(String leadsId, String apiName) {
        List<IObjectData> result = Lists.newArrayList();
        List<Map> list = LeadsUtils.getRelatedObjectDataById(controllerContext.getTenantId(), apiName, leadsId);
        for (Map m : list) {
            result.add(ObjectDataDocument.of(m).toObjectData());
        }
        return result;
    }

    private IFieldDescribe constructFieldDescribe(String description, String targetApiName, String apiName) {

        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("describe_api_name", "LeadsObj");
        fieldMap.put("is_index", true);
        fieldMap.put("is_active", true);
        fieldMap.put("create_time", System.currentTimeMillis());
        fieldMap.put("description", description);
        fieldMap.put("is_unique", false);
        fieldMap.put("label", description);
        fieldMap.put("target_api_name", targetApiName);
        fieldMap.put("type", "object_reference");
        fieldMap.put("target_related_list_name", "object_leads_list");
        fieldMap.put("field_num", null);
        fieldMap.put("target_related_list_label", I18N.text("LeadsObj.attribute.self.display_name")/*销售线索*/);
        fieldMap.put("action_on_target_delete", "cascade_delete");
        fieldMap.put("is_required", false);
        fieldMap.put("api_name", apiName);
        fieldMap.put("define_type", "package");
        fieldMap.put("is_index_field", true);
        fieldMap.put("is_single", false);
        fieldMap.put("index_name", "index");
        fieldMap.put("status", "released");
        return FieldDescribeExt.of(fieldMap).getFieldDescribe();
    }

    private IFieldDescribe constructQuoteFieldDescribe(String description, String targetApiName, String apiName, String field) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), targetApiName);

        Map<String, Object> fieldMap = Maps.newHashMap();
        fieldMap.put("describe_api_name", "LeadsObj");
        fieldMap.put("is_index", true);
        fieldMap.put("is_active", true);
        fieldMap.put("create_time", System.currentTimeMillis());
        fieldMap.put("description", description);
        fieldMap.put("is_unique", false);
        fieldMap.put("label", description);
        fieldMap.put("type", "select_one");
        fieldMap.put("options", JSONObject.toJSON(objectDescribe.getFieldDescribe(field).get("options")));
        fieldMap.put("field_num", null);
        fieldMap.put("is_required", false);
        fieldMap.put("api_name", apiName);
        fieldMap.put("define_type", "package");
        fieldMap.put("is_index_field", true);
        fieldMap.put("is_single", false);
        fieldMap.put("index_name", "index");
        fieldMap.put("status", "released");
        return FieldDescribeExt.of(fieldMap).getFieldDescribe();
    }

    @Override
    protected Result after(Arg arg, Result result) {

        Result newResult = super.after(arg, result);
        IObjectData objectData = newResult.getData().toObjectData();
        AccountUtil.handleRemainingTime(Lists.newArrayList(objectData));
        LeadsUtils.handleIsRemindRecycling(Lists.newArrayList(objectData));
        handleCard(newResult);
        if (newResult.getLayout() != null) {
            ILayout layout = new Layout(newResult.getLayout());
            specialLogicForLayout(layout, newResult);
        }
        return newResult;
    }

    private void handleCard(Result newResult) {
        Object card = (newResult.getData()).get("picture_path");
        if (card != null && !StringUtils.isEmpty(card)) {
            List cardlist = (List) card;
            if (cardlist == null || cardlist.isEmpty()) {
                newResult.getData().put("picture_path", Lists.newArrayList());
            } else {
                Object path = ((Map) (cardlist.get(0))).get("path");
                if (path == null || StringUtils.isEmpty(path.toString())) {
                    newResult.getData().put("picture_path", Lists.newArrayList());
                }
            }
        }
    }

    private void specialLogicForLayout(ILayout layout, Result result) {
        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
        log.info("specialLogicForLayout-clientInfo=" + clientInfo);

        //所有端需要做的特殊处理
        LeadsUtils.handleLeadsFlowRecordRelatedComponents(layout);
        ObjectDataDocument data = result.getData();
        String leadsPoolId = data.get("leads_pool_id") == null ? "" : data.get("leads_pool_id").toString();
        Object ownerObj = (result.getData()).get("owner");
        ArrayList<Object> owner = new ArrayList<>();
        if (ownerObj instanceof ArrayList) {
            owner = (ArrayList<Object>) ownerObj;
        }
        if (!StringUtils.isEmpty(leadsPoolId) && owner != null && owner.size() < 1) {
            CheckNeedShowRelatedObjsResult checkNeedShowRelatedObjsResult = checkNeedShowRelatedObjs();
            handleHideFieldsBySetting(layout, checkNeedShowRelatedObjsResult.getNeedHideFields());
            if (!checkNeedShowRelatedObjsResult.isAllowMemberRelation()) {
                try {
                    Optional<GroupComponent> groupComponent = layout.getComponents().stream().filter(x -> "otherInfo".equals(x.getName())).map(x ->
                            (GroupComponent) x
                    ).findFirst();
                    groupComponent.ifPresent(x -> {
                        try {
                            List<IComponent> childComponents = x.getChildComponents();
                            childComponents.removeIf(k -> "operation_log".equals(k.get("api_name", String.class)));
                            if (!childComponents.isEmpty()) {
                                x.setChildComponents(childComponents);
                            } else {
                                x.get("child_components", ArrayList.class).clear();
                            }
                        } catch (MetadataServiceException e) {
                            log.error("getOtherInfoChildComponents error", e);
                        }
                    });
                    List<Map<String, Object>> components = layout.get("components", ArrayList.class);
                    components.removeIf(k -> "otherInfo".equals(k.get("api_name")) && ((ArrayList) k.get("child_components")).isEmpty());

                } catch (MetadataServiceException ex) {
                    log.error("get allow_member_relation components error: execute sql", ex);
                }
            }
            boolean isAllowMemberViewFeed = checkNeedShowRelatedObjsResult.isAllowMemberViewFeed();
            boolean isAllowMemberSendFeed = checkNeedShowRelatedObjsResult.isAllowMemberSendFeed();
            if (!isAllowMemberSendFeed) {
                LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
                    try {
                        List<IComponent> childComponents = x.getChildComponents();
                        Optional<IComponent> optional = childComponents.stream().filter(c -> "sale_log".equals(c.getName())).findFirst();
                        if (optional.isPresent()) {
                            IComponent component = optional.get();
                            List<IButton> buttons = optional.get().getButtons();
                            buttons.removeIf(b -> ObjectAction.ADD_EVENT.getActionCode().equals(b.getAction()));
                            component.setButtons(buttons);
                        }
                    } catch (MetadataServiceException e) {
                        log.error("get allow_member_view_feed components error", e);
                    }
                });
            }
            if (!isAllowMemberViewFeed) {
                LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
                    try {
                        List<IComponent> childComponents = x.getChildComponents();
                        childComponents.removeIf(k -> "sale_log".equals(k.get("api_name", String.class)));
                        x.setChildComponents(childComponents);
                    } catch (MetadataServiceException e) {
                        log.error("get allow_member_send_feed components error", e);
                    }
                });
            }
        }
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> childComponents = x.getChildComponents();
                childComponents.removeIf(k -> "LeadsTransferLogObj_leads_id_related_list".equals(k.get("api_name", String.class))
                        || "ContactObj_leads_id_related_list".equals(k.get("api_name", String.class))
                        || "AccountObj_leads_id_related_list".equals(k.get("api_name", String.class))
                        || "OpportunityObj_leads_id_related_list".equals(k.get("api_name", String.class))
                        || "NewOpportunityObj_leads_id_related_list".equals(k.get("api_name", String.class)));
                if (!LeadsUtils.isGrayLeadsDuplicated(controllerContext.getTenantId())) {
                    childComponents.removeIf(k -> "LeadsObj_collected_to_related_list".equals(k.get("api_name", String.class)));
                }
                Optional<IComponent> optional = childComponents.stream().filter(c -> "LeadsObj_collected_to_related_list".equals(c.get("api_name", String.class))).findFirst();
                if (optional.isPresent()) {
                    IComponent component = optional.get();
                    List<IButton> buttons = component.getButtons();
                    buttons.removeIf(b -> "Add_button_default".equals(b.get("api_name", String.class))
                            || "BulkRelate_button_default".equals(b.get("api_name", String.class))
                            || "IntelligentForm_button_default".equals(b.get("api_name", String.class))
                            || "BulkDisRelate_button_default".equals(b.get("api_name", String.class)));
                    component.setButtons(buttons);
                }
                x.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.error("getChildComponents error", e);
            }
        });

        //移动端移除ViewFeedCard按钮
        List<IButton> buttons = layout.getButtons();
        if (RequestUtil.isMobileRequest()) {
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.VIEW_FEED_CARD.getActionCode()));
            layout.setButtons(buttons);
            AccountUtil.removeRFMResultCard(layout);
            //移动端屏蔽人脉关系雷达卡片
            LayoutUtils.removeContactMemberRelationshipCard(layout);
        } else if (RequestUtil.isH5Request()) {
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.COLLECT_TO.getActionCode()));
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.MarkMQL.getActionCode()));
            layout.setButtons(buttons);
        }
        if (!CollectionUtils.isEmpty(buttons)) {
            buttons.stream().filter(x -> "Transfer_button_default".equals(x.getName())).findFirst().ifPresent(x -> {
                x.setAction("TransferUI");
                x.setName("TransferUI_button_default");
                x.setLabel(I18N.text("paas.udobj.action.transfer_ui"));
            });
        }
    }

    /**
     * 根据线索池的设置动态隐藏隐藏字段
     */
    protected void handleHideFieldsBySetting(ILayout layout, List<String> needHideFieldList) {

        //隐藏详细信息和顶部信息中的字段
        if (!CollectionUtils.isEmpty(needHideFieldList)) {
            removeDetailInfoFields(layout, needHideFieldList);
            LayoutExt.of(layout).getTopInfoComponent().ifPresent(topInfoComponent -> {
                PreDefLayoutUtil.removeSomeFields(topInfoComponent, new HashSet<>(needHideFieldList));
            });
        }
    }
}
