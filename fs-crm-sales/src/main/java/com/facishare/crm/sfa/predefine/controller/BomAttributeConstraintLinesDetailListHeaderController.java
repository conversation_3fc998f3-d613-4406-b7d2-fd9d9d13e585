package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.SoRelatedListUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailListHeaderController;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;


public class BomAttributeConstraintLinesDetailListHeaderController extends StandardDetailListHeaderController {

    @Override
    protected List<IButton> getDetailButtons() {
        List<IButton> detailButtons = super.getDetailButtons();
        detailButtons.removeIf(x-> SoRelatedListUtils.xxProductFilterButtonInfo.contains(x.getName()));
        return detailButtons;
    }
}
