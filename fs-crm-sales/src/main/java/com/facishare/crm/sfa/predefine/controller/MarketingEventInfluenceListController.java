package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/1/11 16:06
 */


public class MarketingEventInfluenceListController extends StandardListController {

	@Override
	protected Result after(Arg arg, Result result) {
		super.after(arg, result);
		if (result.getLayout() != null) {
			// 暂时只支持导出
			ILayout layout = result.getLayout().toLayout();
			List<IButton> buttons = layout.getButtons();
			if(CollectionUtils.notEmpty(buttons)) {
				buttons.removeIf(b -> !ObjectAction.BATCH_EXPORT.getActionCode().equals(b.getAction()));
				layout.setButtons(buttons);
			}
		}
		return result;
	}

}
