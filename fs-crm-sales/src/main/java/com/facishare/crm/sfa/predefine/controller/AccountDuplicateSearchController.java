package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.LeadsTransferLogUtil;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.function.UnaryOperator;

public class AccountDuplicateSearchController extends SFADuplicateSearchController {

    @Override
    protected GetResult.Result doService(GetResult.Arg arg) {
        GetResult.Result result = super.doService(arg);
        if (IDuplicatedSearch.Type.NEW == arg.getType()) {
            if(isGrayMultiDuplicateRuleAndSupportFilterGray()) {
                multiDuplicatedSearch(result, Lists.newArrayList(SFAPreDefineObject.Leads.getApiName()));
            } else {
                normalDuplicatedSearch(result, Lists.newArrayList(SFAPreDefineObject.Leads.getApiName()));
            }
        }
        return result;
    }

    @Override
    protected UnaryOperator<List<DuplicateSearchResult.DuplicateData>> handleDuplicateSearchResult() {
        String dataId = arg.getObjectData().getId();
        return (it) -> {
            LeadsTransferLogUtil.dealDuplicatedData(controllerContext.getUser(), controllerContext.getObjectApiName(), dataId, it);
            return it;
        };
    }
}

