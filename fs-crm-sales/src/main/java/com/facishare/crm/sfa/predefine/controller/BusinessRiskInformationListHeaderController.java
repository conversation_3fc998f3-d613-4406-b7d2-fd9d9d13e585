package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2024/3/28 16:08
 */
@Slf4j
public class BusinessRiskInformationListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        ILayout layout = new Layout((res.getLayout()));
        List<IButton> buttonList = layout.getButtons();
        if (buttonList != null) {
            buttonList.removeIf(x->x.getAction().equals(ObjectAction.CREATE.getActionCode()));
            layout.setButtons(buttonList);
        }
        return res;
    }
}
