package com.facishare.crm.sfa.predefine.controller;


import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


public class MarketingAttributionDescribeLayoutController extends SFADescribeLayoutController {
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if(result == null || result.getObjectDescribe() == null) {
            return;
        }

        ObjectDescribeDocument describeDocument = result.getObjectDescribe();
        IObjectDescribe objectDescribe = describeDocument.toObjectDescribe();
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe("trigger_object_api_name");
        if(fieldDescribe != null && fieldDescribe instanceof SelectOneFieldDescribe) {
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe)fieldDescribe;
            List<ISelectOption> options = selectOneFieldDescribe.getSelectOptions();
            for(ISelectOption option : options) {
                String i18nKey = String.format("%s.attribute.self.display_name", option.getValue());
                String i18nValue = I18N.text(i18nKey);
                if(StringUtils.isNotBlank(i18nValue)) {
                    option.setLabel(i18nValue);
                }
            }

            result.setObjectDescribe(ObjectDescribeDocument.of(objectDescribe));
        }
    }
}
