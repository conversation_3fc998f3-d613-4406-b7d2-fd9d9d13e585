package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.attributepricebook.AttributePriceBookServiceImpl;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

/**
 * 属性价目表明细列表
 *
 * <AUTHOR>
 */
public class AttributePriceBookLinesDetailController extends StandardDetailController {

    AttributePriceBookServiceImpl attributePriceBookService = SpringUtil.getContext().getBean(AttributePriceBookServiceImpl.class);

    @Override
    protected IObjectData findObjectData(Arg arg) {
        IObjectData iObjectData = super.findObjectData(arg);
        attributePriceBookService.assembleObjectDataDocument(controllerContext.getUser(), Lists.newArrayList(iObjectData));
        return iObjectData;
    }
}
