package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.custom.MarketRuleService;
import com.facishare.crm.sfa.predefine.service.mcr.MCRService;
import com.facishare.crm.sfa.utilities.util.AccountMultiKeyWordSearchUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.ObjectUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.constant.ContactConstants.Field.POSITION_ID;
import static com.facishare.crm.sfa.utilities.constant.ContactConstants.Field.PRIMARYCONTACT;

/**
 * <AUTHOR> lik
 * @date : 2024/7/25 19:24
 */

public class ContactEvaluationRelatedListController extends StandardRelatedListController {
    private static final MCRService mcrService = SpringUtil.getContext().getBean(MCRService.class);

    @Override
    protected boolean skipFilterUnauthorizedFields() {
        if(ObjectUtils.allNotEmpty(arg.getExtraData()) && arg.getExtraData().containsKey("not_check_authority") && (Boolean) arg.getExtraData().get("not_check_authority")){
            return true;
        }
        return super.skipFilterUnauthorizedFields();
    }
    @Override
    protected Result after(StandardRelatedListController.Arg arg, Result result) {
        super.after(arg, result);
        List<ObjectDataDocument> dataList = result.getDataList();
//        fillContactInfo(dataList);
        result.setDataList(dataList);
        return result;
    }

    private void fillContactInfo(List<ObjectDataDocument> dataList){
        if(CollectionUtils.notEmpty(dataList) && ObjectUtils.allNotEmpty(arg.getExtraData()) && arg.getExtraData().containsKey("need_fill_contact_info")){
            boolean falg = (boolean)arg.getExtraData().get("need_fill_contact_info");
            if(falg){
                List<String> contactIds = dataList.stream().filter(x->ObjectUtils.allNotEmpty(x.get("contact_id"))).map(x->x.get("contact_id").toString()).collect(Collectors.toList());
                List<IObjectData> contactDataS = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(),contactIds, SFAPreDefineObject.Contact.getApiName());
                if(CollectionUtils.notEmpty(contactDataS)){
                    Map<String,IObjectData> contactDataMap = contactDataS.stream().collect(Collectors.toMap(IObjectData::getId,Function.identity(),(v1,v2)->v1));
                    List<String> positionIds =  dataList.stream().filter(x->ObjectUtils.allNotEmpty(x.get(POSITION_ID))).map(x->x.get(POSITION_ID).toString()).collect(Collectors.toList());
                    if(CollectionUtils.notEmpty(positionIds)){
                        List<IObjectData> positionDataS = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), positionIds,SFAPreDefineObject.Position.getApiName());
                        Map<String,IObjectData> positionDataMap = positionDataS.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity(),(v1, v2)->v1));
                        IObjectDescribe contactDesc = serviceFacade.findObject(controllerContext.getTenantId(), SFAPreDefineObject.Contact.getApiName());
                        List<Map> primaryContactOptionsList = contactDesc.getFieldDescribe(PRIMARYCONTACT).get("options",List.class);
                        Map<String,String> primaryContactOptionsMap = primaryContactOptionsList.stream().collect(Collectors.toMap(x->x.get("value").toString(),y->y.get("label").toString(),(v1,v2)->v1));
                        for(ObjectDataDocument data : dataList){
                            if(ObjectUtils.allNotEmpty(data.get("contact_id"))){
                                mcrService.fillContactInfo(data.toObjectData(),contactDataMap.get(data.get("contact_id").toString()),positionDataMap,contactDesc.getFieldDescribe(PRIMARYCONTACT).getLabel(),primaryContactOptionsMap);
                            }

                        }
                    }
                }
            }
        }
    }
}
