package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
public class LeadsTransferLogNewDetailController extends SFANewDetailController {
    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void doDataPrivilegeCheck(StandardDetailController.Arg arg) {
    }

    @Override
    protected StandardDetailController.Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        StandardDetailController.Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null || newResult.getData() == null) {
            return newResult;
        }
        List<IFieldDescribe> fieldDescribes = newResult.getDescribe().toObjectDescribe().getFieldDescribes();
        Optional<IFieldDescribe> opField = fieldDescribes.stream().filter(f -> "created_by".equals(f.getApiName())).findFirst();
        if (opField.isPresent()) {
            //转换者
            opField.get().setLabel(I18N.text(SFAI18NKeyUtil.SFA_CONVERTER));
        }
        opField = fieldDescribes.stream().filter(f -> "create_time".equals(f.getApiName())).findFirst();
        if (opField.isPresent()) {
            //转换时间
            opField.get().setLabel(I18N.text("sfa.ContactBusiness.2957.3"));
        }
        ILayout layout = new Layout(newResult.getLayout());
        layout.setButtons(Lists.newArrayList());
        try {
            Optional<GroupComponent> optionalCom = layout.getComponents().stream().filter(x -> "detailInfo".equals(x.getName())).map(x ->
                    (GroupComponent) x
            ).findFirst();
            layout.getComponents().clear();
            if (optionalCom.isPresent()) {
                IComponent component = optionalCom.get();

                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m -> {
                    IGroupComponent groupComponent = (IGroupComponent) m;
                    groupComponent.setButtons(Lists.newArrayList());
                    groupComponent.get("child_components", ArrayList.class).clear();
                });
            }
        } catch (MetadataServiceException e) {
            log.error("getChildComponents error", e);
        }
        return newResult;
    }
}
