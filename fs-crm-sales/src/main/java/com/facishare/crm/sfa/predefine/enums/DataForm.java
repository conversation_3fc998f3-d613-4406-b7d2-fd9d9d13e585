package com.facishare.crm.sfa.predefine.enums;

/**
 * <AUTHOR>
 * @time 2023-07-08 11:14
 * @Description
 */
public enum DataForm {
    LIST("1", "合作伙伴列表"),
    PROCUREMENT_ENTERPRISE_TRANSFER("6", "招投标主体转换"),
    LEADS_TRANSFER_PARTNER("leads_transfer_partner", "线索转换合作伙伴");

    private final String value;
    private final String label;

    DataForm(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }
    public static DataForm getByValue(String code) {
        for (DataForm e : values()) {
            if (e.value.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
