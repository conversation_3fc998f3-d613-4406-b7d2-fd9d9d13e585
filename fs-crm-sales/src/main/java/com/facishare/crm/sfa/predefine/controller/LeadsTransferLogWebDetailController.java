package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class LeadsTransferLogWebDetailController extends SFAWebDetailController {
    private final List<String> remainComponentType = Lists.newArrayList("tabs", "simple", "form", "navigation", "top_info");
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);


    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void doDataPrivilegeCheck(Arg arg) {
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null || newResult.getData() == null) {
            return newResult;
        }
        try {
            ILayout layout = new Layout(newResult.getLayout());
            WebDetailLayout webDetailLayout = WebDetailLayout.of(layout);
            List<IComponent> components = layout.getComponents();
            if (CollectionUtils.notEmpty(components)) {
                if (bizConfigThreadLocalCacheService.isPartnerEnabled(controllerContext.getTenantId())) {
                    addSpecialField(components);
                }
                List<String> remainApiNames = components.stream().filter(x -> remainComponentType.contains(x.getType())).map(x -> x.getName()).collect(Collectors.toList());
                webDetailLayout.retainComponents(remainApiNames);
            }

			webDetailLayout.clearButtons();
			// 只保留编辑按钮
//			List<IButton> buttons = webDetailLayout.getButtons();
//			buttons.removeIf(b -> !Objects.equals(b.getAction(), ObjectAction.UPDATE.getActionCode()));
//			webDetailLayout.setButtons(buttons);
        } catch (Exception e) {
            log.error("LeadsFlowRecordWebDetailController get component error", e);
        }
        return newResult;
    }

    private void addSpecialField(List<IComponent> components) {
        try {
            boolean finished = false;
            for (IComponent component : components) {
                if (!(component instanceof IFormComponent)) {
                    continue;
                }
                FormComponentExt formComponent = FormComponentExt.of((IFormComponent) component);
                List<IFieldSection> fieldSections = formComponent.getFieldSections();
                List<IFormField> fields;
                for (IFieldSection fieldSection : fieldSections) {
                    if (Objects.equals("base_field_section__c", fieldSection.getName())) {
                        fields = fieldSection.getFields();
                        FormField partnerIdField = new FormField();
                        partnerIdField.setReadOnly(false);
                        partnerIdField.setRequired(false);
                        partnerIdField.setRenderType("object_reference");
                        partnerIdField.setFieldName("partner_id");
                        partnerIdField.set("target_display_name", I18N.text("LeadsTransferLogObj.field.partner_id.label"));
                        fields.add(partnerIdField);

                        FormField isPartnerSourceLeadsField = new FormField();
                        isPartnerSourceLeadsField.setRequired(false);
                        isPartnerSourceLeadsField.setReadOnly(true);
                        partnerIdField.setRenderType("true_or_false");
                        partnerIdField.setFieldName("is_partner_source_leads");
                        partnerIdField.set("target_display_name", I18N.text("LeadsTransferLogObj.field.is_partner_source_leads.label"));
                        fields.add(isPartnerSourceLeadsField);
                        fieldSection.setFields(fields);
                        finished = true;
                        break;
                    }
                }
                if (finished) {
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("LeadsFlowRecordWebDetailController add partner_id error", e);
        }

    }
}
