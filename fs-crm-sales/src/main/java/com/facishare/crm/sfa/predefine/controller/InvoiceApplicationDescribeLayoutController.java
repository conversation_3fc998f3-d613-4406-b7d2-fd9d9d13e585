package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.crm.sfa.utilities.util.VersionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;

import java.util.*;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

public class InvoiceApplicationDescribeLayoutController extends SFADescribeLayoutController {

    private final InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if(result.getLayout() != null){
            invoiceService.removeCloneButtonOnOpenInvoiceLine(controllerContext.getUser(), new Layout(result.getLayout()));
        }
        if (arg.getLayout_type() == null) {
            return;
        }
        switch (arg.getLayout_type()) {
            case LAYOUT_TYPE_EDIT:
                checkClientVersion();
                //编辑时需要将客户 销售订单 置为不可编辑
                List<String> readonlyFieldNames = new ArrayList<String>();
                readonlyFieldNames.add("order_id");
                readonlyFieldNames.add("account_id");
                PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, readonlyFieldNames);
                dddMaxInvoiceLineFields(result);
                break;

            case LAYOUT_TYPE_ADD:
                checkClientVersion();
                break;
        }
    }

    private void dddMaxInvoiceLineFields(Result result) {
        if (invoiceService.isNewInvoiceOpen(controllerContext.getUser())) {
            InvoiceApplicationConstants.InvoiceApplicationMode invoiceApplicationMode = invoiceService.getInvoiceMode(controllerContext.getTenantId());

            if(invoiceApplicationMode.equals(InvoiceApplicationConstants.InvoiceApplicationMode.SALES_ORDER)){
                Optional<DetailObjectListResult> invoiceLinesOptional = result.getDetailObjectList()
                        .stream().filter(o -> Objects.equals("invoice_id", o.getFieldApiName())).findFirst();

                if (invoiceLinesOptional.isPresent()) {
                    DetailObjectListResult invoiceLinesDetailObjectListRe = invoiceLinesOptional.get();
                    Map describe = invoiceLinesDetailObjectListRe.getObjectDescribe();
                    Map fields = (Map) describe.get("fields");
                    Map noInvoiceQuantity = (Map)  fields.get("invoiced_amount");
                    Map maxInvoiceQuantity = Maps.newHashMap(noInvoiceQuantity);

                    maxInvoiceQuantity.remove("index_name");
                    maxInvoiceQuantity.remove(DBRecord.ID);
                    maxInvoiceQuantity.put("api_name", "max_invoice_amount");
                    maxInvoiceQuantity.put("type", "currency");
                    maxInvoiceQuantity.put("label", I18N.text("InvoiceApplicationLinesObj.field.invoiced_amount.label"));
                    fields.put("max_invoice_amount", maxInvoiceQuantity);
                }
            }
            if(invoiceApplicationMode.equals(InvoiceApplicationConstants.InvoiceApplicationMode.SALES_ORDER_PRODUCT)){
                Optional<DetailObjectListResult> invoiceLinesOptional = result.getDetailObjectList()
                        .stream().filter(o -> Objects.equals("invoice_id", o.getFieldApiName())).findFirst();

                if (invoiceLinesOptional.isPresent()) {
                    DetailObjectListResult invoiceLinesDetailObjectListRe = invoiceLinesOptional.get();
                    Map describe = invoiceLinesDetailObjectListRe.getObjectDescribe();
                    Map fields = (Map) describe.get("fields");
                    Map noInvoiceQuantity = (Map) fields.get("invoiced_quantity");
                    Map maxInvoiceQuantity = Maps.newHashMap(noInvoiceQuantity);

                    maxInvoiceQuantity.remove("index_name");
                    maxInvoiceQuantity.remove(DBRecord.ID);
                    maxInvoiceQuantity.put("api_name", "max_invoice_quantity");
                    maxInvoiceQuantity.put("type", "number");
                    maxInvoiceQuantity.put("label", I18N.text("InvoiceApplicationLinesObj.field.invoiced_quantity.label"));
                    fields.put("max_invoice_quantity", maxInvoiceQuantity);
                }
            }
        }
    }


    private void checkClientVersion() {
        if(invoiceService.isNewInvoiceOpen(controllerContext.getUser())){
            if (VersionUtil.isVersionEarlierEqualThan705(controllerContext.getRequestContext())) {
                throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
            }
            if (invoiceService.isNewInvoiceUnderOpen(controllerContext.getUser())) {
                throw new ValidateException(I18N.text("invoice.line.under.open"));
            }
        }
    }

    @Override
    protected boolean supportSaveDraft() {
        return true;
    }


}
