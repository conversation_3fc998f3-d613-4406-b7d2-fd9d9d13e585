package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;

/**
 * Created by zhaopx on 2017/11/15.
 */
public class CampaignMembersDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }

        switch (arg.getLayout_type()) {
            case LayoutExt.Edit_LAYOUT_TYPE:
                //编辑时预设字段设为只读
                PreDefLayoutUtil.setFormComponentFieldsReadOnlyForPackage(formComponent);
                break;
            default:
                break;
        }
    }
}
