package com.facishare.crm.sfa.predefine.enums;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/24 17:30
 * @description:
 */
public enum BizCurrencyEnum {

    RMB(0, "人民币"),
    USD(1, "美元");

    private Integer value;
    private String label;

    BizCurrencyEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static BizCurrencyEnum fromValue(Integer value) {
        for (BizCurrencyEnum bizCurrencyEnum : BizCurrencyEnum.values()) {
            if (bizCurrencyEnum.getValue().equals(value)) {
                return bizCurrencyEnum;
            }
        }
        return null;
    }

}
