package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.crm.sfa.utilities.util.MaskUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.google.common.collect.Sets;

import java.util.Set;

public class ContactRelatedListController extends SFARelatedListController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);

        if(RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()){
            ContactUtil.addButton(controllerContext.getUser(), result);
        }
        if (CollectionUtils.empty(result.getDataList())) {
            return result;
        }
        //列表需要下发手机号归属地信息
        ContactUtil.fillPhoneNumberInformation(result, objectDescribe);
        ContactUtil.handlePredefinePhoneField(ObjectDataDocument.ofDataList(result.getDataList()));
        ContactUtil.concatenateBirthDay(ObjectDataDocument.ofDataList(result.getDataList()));
        MaskUtil.maskEncodeOfDataListByFiledList(controllerContext.getUser(),ObjectDataDocument.ofDataList(result.getDataList()),objectDescribe);
        return result;
    }

    @Override
    protected Set<String> getUnauthorizedFields() {
        Set<String> unauthorizedFields = super.getUnauthorizedFields();
        if (unauthorizedFields != null && unauthorizedFields.contains("tel")) {
            unauthorizedFields.addAll(Sets.newHashSet("tel1", "tel2", "tel3", "tel4", "tel5"));
        }
        if (unauthorizedFields != null && unauthorizedFields.contains("mobile")) {
            unauthorizedFields.addAll(Sets.newHashSet("mobile1", "mobile2", "mobile3", "mobile4", "mobile5"));
        }
        return unauthorizedFields;
    }
}
