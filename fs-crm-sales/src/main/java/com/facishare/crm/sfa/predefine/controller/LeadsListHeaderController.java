package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;

import java.util.List;


/**
 * Created by zhangtao on 2019/1/2.
 */
public class LeadsListHeaderController extends BaseSFAListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if(RequestUtil.isH5Request() || RequestUtil.isMobileRequest() || RequestUtil.isWXMiniProgram()) {
            List<String> removeButtonApiList = Lists.newArrayList("ProcessLeads_button_default", "Merge_button_default");
            result.getButtons().removeIf(x -> removeButtonApiList.contains(x.toButton().getApiName()));
        }

        if(RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()){
            addMobileButton(result);
        }
        //修改 button
        ButtonUtils.editMergeButton(result);
        return result;
    }

    private void addMobileButton(Result newResult) {
        ILayout layout = new Layout((newResult.getLayout()));
        layout.addButtons(ObjectAction.SCAN_CARD.createButton());
    }
}
