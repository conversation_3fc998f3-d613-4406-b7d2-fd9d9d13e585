package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.UseRangeFieldDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

public class AvailableRangeDetailController extends SFADetailController {
    private static final UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext().getBean(UseRangeFieldDataRender.class);
    private static final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);

    @Override
    protected Result after(Arg arg, Result result) {
        if (result.getLayout() != null) {
            Optional.ofNullable(result.getData()).ifPresent(data -> {
                describe.getFieldDescribes().stream().filter(k -> IFieldType.UseRange.equals(k.getType()))
                        .forEach(k -> {
                            data.put(k.getApiName(),
                                    useRangeFieldDataRender.render(data.get(k.getApiName()),
                                            ((UseRangeFieldDescribe) k).getTargetApiName()));
                        });
            });
        }
        return super.after(arg, result);
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() || RequestUtil.isH5Request()) {
            availableRangeUtils.removeMobileButton(layout);
        }
        return layout;
    }
}
