package com.facishare.crm.sfa.predefine.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @time 2023-12-15 11:22
 * @Description
 */
public enum CategoryFilterEnum {
    ALL("ALL"),
    PRODUCT("Product"),
    SHOP("Shop");

    private final String filterCategory;

    CategoryFilterEnum(String filterCategory) {
        this.filterCategory = filterCategory;
    }

    public static CategoryFilterEnum getCategoryFilter(String filterCategory) {
        if (StringUtils.isBlank(filterCategory)) {
            return null;
        }
        for (CategoryFilterEnum e : values()) {
            if (e.filterCategory.equals(filterCategory)) {
                return e;
            }
        }
        return null;
    }
}
