package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.ObjectUtils;
import com.facishare.crm.sfa.utilities.util.UserUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardQuickEditLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> lik
 * @date : 2023/4/18 20:26
 */
@Slf4j
public class AccountQuickEditLayoutController extends StandardQuickEditLayoutController {

    @Override
    protected void validateByData() {
        super.validateByData();
    }

    @Override
    protected Result doService(Arg arg) {
            result = super.doService(arg);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg,result);
        return result;
    }
    @Override
    protected void processLayout(ILayout layout) {
        Optional<IFormField> formField = LayoutExt.of(this.layout).getField(arg.getFieldApiName());
        if (!formField.isPresent()) {
            return;
        }
        if (AccountUtil.handleAuthorityOfQuickEdit(describe, controllerContext.getUser(), arg.getFieldApiName(), data)) {
            formField.get().setReadOnly(false);
        }
    }
}
