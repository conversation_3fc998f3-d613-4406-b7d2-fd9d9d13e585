package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.util.PriceBookUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

public class AvailablePriceBookListHeaderController extends StandardListHeaderController {

    private  FunctionPrivilegeService functionPrivilegeService = (FunctionPrivilegeService) SpringUtil.getContext().getBean("functionPrivilegeService");

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        addSpecialButton4PriceBook(arg, result);
        return result;
    }

    private void addSpecialButton4PriceBook(Arg arg, Result result) {
        if (null == result) {
            return;
        }
        if (SFAPreDefineObject.PriceBook.getApiName().equals(arg.getTargetObjectApiName()) &&
                AvailableConstants.PriceBookField.PRICE_BOOK_ID.equals(arg.getObjectReferenceApiName())) {
            List<String> actionCodes = Lists.newArrayList(ObjectAction.UPDATE.getActionCode());
            Map<String, Boolean> funPrivilegeMap = functionPrivilegeService.funPrivilegeCheck(controllerContext.getUser(),
                    SFAPreDefineObject.AvailableRange.getApiName(), actionCodes);
            Boolean hasPrivilege = funPrivilegeMap.getOrDefault(ObjectAction.UPDATE.getActionCode(), Boolean.FALSE);
            if (Boolean.TRUE.equals(hasPrivilege)) {
                List<ButtonDocument> buttonList = result.getButtons();
                PriceBookUtil.addDeleteButton(buttonList);
                if (null != result.getLayout()) {
                    List<IButton> buttons = result.getLayout().toLayout().getButtons();
                    PriceBookUtil.addSpecialButton(buttons, false);
                    result.getLayout().toLayout().setButtons(buttons);
                }
            }
        }
    }
}
