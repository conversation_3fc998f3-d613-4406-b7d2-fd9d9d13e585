package com.facishare.crm.sfa.predefine.enums;

public enum VerificationMethodEnum {
    ArOffsetAr("ArOffsetAr", "应收冲应收"),
    PaymentOffsetAr("PaymentOffsetAr", "回款冲应收"),
    AccountFlowOffsetAr("AccountFlowOffsetAr", "账户流水冲应收");

    private final String value;
    private final String label;

    VerificationMethodEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
