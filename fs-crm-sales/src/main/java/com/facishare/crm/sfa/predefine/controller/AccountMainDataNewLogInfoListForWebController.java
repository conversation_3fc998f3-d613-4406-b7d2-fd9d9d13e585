package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetNewLogInfoListForWeb;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import org.apache.commons.lang3.StringUtils;

public class AccountMainDataNewLogInfoListForWebController extends StandardNewLogInfoListForWebController {
    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogInfo logInfo = record.getLogInfo();
        if (logInfo == null) {
            return super.modifyRecordToLogRecord(record);
        }
        String textMessage = logInfo.getTextMessage();
        if (StringUtils.isBlank(textMessage)) {
            return super.modifyRecordToLogRecord(record);
        }
        String msgLabel = "[" + "MasterDataAppFieldControl" + "]";
        if (textMessage.contains(msgLabel)) {
            String newLabel = "[" + I18N.text("sfa.master.field.control") + "]";
            record.setOperationLabel(newLabel);
        }
        return super.modifyRecordToLogRecord(record);
    }
}
