package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.AdjustableForecastTask;
import com.facishare.crm.sfa.predefine.service.ForecastTaskService;
import com.facishare.crm.sfa.predefine.service.model.ForecastTaskModel;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ForecastRuleConstants;
import com.facishare.crm.sfa.utilities.constant.ForecastTaskConstants;
import com.facishare.crm.sfa.utilities.proxy.model.bi.BiCrmRestQuery;
import com.facishare.crm.sfa.utilities.proxy.model.bi.StatViewDataQuery;
import com.facishare.crm.sfa.utilities.util.ContextConvertUtils;
import com.facishare.crm.sfa.utilities.util.DepartmentBasedAccessControl;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardListController;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ForecastTaskListController extends AbstractStandardListController<ForecastTaskListController.ForecastTaskListArg> implements AdjustableForecastTask {

    public static final String GROUP_RESULT_LABEL = "group_result_label";
    private static final ForecastTaskService FORECAST_TASK_SERVICE = SpringUtil.getContext().getBean(ForecastTaskService.class);
    private static final JSONObject SYSTEM_FILTER_ID_CONFIG = new JSONObject();
    private static final String BY_DEPT = "byDept";
    private static final String BY_DATE = "byDate";
    private static final String BY_QUARTER = "byQuarter";
    private static final String BY_MONTH = "byMonth";
    private static final String SYSTEM_TENANT_ID = "-100";
    private static final JSONObject GRAY_FILTER_ID_CONFIG = new JSONObject();

    static {
        GRAY_FILTER_ID_CONFIG.put(SYSTEM_TENANT_ID, SYSTEM_FILTER_ID_CONFIG);
        // 前端byDate(按时间查看，已自动解析规则按月or按季分组) -> 后台使用bi-byDept(按主属部门分组报表)
        // 前端byDept(按组织查看) -> 规则按月分组 -> 后台使用bi-byMonth(按年按月分组报表)
        // 前端byDept(按组织查看) -> 规则按季分组 -> 后台使用bi-byQuarter(按年按季分组报表)
        initSystemFilterIdConfig();
        ConfigFactory.getConfig("fs-crm-sales-config", config -> {
            String str = config.get("forecast_task_filter_id_config_gray");
            if (str != null) {
                GRAY_FILTER_ID_CONFIG.putAll(JSON.parseObject(str));
                log.info("filter id gray config has updated");
            }
        });
    }

    private static void initSystemFilterIdConfig() {
        JSONObject defaultByDept = defaultFilterIdGroup(
                "BI_SYS_62b92b8b1c5b7a00012b7b63",
                "BI_SYS_62bc10721c5b7a00015f65bc",
                "BI_SYS_62bc11711c5b7a00015f65c7",
                "BI_SYS_62ba68e31c5b7a0001d2896e",
                "BI_SYS_62c2bcc4383e420001c075b7");
        JSONObject defaultByQuarter = defaultFilterIdGroup(
                "BI_SYS_62c3a882383e420001e8104c",
                "BI_SYS_62c3a882383e420001e81050",
                "BI_SYS_62c3a882383e420001e81052",
                "BI_SYS_62c3a882383e420001e81051",
                "BI_SYS_62c3a882383e420001e8104f");
        JSONObject defaultByMonth = defaultFilterIdGroup(
                "BI_SYS_62b92b3b1c5b7a00012b7b57",
                "BI_SYS_62c2d074383e42000127e2b0",
                "BI_SYS_62ba742f1c5b7a0001d28987",
                "BI_SYS_62ba694a1c5b7a0001d2897a",
                "BI_SYS_62c2d074383e42000127e2b1");
        SYSTEM_FILTER_ID_CONFIG.put(BY_DEPT, defaultByMonth);
        SYSTEM_FILTER_ID_CONFIG.put(BY_QUARTER, defaultByQuarter);
        SYSTEM_FILTER_ID_CONFIG.put(BY_MONTH, defaultByMonth);
        SYSTEM_FILTER_ID_CONFIG.put(BY_DATE, defaultByDept);
    }

    private static JSONObject defaultFilterIdGroup(String viewId, String ownDept, String monthGroup, String quarterGroup, String ruleId) {
        JSONObject defaultByDept = new JSONObject();
        defaultByDept.put("viewId", viewId);
        defaultByDept.put(DBRecord.DATA_OWN_DEPARTMENT, ownDept);
        defaultByDept.put(ForecastTaskConstants.Field.FORECAST_MONTH_GROUP, monthGroup);
        defaultByDept.put(ForecastTaskConstants.Field.FORECAST_QUARTER_GROUP, quarterGroup);
        defaultByDept.put(ForecastTaskConstants.Field.FORECAST_RULE_OBJECT_ID, ruleId);
        return defaultByDept;
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        Optional<IFilter> ruleFilter = searchTemplateQuery.getFilters().stream().filter(filter -> ForecastTaskConstants.Field.FORECAST_RULE_OBJECT_ID.equals(filter.getFieldName())).findAny();
        if (!ruleFilter.isPresent()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        IObjectData rule = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), ruleFilter.get().getFieldValues().get(0), SFAPreDefineObject.ForecastRule.getApiName());
        arg.setAdjustForecastTaskConfig(rule.get(ForecastRuleConstants.Field.ADJUST_FORECAST_TASK, String.class, ForecastRuleConstants.AdjustBy.DEFAULT.getValue()));
        arg.setAllowToAdjustForecastTask(rule.get(ForecastRuleConstants.Field.ALLOW_TO_ADJUST_FORECAST_TASK, Boolean.class, Boolean.FALSE));
        arg.setAllowToAdjustForecastGroup(rule.get(ForecastRuleConstants.Field.ALLOW_TO_ADJUST_FORECAST_GROUP, Boolean.class, Boolean.FALSE));
        if (arg.getManagerCondition() == null) {
            SearchUtil.fillFilterEq(searchTemplateQuery.getFilters(), IObjectData.OWNER, controllerContext.getUser().getUserId());
        } else {
            searchTemplateQuery.setPermissionType(0);
            Integer dateSplitType = rule.get(ForecastRuleConstants.Field.FORECAST_DATE_SPLIT_TYPE, Integer.class, ForecastRuleConstants.DateSplitType.BY_MONTH.getValue());
            @SuppressWarnings("unchecked")
            List<String> applyRange = rule.get(ForecastRuleConstants.Field.FORECAST_APPLY_RANGE, List.class, Collections.emptyList());
            ManagerCondition managerCondition = arg.getManagerCondition();
            managerCondition.setDateSplitType(dateSplitType);
            managerCondition.setApplyRange(applyRange);
            String dateGroup = managerCondition.getDateGroup();
            if (dateGroup != null) {
                if (ForecastRuleConstants.DateSplitType.BY_MONTH.getValue().equals(dateSplitType)) {
                    SearchUtil.fillFilterEq(searchTemplateQuery.getFilters(), ForecastTaskConstants.Field.FORECAST_MONTH_GROUP, dateGroup);
                } else if (ForecastRuleConstants.DateSplitType.BY_QUARTER.getValue().equals(dateSplitType)) {
                    SearchUtil.fillFilterEq(searchTemplateQuery.getFilters(), ForecastTaskConstants.Field.FORECAST_QUARTER_GROUP, dateGroup);
                } else {
                    throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
                }
            }
        }
        return searchTemplateQuery;
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> data;
        if (arg.getManagerCondition() == null) {
            data = super.findData(query);
            for (IObjectData task : data.getData()) {
                setDeptViewGroupResultLabel(task, task.get(ForecastTaskConstants.Field.FORECAST_DATE_SPLIT_TYPE, Integer.class));
            }
        } else {
            data = findDataWithGroup(arg.getManagerCondition(), query);
        }
        setAdjustable(data.getData());
        return data;
    }

    @Override
    protected void doFunPrivilegeCheck() {
        // Do nothing because of X and Y.
    }

    private QueryResult<IObjectData> findDataWithGroup(ManagerCondition managerCondition, SearchTemplateQuery query) {
        stopWatch.lap("findDataWithGroup begin");
        List<IObjectData> dataList = new ArrayList<>();
        int totalNumber1 = queryGroup(managerCondition, query, dataList);
        stopWatch.lap("query group end");
        int totalNumber2 = queryData(managerCondition, query, dataList);
        stopWatch.lap("query data end");
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        queryResult.setData(dataList);
        queryResult.setTotalNumber(Math.max(totalNumber1, totalNumber2));// 取最大值支撑继续翻页
        return queryResult;
    }

    private int queryGroup(ManagerCondition managerCondition, SearchTemplateQuery query, List<IObjectData> dataList) {
        if (managerCondition.getOwnerId() != null) {
            return 0;
        }
        processSubDeptIds(managerCondition);
        if (managerCondition.getSubDeptIds() != null && !managerCondition.getSubDeptIds().isEmpty()) {
            Optional<StatViewDataQuery.ResultData> queryGroupResult = queryGroup(managerCondition, (query.getOffset() / query.getLimit()) + 1, query.getLimit());
            if (queryGroupResult.isPresent()) {
                StatViewDataQuery.ResultData resultData = queryGroupResult.get();
                List<IObjectData> groupDataList = convertToObjectDataList(managerCondition, resultData);
                List<IObjectData> adjustedDataList = FORECAST_TASK_SERVICE.queryGroupAdjustedTask(controllerContext.getTenantId(), managerCondition.getRuleId());
                merge(groupDataList, adjustedDataList);
                dataList.addAll(groupDataList);
                return resultData.getPage().getIntValue("totalCount") - 1; // BI返回值最后一行数据是“总计”
            }
        }
        return 0;
    }

    private void processSubDeptIds(ManagerCondition managerCondition) {
        String deptId = managerCondition.getDeptId();
        if (BY_DEPT.equals(managerCondition.getViewType())) {
            managerCondition.setSubDeptIds(Collections.singletonList(deptId == null ? "999999" : deptId));
        } else if (BY_DATE.equals(managerCondition.getViewType())) {
            if (deptId == null) {
                managerCondition.setSubDeptIds(Collections.singletonList("999999"));
            } else {
                List<DepartmentBasedAccessControl.TreeNode> treeNodes = getSubDeptIdsByRule(deptId, managerCondition.getApplyRange());
                List<String> subDeptIds = treeNodes.stream().filter(n -> DepartmentBasedAccessControl.TreeNode.TYPE_DEPT.equals(n.getType())).map(DepartmentBasedAccessControl.TreeNode::getId).collect(Collectors.toList());
                managerCondition.setSubDeptIds(subDeptIds);
                managerCondition.setSubTreeNodes(treeNodes);
            }
        } else {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    private List<DepartmentBasedAccessControl.TreeNode> getSubDeptIdsByRule(String deptId, List<String> applyRange) {
        return FORECAST_TASK_SERVICE.getSubDeptByRule(controllerContext.getTenantId(), deptId, controllerContext.getUser(), applyRange);
    }

    private StatViewDataQuery.Arg buildQueryParam(ManagerCondition managerCondition, int pageNumber, int pageSize) {
        String viewType = managerCondition.getViewType();
        Integer dateSplitType = managerCondition.getDateSplitType();
        if (BY_DEPT.equals(viewType)) {
            if (ForecastRuleConstants.DateSplitType.BY_MONTH.getValue().equals(dateSplitType)) {
                viewType = BY_MONTH;
            } else if (ForecastRuleConstants.DateSplitType.BY_QUARTER.getValue().equals(dateSplitType)) {
                viewType = BY_QUARTER;
            } else {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
        }
        if (viewType == null) {
            viewType = BY_DEPT;
        }
        JSONObject filterIdMap = getFilterIdMap(viewType);
        StatViewDataQuery.Arg param = new StatViewDataQuery.Arg();
        param.setFilterList(buildQueryFilterList(managerCondition, filterIdMap));
        param.setViewId(filterIdMap.getString("viewId"));
        param.setShowMode(0); // 不显示明细
        param.setPageNumber(pageNumber);
        param.setPageSize(pageSize);
        return param;
    }

    // 优先级：租户级灰度 > 系统级灰度 > 系统级
    private JSONObject getFilterIdMap(String viewType) {
        JSONObject filterIdMap;
        String tenantId = controllerContext.getTenantId();
        if (GrayUtil.grayForecastTaskFilterId(tenantId)) {
            JSONObject tenantFilterIdGrayConfig = GRAY_FILTER_ID_CONFIG.getJSONObject(tenantId);
            if (tenantFilterIdGrayConfig == null) {
                tenantFilterIdGrayConfig = GRAY_FILTER_ID_CONFIG.getJSONObject(SYSTEM_TENANT_ID);
            }
            filterIdMap = tenantFilterIdGrayConfig.getJSONObject(viewType);
        } else {
            filterIdMap = SYSTEM_FILTER_ID_CONFIG.getJSONObject(viewType);
        }
        return filterIdMap;
    }

    private Optional<StatViewDataQuery.ResultData> queryGroup(ManagerCondition managerCondition, int pageNumber, int pageSize) {
        StatViewDataQuery.Arg param = buildQueryParam(managerCondition, pageNumber, pageSize);
        ForecastTaskModel.Arg forecastTaskModelArg = new ForecastTaskModel.Arg();
        forecastTaskModelArg.setBiParam(param);
        return FORECAST_TASK_SERVICE.queryGroup(ContextConvertUtils.controllerContext2ServiceContext(controllerContext), forecastTaskModelArg);
    }

    private List<IObjectData> convertToObjectDataList(ManagerCondition managerCondition, StatViewDataQuery.ResultData groupResultDataObject) {
        List<List<JSONObject>> dataSet = groupResultDataObject.getDataSet();
        if (dataSet == null || dataSet.isEmpty()) {
            return Collections.emptyList();
        }
        List<JSONObject> displayFields = groupResultDataObject.getDisplayFields();
        Map<String, Integer> displayFieldsMap = new HashMap<>();
        for (int i = 0; i < displayFields.size(); i++) {
            JSONObject displayField = displayFields.get(i);
            displayFieldsMap.put(displayField.getString("apiName"), i);
        }
        JSONObject page = groupResultDataObject.getPage();
        boolean isNotLastPage = page.getIntValue("pageSize") * page.getIntValue("pageNumber") < page.getIntValue("totalCount");
        List<IObjectData> result = new ArrayList<>(dataSet.size());
        String viewType = managerCondition.getViewType();
        if (BY_DEPT.equals(viewType)) {
            for (int i = 0; i < (isNotLastPage ? dataSet.size() : dataSet.size() - 1); i++) { // 判断是不是最后一页
                result.add(convertToDeptViewObjectData(displayFieldsMap, dataSet.get(i), managerCondition));
            }
        } else if (BY_DATE.equals(viewType)) {
            for (int i = 0; i < (isNotLastPage ? dataSet.size() : dataSet.size() - 1); i++) { // 判断是不是最后一页
                result.add(convertToDateViewObjectData(displayFieldsMap, dataSet.get(i), managerCondition));
            }
        } else {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        return result;
    }

    private IObjectData convertToDateViewObjectData(Map<String, Integer> displayFieldsMap, List<JSONObject> subArray, ManagerCondition managerCondition) {
        IObjectData objectData = new ObjectData();
        for (Map.Entry<String, Integer> entry : displayFieldsMap.entrySet()) {
            String key = entry.getKey();
            JSONObject subObject = subArray.get(entry.getValue());
            String formattedValue = subObject.getString("formattedValue");
            if ("owner_department".equals(key)) {
                String value = subObject.getString("value");
                String ownerDepartmentId = value.substring(0, value.length() - 2);
                objectData.set("owner_department_id", ownerDepartmentId); // 去掉bi返回结果的"-g"
                if ("999999".equals(ownerDepartmentId)) {
                    formattedValue = I18N.text("paas.udobj.company_wide"); // 全公司
                }
                objectData.set("owner_department", formattedValue);
                objectData.set(GROUP_RESULT_LABEL, formattedValue);
                objectData.set(ForecastTaskConstants.Field.ADJUST_ID, generateGroupResultId(ownerDepartmentId, managerCondition.getDateGroup(), managerCondition.getDateSplitType()));
            } else {
                objectData.set(key, formattedValue);
            }
        }
        objectData.set(ForecastTaskConstants.Field.IS_GROUP_RESULT, true);
        objectData.setId(serviceFacade.generateId());
        return objectData;
    }

    private IObjectData convertToDeptViewObjectData(Map<String, Integer> displayFieldsMap, List<JSONObject> subArray, ManagerCondition managerCondition) {
        IObjectData objectData = new ObjectData();
        for (Map.Entry<String, Integer> entry : displayFieldsMap.entrySet()) {
            String key = entry.getKey();
            String value = subArray.get(entry.getValue()).getString("formattedValue");
            objectData.set(key, value);
        }
        String dateGroup = getDateGroup(objectData, managerCondition.getDateSplitType());
        objectData.set(GROUP_RESULT_LABEL, dateGroup);
        objectData.set(ForecastTaskConstants.Field.IS_GROUP_RESULT, true);
        objectData.setId(serviceFacade.generateId());
        String deptId = managerCondition.getDeptId() == null ? "999999" : managerCondition.getDeptId(); // 织部门视图下首页查询条件deptId=null
        objectData.set(ForecastTaskConstants.Field.ADJUST_ID, generateGroupResultId(deptId, dateGroup, managerCondition.getDateSplitType()));
        return objectData;
    }

    private List<BiCrmRestQuery.Filter> buildQueryFilterList(ManagerCondition managerCondition, JSONObject filterIdConfig) {
        List<BiCrmRestQuery.Filter> filterList = new ArrayList<>();
        BiCrmRestQuery.Filter filter0 = new BiCrmRestQuery.Filter();
        filter0.setFilterId(filterIdConfig.getString(ForecastTaskConstants.Field.FORECAST_RULE_OBJECT_ID)); // 预测规则ID
        filter0.setValue1(managerCondition.getRuleId());
        filterList.add(filter0);
        JSONArray value1 = new JSONArray();
        for (String subDeptId : managerCondition.getSubDeptIds()) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("id", subDeptId);
            valueObj.put("type", "g");
            value1.add(valueObj);
        }
        if (!value1.isEmpty()) {
            BiCrmRestQuery.Filter filter = new BiCrmRestQuery.Filter();
            filter.setFilterId(filterIdConfig.getString(DBRecord.DATA_OWN_DEPARTMENT)); // 负责人主属部门
            filter.setValue1(value1.toJSONString());
            filterList.add(filter);
        }
        String dateGroup = managerCondition.getDateGroup();
        if (dateGroup != null) {
            BiCrmRestQuery.Filter filter = new BiCrmRestQuery.Filter();
            filter.setValue1(dateGroup);
            Integer dateSplitType = managerCondition.getDateSplitType();
            if (ForecastRuleConstants.DateSplitType.BY_MONTH.getValue().equals(dateSplitType)) {
                filter.setFilterId(filterIdConfig.getString(ForecastTaskConstants.Field.FORECAST_MONTH_GROUP)); // 按年按月分组
            } else if (ForecastRuleConstants.DateSplitType.BY_QUARTER.getValue().equals(dateSplitType)) {
                filter.setFilterId(filterIdConfig.getString(ForecastTaskConstants.Field.FORECAST_QUARTER_GROUP)); // 按年按季分组
            } else {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
            filterList.add(filter);
        }
        return filterList;
    }

    private int queryData(ManagerCondition managerCondition, SearchTemplateQuery query, List<IObjectData> dataList) {
        String viewType = managerCondition.getViewType();
        if (BY_DEPT.equals(viewType) && managerCondition.getOwnerId() != null) {
            SearchUtil.fillFilterIn(query.getFilters(), IObjectData.OWNER, managerCondition.getOwnerId());
            QueryResult<IObjectData> data = super.findData(query);
            setDeptViewGroupResultLabel(data, managerCondition.getDateSplitType());
            dataList.addAll(data.getData());
            return data.getTotalNumber();
        } else if (BY_DATE.equals(viewType)) {
            return querySubDeptData(managerCondition, query, dataList);
        } else {
            return 0;
        }
    }

    private int querySubDeptData(ManagerCondition managerCondition, SearchTemplateQuery query, List<IObjectData> dataList) {
        List<DepartmentBasedAccessControl.TreeNode> userInfoList = querySubDeptUserTreeNode(managerCondition);
        if (!userInfoList.isEmpty()) {
            List<String> userIds = new ArrayList<>(userInfoList.size());
            Map<String, DepartmentBasedAccessControl.TreeNode> userInfoMap = new HashMap<>();
            for (DepartmentBasedAccessControl.TreeNode treeNode : userInfoList) {
                userIds.add(treeNode.getId());
                userInfoMap.put(treeNode.getId(), treeNode);
            }
            SearchUtil.fillFilterIn(query.getFilters(), IObjectData.OWNER, userIds);
            QueryResult<IObjectData> data = super.findData(query);
            for (IObjectData objectData : data.getData()) {
                DepartmentBasedAccessControl.TreeNode treeNode = userInfoMap.get(objectData.getOwner().get(0));
                if (treeNode != null) {
                    objectData.set(GROUP_RESULT_LABEL, treeNode.getLabel());
                }
            }
            dataList.addAll(data.getData());
            return data.getTotalNumber();
        }
        return 0;
    }

    private List<DepartmentBasedAccessControl.TreeNode> querySubDeptUserTreeNode(ManagerCondition managerCondition) {
        String deptId = managerCondition.getDeptId();
        if (deptId != null && managerCondition.getSubTreeNodes() != null) {
            return managerCondition.getSubTreeNodes().stream().filter(node -> DepartmentBasedAccessControl.TreeNode.TYPE_USER.equals(node.getType())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private void setDeptViewGroupResultLabel(QueryResult<IObjectData> data, Integer dateSplitType) {
        for (IObjectData objectData : data.getData()) {
            setDeptViewGroupResultLabel(objectData, dateSplitType);
        }
    }

    private void setDeptViewGroupResultLabel(IObjectData objectData, Integer dateSplitType) {
        objectData.set(GROUP_RESULT_LABEL, getDateGroup(objectData, dateSplitType));
    }

    private void setAdjustable(List<IObjectData> dataList) {
        boolean allowToAdjustForecastGroup = arg.getAllowToAdjustForecastGroup() != null && arg.getAllowToAdjustForecastGroup();
        boolean allowToAdjustForecastTask = arg.getAllowToAdjustForecastTask() != null && arg.getAllowToAdjustForecastTask();
        setAdjustable(dataList, controllerContext.getUser().getUserId(), arg.getManagerCondition() != null, allowToAdjustForecastGroup, allowToAdjustForecastTask, ForecastRuleConstants.AdjustBy.from(arg.getAdjustForecastTaskConfig()));
    }

    @Override
    protected Result after(ForecastTaskListArg arg, Result result) {
        Result res = super.after(arg, result);
        Map<String, IObjectData> dataIdMap = queryResult.getData().stream().collect(Collectors.toMap(IObjectData::getId, Function.identity(), (v1, v2) -> v2));
        ButtonInfo buttonInfo = res.getButtonInfo();
        List<IUdefButton> udefButtons = serviceFacade.findCustomButtonByUsePage(SFAPreDefineObject.ForecastTask.getApiName(), getUsePageType(), controllerContext.getUser());
        List<String> buttonApiNames = udefButtons.stream().map(IUdefButton::getApiName).collect(Collectors.toList());
        if (buttonInfo.getButtons().isEmpty()) {
            buttonInfo.setButtons(udefButtons.stream().map(ButtonDocument::of).collect(Collectors.toList()));
        }
        for (Map.Entry<String, List<String>> entry : buttonInfo.getButtonMap().entrySet()) {
            IObjectData data = dataIdMap.get(entry.getKey());
            List<String> value = entry.getValue();
            if (ObjectDataExt.of(data).isLock()) {
                value.addAll(buttonApiNames);
            }
        }
        return res;
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }


    @Setter
    @Getter
    public static class ForecastTaskListArg extends StandardListController.Arg {
        private String ruleId;
        private ManagerCondition managerCondition;
        private Boolean allowToAdjustForecastTask;
        private String adjustForecastTaskConfig;
        private Boolean allowToAdjustForecastGroup;

        @Override
        public boolean equals(Object o) {
            return super.equals(o);
        }

        @Override
        public int hashCode() {
            return super.hashCode();
        }
    }

    @Setter
    @Getter
    public static class ManagerCondition {
        private String deptId;
        private String dateGroup;
        private String ruleId;
        private String viewType;
        private String ownerId;
        private Integer dateSplitType;
        private List<String> subDeptIds;
        private List<String> applyRange;
        private List<DepartmentBasedAccessControl.TreeNode> subTreeNodes;
    }
}
