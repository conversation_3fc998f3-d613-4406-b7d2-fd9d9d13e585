package com.facishare.crm.sfa.predefine.enums;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum InvoiceStatusEnum {
    No("no", "未开票"),
    Part("part", "部分开票"),
    All("all", "全部开票");
    ;
    private final String value;
    private final String label;

    InvoiceStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static InvoiceStatusEnum getByCode(String status) {
        for (InvoiceStatusEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static List<String> getAllStatus() {
        List<String> allStatus = Lists.newArrayList();
        for (InvoiceStatusEnum d : InvoiceStatusEnum.values()) {
            allStatus.add(d.getValue());
        }
        return allStatus;
    }
}
