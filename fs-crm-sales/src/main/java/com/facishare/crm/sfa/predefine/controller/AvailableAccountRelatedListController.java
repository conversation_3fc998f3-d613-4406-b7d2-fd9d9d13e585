package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.QueryUtils;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * 可售客户相关列表页
 *
 * <AUTHOR>
 */
public class AvailableAccountRelatedListController extends StandardRelatedListController {
    private AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        availableRangeUtils.handleALL(AvailableConstants.AccountResultField.ACCOUNT_ID,queryResult.getData());
        return result;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        QueryUtils.resetNameOrder2LastModifiedTime(query);
        return query;
    }
}
