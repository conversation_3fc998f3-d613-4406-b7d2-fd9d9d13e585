package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.core.util.RequestUtil;

public class AccountAddrListController extends StandardListController {

    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_680)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
       // AccountAddrUtil.changeButton(result);
        AccountAddrUtil.setButtonsOfData(controllerContext.getUser(),result, SFAPreDefineObject.AccountAddr.getApiName(),AccountAddrUtil.LIST_TYPE);
        return result;
    }
}
