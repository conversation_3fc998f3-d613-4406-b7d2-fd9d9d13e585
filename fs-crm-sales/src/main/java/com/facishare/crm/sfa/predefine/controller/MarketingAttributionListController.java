package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class MarketingAttributionListController extends StandardListController {

	private static final UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext()
			.getBean(UseRangeFieldDataRender.class);


	@Override
	protected Result after(Arg arg, Result result) {

		List<ObjectDataDocument> dataList = result.getDataList();
		if (CollectionUtils.isEmpty(dataList)) {
			return super.after(arg, result);
		}

		String tenantId = controllerContext.getTenantId();
		User user = new User(RequestContextManager.getContext().getTenantId(), User.SUPPER_ADMIN_USER_ID);

		// 触发对象的describeMap
		Map<String, IObjectDescribe> describeMap = dataList.stream()
				.map(data -> AccountUtil.getStringValue(data, "trigger_object_api_name", StringUtils.EMPTY))
				.distinct()
				.map(apiName -> serviceFacade.findObject(tenantId, apiName))
				.collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
		// 回溯数据describe
		IObjectDescribe marketingEventDescribe = serviceFacade
				.findObject(tenantId, SFAPreDefineObject.MarketingEvent.getApiName());

		// 添加转换数据
		for (ObjectDataDocument objectDataDocument : dataList) {
			String triggerObjectApiName = AccountUtil
					.getStringValue(objectDataDocument, "trigger_object_api_name", StringUtils.EMPTY);

			String triggerDataCondition = AccountUtil
					.getStringValue(objectDataDocument, "trigger_data_condition", StringUtils.EMPTY);
			objectDataDocument.replace("trigger_data_condition", useRangeFieldDataRender
					.resolveConditionByI18N(describeMap.get(triggerObjectApiName), triggerDataCondition, user));

			String retroactiveDataCondition = AccountUtil
					.getStringValue(objectDataDocument, "retroactive_data_condition", StringUtils.EMPTY);
			objectDataDocument.replace("retroactive_data_condition", useRangeFieldDataRender
					.resolveConditionByI18N(marketingEventDescribe, retroactiveDataCondition, user));
		}

		return super.after(arg, result);
	}
}
