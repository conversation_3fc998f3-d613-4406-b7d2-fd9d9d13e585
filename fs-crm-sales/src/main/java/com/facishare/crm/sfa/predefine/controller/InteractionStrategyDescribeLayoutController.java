package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.InteractionStrategyConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.DescribeUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IFormField;

public class InteractionStrategyDescribeLayoutController extends StandardDescribeLayoutController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        
        if (arg.getLayout_type() != null && LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            setUsedObjectApiNameReadOnly(result);
        }

        return result;
    }
    
    private void setUsedObjectApiNameReadOnly(Result result) {
        DescribeUtils.formFieldConsumer(result, fields -> {
            for (IFormField field : fields) {
                if (InteractionStrategyConstants.USED_OBJECT_API_NAME.equals(field.getFieldName())) {
                    field.setReadOnly(true);
                }
            }
        });
    }
}
