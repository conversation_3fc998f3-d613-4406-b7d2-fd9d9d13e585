package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.predefine.service.LayoutButtonService;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by renlb on 2018/12/14.
 */
@Slf4j
public class AccountListHeaderController extends BaseSFAListHeaderController {
    private final LayoutButtonService layoutButtonService = SpringUtil.getContext().getBean(LayoutButtonService.class);
    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        ILayout layout = new Layout((res.getLayout()));
        if (RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()) {
            layoutButtonService.removeListPageButton(result, ObjectAction.ACC_RENEW_EXPIRATION.getButtonApiName());
            addMobileButton(layout);
        }
        //修改 button
        ButtonUtils.editMergeButton(res);
        return res;
    }

    private void addMobileButton(ILayout layout) {
        if (!AccountUtil.isOpenManyOrganizations(controllerContext.getUser(), objectDescribeExt)) {
            layout.addButtons(ObjectAction.SCAN_CARD.createButton());
        }
    }
}
