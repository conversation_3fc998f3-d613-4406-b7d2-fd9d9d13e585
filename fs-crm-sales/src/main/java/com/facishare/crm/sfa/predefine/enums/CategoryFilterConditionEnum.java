package com.facishare.crm.sfa.predefine.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @time 2024-06-11 17:15
 * @Description
 */
public enum CategoryFilterConditionEnum {
    INCLUDE_BROTHER_NODE("include_brother_node", "包含兄弟节点"),
    INCLUDE_CHILDREN_NODE("include_children_node", "包含子节点"),
    INCLUDE_PARENT_NODE("include_parent_node", "包含父节点"),
    CHILDREN_INCLUDE_CHILDREN_NODE("brother_include_children_node", "");

    private final String condition;
    private final String describe;

    CategoryFilterConditionEnum(String condition, String describe) {
        this.condition = condition;
        this.describe = describe;
    }

    public static CategoryFilterConditionEnum getCategoryFilterCondition(String condition) {
        if (StringUtils.isBlank(condition)) {
            return null;
        }
        for (CategoryFilterConditionEnum e : values()) {
            if (e.condition.equals(condition)) {
                return e;
            }
        }
        return null;
    }
}
