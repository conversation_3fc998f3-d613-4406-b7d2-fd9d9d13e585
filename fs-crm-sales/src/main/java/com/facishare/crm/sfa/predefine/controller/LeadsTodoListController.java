package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.lto.utils.GrayUtil;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

public class LeadsTodoListController extends SFATodoListController {
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected void buildUnProcessedQuery(SearchTemplateQuery query) {
        LeadsUtils.handleSearchQuery(query, controllerContext.getUser(), arg.getSessionBOCItemKey());
        SearchListUtil.buildKeywordQuery(query, arg.getKeyword());
    }

    @Override
    protected void buildCustomUnProcessedQuery(SearchTemplateQuery searchQuery) {
        super.buildCustomUnProcessedQuery(searchQuery);
        List<IFilter> filters = LeadsUtils.getToDoListFilters(controllerContext.getUser(), arg.getSessionBOCItemKey());
        SearchListUtil.setSearchTemplateQueryPattern(searchQuery, filters);
        SearchListUtil.buildKeywordQuery(searchQuery, arg.getKeyword());
        searchQuery.setDataRightsParameter(null);
    }

    private void buildPerformanceQuery(SearchTemplateQuery query) {
        String tenantId = controllerContext.getTenantId();
        Integer type = getDealDataType();
        String userId = controllerContext.getUser().isOutUser() ? controllerContext.getUser().getOutUserId() : controllerContext.getUser().getUpstreamOwnerIdOrUserId();
        String querySql = String.format("SELECT DISTINCT data_id FROM crm_deal_data_relation " +
                "  WHERE ei='%s'  AND type='%s' AND employee_id='%s' LIMIT 2000 ", SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(String.valueOf(type)), SqlEscaper.pg_escape(userId));
        try {
            List<Map> result = objectDataService.findBySql(tenantId, querySql);
            if (result != null && !result.isEmpty()) {
                List<String> dataIds = Lists.newArrayList();
                for (Map map : result) {
                    dataIds.add(String.valueOf(map.get("data_id")));
                }
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterIn(filters, "_id", dataIds);
                query.setFilters(filters);
            }
        } catch (Exception e) {
            log.error("LeadsTodoListController error {}", tenantId, e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
    }

    @Override
    protected void buildCustomProcessedQuery(SearchTemplateQuery query) {
        String tenantId = controllerContext.getTenantId();
        if(GrayUtil.isGrayEnable("leads_todo_performance", tenantId)) {
            buildPerformanceQuery(query);
        } else {
            super.buildCustomProcessedQuery(query);
        }
    }
}
