package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.AvailableRangeImportUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AvailableRangeImportViewController extends StandardImportViewController {

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (arg.getImportType() == IMPORT_TYPE_ADD) {
            if (StringUtils.equals(describe.getApiName(), SFAPreDefineObject.AvailableRange.getApiName())) {
                AvailableRangeImportUtils.removeAndAddFields(controllerContext.getTenantId(), fieldDescribes);
            }
            if (StringUtils.equals(describe.getApiName(), SFAPreDefineObject.AvailableAccount.getApiName())
                    || StringUtils.equals(describe.getApiName(), SFAPreDefineObject.AvailableProduct.getApiName())) {
                AvailableRangeImportUtils.removeDetailFields(fieldDescribes);
            }
        }
    }
}
