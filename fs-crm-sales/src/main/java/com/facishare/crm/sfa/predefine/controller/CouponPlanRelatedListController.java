package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

public class CouponPlanRelatedListController extends StandardRelatedListController {

    @Override
    protected List<IButton> getButtons(ILayout layout) {
        if (RequestUtil.isMobileOrH5Request()) {
            return Lists.newArrayList();
        }
        return super.getButtons(layout);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        CouponUtils.setDefaultCouponType(result, controllerContext.getTenantId());
        return result;
    }
}