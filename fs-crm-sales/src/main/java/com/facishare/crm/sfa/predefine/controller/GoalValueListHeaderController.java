package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.enums.GoalEnum;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.GoalRoleConstants;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.GoalRuleApplyCircleObj;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.GoalRuleObj;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.GoalValueConstants;
import com.facishare.crm.sfa.predefine.service.GoalValue.service.GoalRuleCommonService;
import com.facishare.crm.sfa.predefine.service.GoalValue.service.GoalValueCommonService;
import com.facishare.crm.privilege.service.UserInfoService;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhaopx on 2018/4/13.
 */
public class GoalValueListHeaderController extends StandardListHeaderController {

    private final GoalRuleCommonService goalRuleCommonService = (GoalRuleCommonService) SpringUtil.getContext().getBean
            ("goalRuleCommonService");
    private final UserInfoService userInfoService = (UserInfoService) SpringUtil.getContext().getBean("userInfoService");
    private final GoalValueCommonService goalValueCommonService = (GoalValueCommonService) SpringUtil.getContext().getBean
            ("goalValueCommonService");

    private Integer startMonth = 0;
    private final List<String> showFields = Lists.newArrayList(
            GoalValueConstants.ID
            , GoalValueConstants.ANNUAL_VALUE
            , GoalValueConstants.CHECK_OBJECT_ID
            , GoalValueConstants.FISCAL_YEAR
    );
    private String themeApiName;
    private String checkCycle;
    private Integer startWeek = 0;
    private Integer startQuarter = 0;
    private int startYear = 1;


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (arg.getGoalRuleId().isEmpty()) {
            throw new ValidateException("goalRuleId不能为空");// ignoreI18n
        }
        IObjectData data = goalRuleCommonService.findGoalRule(controllerContext.getUser(), arg.getGoalRuleId());
        startMonth = data.get(GoalRuleObj.START_MONTH, Integer.class);
        themeApiName = data.get(GoalRuleObj.THEME_API_NAME, String.class);
        checkCycle = data.get(GoalRuleObj.CHECK_CYCLE, String.class);
        startWeek = data.get(GoalRuleObj.START_WEEK, Integer.class);
        startQuarter = data.get(GoalRuleObj.START_QUARTER, Integer.class);

        if(startMonth == null){
            startMonth = 1;
        }
    }

    @Override
    protected List<IButton> getButtons(){
        //目标值比较特殊，按钮需要综合详情+列表一起下发
        List<IButton> listButtons = super.getButtons();
        List<IButton> detailButtons = serviceFacade.getButtonByComponentActions(controllerContext.getUser(),
                ComponentActions.DETAIL_PAGE,
                objectDescribeExt.getObjectDescribe(),
                (IObjectData) null,true);
        List<IButton> retainButtons = Lists.newArrayList();
        List<String> retainButtonNames = Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode()
                , ObjectAction.BATCH_EXPORT.getActionCode()
                , ObjectAction.UPDATE.getActionCode());

        if (!CollectionUtils.isEmpty(listButtons)) {
            retainButtons.addAll(listButtons.stream()
                    .filter(button -> retainButtonNames.contains(button.getAction()))
                    .collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(detailButtons)){
            retainButtons.addAll(detailButtons.stream()
                    .filter(button -> retainButtonNames.contains(button.getAction()))
                    .collect(Collectors.toList()));
        }
        fillLockButtons(retainButtons);

        return retainButtons;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        fillFieldConfigList(arg, rst);
        filterFieldList(rst);
        List<DocumentBaseEntity> fieldList = reorderFieldList(rst.getFieldList(), startMonth);
        rst.setFieldList(fieldList);
        return rst;
    }

    private void filterFieldList(Result rst) {
        List<DocumentBaseEntity> fieldList = rst.getFieldList();
        if (CollectionUtils.isEmpty(fieldList)) {
            return;
        }
        List<DocumentBaseEntity> finalGoalValueFieldList;
        List<DocumentBaseEntity> originalGoalValueFieldList = fieldList.stream()
                                                               .filter(o -> getFieldName(o).endsWith("_value"))
                                                               .filter(o -> !"annual_value".equals(getFieldName(o)))
                                                               .collect(Collectors.toList());
        fieldList.removeAll(originalGoalValueFieldList);
        if(Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.WEEK.getValue())){
            finalGoalValueFieldList = originalGoalValueFieldList.stream()
                                                                .filter(o -> getFieldName(o).startsWith("week_"))
                                                                .collect(Collectors.toList());
        }else if (Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.QUARTER.getValue())) {
            finalGoalValueFieldList = originalGoalValueFieldList.stream()
                                                                .filter(o -> getFieldName(o).startsWith("quarter_"))
                                                                .collect(Collectors.toList());
        }else if (Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.YEAR.getValue())) {
            finalGoalValueFieldList = originalGoalValueFieldList.stream()
                                                                .filter(o -> getFieldName(o).startsWith("year_"))
                                                                .collect(Collectors.toList());
        }else {
            finalGoalValueFieldList = originalGoalValueFieldList.stream()
                                                                .filter(o -> !getFieldName(o).startsWith("year_"))
                                                                .filter(o -> !getFieldName(o).startsWith("quarter_"))
                                                                .filter(o -> !getFieldName(o).startsWith("week_"))
                                                                .collect(Collectors.toList());
        }
        fieldList.addAll(finalGoalValueFieldList);
    }

    private void fillLockButtons(List<IButton> buttons){
        boolean isAdmin = serviceFacade.isAdmin(controllerContext.getUser());
        List<String> userRoleList = serviceFacade.getUserRole(controllerContext.getUser());
        boolean isGoalRuleManager = !com.facishare.paas.appframework.common.util.CollectionUtils.empty(userRoleList) &&
          userRoleList.contains(GoalRoleConstants.GOAL_MANAGER_ROLE_CODE);

        if(isAdmin || isGoalRuleManager){
            String checkId;
            List<String> applyCircleIds = Lists.newArrayList();
            List<IObjectData> applyCircleData = goalRuleCommonService.findGoalRuleApplyCircle(controllerContext.getUser()
                    ,arg.getGoalRuleId());
            if(!CollectionUtils.isEmpty(applyCircleData)){
                applyCircleIds = applyCircleData.stream()
                        .map(data -> String.valueOf(data.get(GoalRuleApplyCircleObj.FIELD_APPLY_CIRCLE_ID)))
                        .collect(Collectors.toList());
            }
            boolean isLock = false;
            String goalValueType;
            if(StringUtils.isEmpty(themeApiName) || GoalEnum.ThemeTypeValue.PERSON.getValue().equals(themeApiName)){
                goalValueType = GoalEnum.GoalTypeValue.CIRCLE.getValue();
                checkId = applyCircleIds.get(0);
            }else {
                goalValueType = themeApiName;
                checkId = "";
            }
            if(!CollectionUtils.isEmpty(applyCircleIds)){
                isLock = goalValueCommonService.isLock(controllerContext.getUser()
                        ,arg.getGoalRuleId()
                        ,arg.getGoalRuleDetailId()
                        ,arg.getFiscalYear()
                        ,goalValueType
                        ,checkId);
            }
            if(isLock){
                IButton unlockButton = new Button();
                unlockButton.setLabel(ObjectAction.UNLOCK.getActionLabel());
                unlockButton.setAction(ObjectAction.UNLOCK.getActionCode());
                buttons.add(unlockButton);
            }else{
                IButton lockButton = new Button();
                lockButton.setLabel(ObjectAction.LOCK.getActionLabel());
                lockButton.setAction(ObjectAction.LOCK.getActionCode());
                buttons.add(lockButton);
            }
        }
    }

    private List<DocumentBaseEntity> reorderFieldList(List<DocumentBaseEntity> fieldList, Integer startMonth) {
        int startNum;
        List<DocumentBaseEntity> rst = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fieldList)) {
            return rst;
        }
        Map<Integer, String> data = goalRuleCommonService.getMonthData(checkCycle);
        Map<Integer, String> beforeData = new HashMap<>();
        Map<Integer, String> afterData = new HashMap<>();

        if(Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.WEEK.getValue())){
            startNum = startWeek;
        }else if (Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.QUARTER.getValue())){
            startNum = startQuarter;
        }else if (Objects.equals(checkCycle, GoalEnum.GoalCheckCycle.YEAR.getValue())) {
            startNum = startYear;
        }else {
            startNum = startMonth;
        }
        int finalStartNum = startNum;
        data.forEach((k, v) -> {
            if (k >= finalStartNum) {
                beforeData.put(k, v);
            } else {
                afterData.put(k, v);
            }
        });

        for (DocumentBaseEntity entity : fieldList) {
            String fieldName = getFieldName(entity);

            if (showFields.contains(fieldName)) {
                rst.add(entity);
            }
//            重新排序
            if (fieldName.equals(GoalValueConstants.ANNUAL_VALUE)) {
                List<Map<String, Object>> beforeList = Lists.newArrayList();
                beforeData.forEach((k, v) -> {
                    Map<String, Object> tmp = new HashMap<>();
                    tmp.put(v, true);
                    beforeList.add(tmp);
                });
                rst.addAll(beforeList.stream().map(DocumentBaseEntity::new).collect(Collectors.toList()));
                List<Map<String, Object>> afterList = Lists.newArrayList();
                afterData.forEach((k, v) -> {
                    Map<String, Object> tmp = new HashMap<>();
                    tmp.put(v, true);
                    afterList.add(tmp);
                });
                rst.addAll(afterList.stream().map(DocumentBaseEntity::new).collect(Collectors.toList()));
            }
        }
        return rst;
    }

    private String getFieldName(DocumentBaseEntity entity) {
        String fieldName = "";
        for (String s : entity.keySet()) {
            if("width".endsWith(s)){
                continue;
            }
            fieldName = s;
            break;
        }
        return fieldName;
    }

    private void fillFieldConfigList(Arg arg, Result ret) {
        if (Strings.isNullOrEmpty(arg.getRecordTypeAPIName())) {
            List<Map<String, Object>> fieldConfigList = infraServiceFacade.findFieldListConfig(controllerContext.getUser(),
                    objectDescribeExt.getApiName(), arg.getExtendAttribute());
            ret.setFieldList(layoutExt.getFieldShowList(fieldConfigList));
        }
    }
}
