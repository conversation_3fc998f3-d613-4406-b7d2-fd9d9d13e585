package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.InteractionStrategyConstants;
import com.facishare.crm.sfa.predefine.service.InteractionStrategyRenderService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetSnapShotForMod;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

/**
 * InteractionStrategySnapShotForMobController
 *
 * <AUTHOR>
 */
public class InteractionStrategySnapShotForMobController extends StandardSnapShotForMobController {
    private final InteractionStrategyRenderService interactionStrategyRenderService = SpringUtil.getContext().getBean(InteractionStrategyRenderService.class);

    @Override
    protected GetSnapShotForMod.Result doService(GetSnapShotForMod.Arg arg) {
        GetSnapShotForMod.Result result = super.doService(arg);
        render(result);
        return result;
    }

    private void render(GetSnapShotForMod.Result result) {
        Optional.ofNullable(result)
                .map(GetSnapShotForMod.Result::getData)
                .map(x -> ObjectDataExt.of(x).getObjectData())
                .ifPresent(data -> {
                            interactionStrategyRenderService.render(data, User.systemUser(controllerContext.getTenantId()), InteractionStrategyConstants.CONDITION);
                        }
                );
    }
}