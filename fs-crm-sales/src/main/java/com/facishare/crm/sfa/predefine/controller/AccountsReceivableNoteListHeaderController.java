package com.facishare.crm.sfa.predefine.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.utilities.constant.SpuConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.elasticsearch.common.util.set.Sets;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class AccountsReceivableNoteListHeaderController extends StandardListHeaderController {
    /**
     * todo 后面支持再放开
     * 过滤掉导入导出按键
     */
    @Override
    public Result after(Arg arg, Result result) {
        result = super.after(arg, result);

/*        if (Objects.nonNull(result.getLayout())) {
            LayoutExt layout = LayoutExt.of(result.getLayout());
            List<IButton> buttonList = layout.getButtons();
            buttonList = buttonList.stream()
                    .filter(button -> !Objects.equals(ObjectAction.BATCH_IMPORT.getActionCode(), button.getAction()))
                    .filter(button -> !Objects.equals(ObjectAction.BATCH_EXPORT.getActionCode(), button.getAction()))
                    .collect(Collectors.toList());
            *//*if (!GrayUtil.isAccountsReceivableOldTenants(controllerContext.getTenantId())) {
                buttonList = buttonList.stream()
                        .filter(button -> !Objects.equals(ObjectAction.CREATE.getActionCode(), button.getAction()))
                        .collect(Collectors.toList());
            }*//*
            layout.setButtons(buttonList);
            result.setLayout(LayoutDocument.of(layout));
        }*/
        result.setHasEditPermission(false);
        return result;
    }
    @Override
    protected List<String> getAuthorizedFields() {
        List<String> authorizedFields = super.getAuthorizedFields();
        authorizedFields.remove("periodic_source");
        return authorizedFields;
    }
}
