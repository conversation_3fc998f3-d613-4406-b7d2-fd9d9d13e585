package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class AccountAddrNewDetailController extends SFANewDetailController{

    @Override
    protected StandardDetailController.Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        StandardDetailController.Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null || newResult.getData() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        IObjectData accountData = arg.isFromRecycleBin() ? this.serviceFacade.findObjectDataIncludeDeleted(this.controllerContext.getUser(),
                String.valueOf(newResult.getData().get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName())), SFAPreDefineObject.Account.getApiName()) :
                this.serviceFacade.findObjectData(this.controllerContext.getUser(),
                        String.valueOf(newResult.getData().get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName())), SFAPreDefineObject.Account.getApiName());
        if (accountData == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_CONTACTNOTNULL, I18N.text("AccountObj.attribute.self.display_name")));
        }
        //用客户的生命状态和业务状态过滤一遍按钮
        List<IButton> hasPrivilege = layout.getButtons().stream().filter((x) -> {
            return AccountAddrUtil.checkLifeStatus(ObjectAction.of(x.getAction()), ObjectDataExt.of(accountData))
                    && AccountAddrUtil.checkLockStatus(ObjectAction.of(x.getAction()), ObjectDataExt.of(accountData), controllerContext.getUser());
        }).collect(Collectors.toList());
        layout.setButtons(hasPrivilege);
        return newResult;
    }
}
