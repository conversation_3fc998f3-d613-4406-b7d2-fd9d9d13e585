package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.MarketingAttributionUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class MarketingEventInfluenceListHeaderController extends StandardListHeaderController {
	@Override
	protected List<IButton> getButtons() {
		List<IButton> buttons = super.getButtons();
		if(CollectionUtils.isNotEmpty(buttons)){
			// 暂时只支持导出功能
			buttons.removeIf(b -> !ObjectAction.BATCH_EXPORT.getActionCode().equals(b.getAction()));
		}
		return buttons;
	}

	@Override
	protected Result after(Arg arg, Result result) {
		super.after(arg, result);
		if (result == null) {
			return result;
		}
		if(StringUtils.isNotBlank(arg.getTargetObjectApiName()) && result.getLayout() != null) {
			ILayout layout = result.getLayout().toLayout();
			MarketingAttributionUtil.specialLayout(controllerContext.getObjectApiName(), arg.getTargetObjectApiName(), controllerContext.getUser(), layout);
		}
		return result;
	}
}
