package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.BiddingRuleService;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.UseRangeFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_BIDDING_SUBSCRIPTING_MOBILE_NOT_SUPPORT;


public class BiddingSubscriptionWebDetailController extends SFAWebDetailController {

    private static final BiddingRuleService BIDDING_RULE_SERVICE = SpringUtil.getContext().getBean(BiddingRuleService.class);

    private static final UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext().getBean(UseRangeFieldDataRender.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (RequestUtil.isMobileRequest() || RequestUtil.isH5MobileRequest()) {
            throw new ValidateException(I18N.text(SFA_BIDDING_SUBSCRIPTING_MOBILE_NOT_SUPPORT));
        }
    }


    @Override
    protected Result doService(Arg arg) {
        return super.doService(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (result.getLayout() != null) {
            Optional.ofNullable(result.getData()).ifPresent(data -> {
                describe.getFieldDescribes().stream().filter(k -> IFieldType.UseRange.equals(k.getType()))
                        .forEach(k -> {
                            data.put(k.getApiName(),
                                    useRangeFieldDataRender.render(data.get(k.getApiName()),
                                            ((UseRangeFieldDescribe) k).getTargetApiName()));
                        });
            });
        }
        return super.after(arg, result);
    }
}
