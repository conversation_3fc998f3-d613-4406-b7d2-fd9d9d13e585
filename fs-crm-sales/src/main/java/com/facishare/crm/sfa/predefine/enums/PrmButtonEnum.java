package com.facishare.crm.sfa.predefine.enums;

import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;

/**
 * <AUTHOR>
 * @date 2021/4/12 10:55
 */
public enum PrmButtonEnum {
    OWNER("owner", "企信联系负责人"),
    ADMIN("admin", "企信联系管理员"),
    CHANGE_PARTNER_OWNER("change_partner_owner", "成为外部负责人"),
    ADD_TEAM_MEMBER("add_team_member", "成为团队成员");
    private String apiName;
    private String label;
    private GetResult.Button button;

    PrmButtonEnum(String apiName, String label) {
        this.apiName = apiName;
        this.label = label;
        this.button = GetResult.Button.builder()
                .apiName(this.apiName)
                .label(this.label)
                .build();
    }

    public String getApiName() {
        return apiName;
    }

    public String getLabel() {
        return label;
    }

    public GetResult.Button getResultButton() {
        return this.button;
    }

}
