package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.common.util.ObjectAction.CREATE;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;

/**
 * Created by zhaopx on 2017/11/15.
 */
@Slf4j
public class AccountDescribeLayoutController extends SFADescribeLayoutController {

    private static final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext ().getBean ("functionPrivilegeService", FunctionPrivilegeService.class);
    private static final ConfigService configService = SpringUtil.getContext ().getBean ("configService", ConfigService.class);
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout (arg, result);
        if (arg.getLayout_type () == null) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        LayoutExt layoutExt = LayoutExt.of(layout);
        switch (arg.getLayout_type ()) {
            case LayoutExt.Add_LAYOUT_TYPE:
                if (!controllerContext.isFromOpenAPI ()) {
                    //负责人必填
                    setRequired(layoutExt,Lists.newArrayList ("owner"),true);
                }
                //region 开启多组织逻辑
                if (AccountUtil.isOpenManyOrganizations (controllerContext.getUser (), describe)) {
                    //客户名称新建编辑时只读
                    setReadOnly(layoutExt, Lists.newArrayList ("name"), true);
                    AccountUtil.settingMarkOfCreateAccountBeforeCreateMainDataByAdd_LAYOUT_TYPE(describe,result,controllerContext.getUser());
                }
                //endregion

                boolean isHaveCreateFunc = functionPrivilegeService.funPrivilegeCheck (controllerContext.getUser (),
                        Utils.CONTACT_API_NAME, CREATE.getActionCode ());
                if (!isHaveCreateFunc) {
                    WebDetailLayout.of (layout).removeButtonsByActionCode (Lists.newArrayList (ObjectAction.CREATE_SAVE_CREATE_CONTACT.getActionCode ()));
                }
                handleDetailLayout (arg.getLayout_type (), result);
                break;
            case LayoutExt.Edit_LAYOUT_TYPE:
                PreDefLayoutUtil.specialDealAccountObjForLeadsId (formComponent, objectData);
                //region 开启多组织逻辑
                if (AccountUtil.isOpenManyOrganizations (controllerContext.getUser (), describe)) {
                    //客户名称、主客户、归属组织编辑时只读
                    setReadOnly(layoutExt, Lists.newArrayList ("account_main_data_id", "name", "data_own_organization"), true);
                    AccountUtil.settingMarkOfCreateAccountBeforeCreateMainDataByEdit_LAYOUT_TYPE(objectData,describe,result,controllerContext.getUser(),arg);
                }else{
                    if(!getAllowUpdataNameConfig(controllerContext.getUser())){
                        setReadOnly(layoutExt, Lists.newArrayList ("account_main_data_id", "name", "data_own_organization"), true);
                    }
                }
                //endregion
                handleDetailLayout (arg.getLayout_type (), result);
                break;
            default:
                break;
        }
    }

    private void handleDetailLayout(String layoutType, Result result) {
        if(AccountUtil.isOpenAccountAddrConfig(controllerContext.getUser())){
            log.warn("isOpenAccountAddrConfig is 1");
        }else{
            handleDetailLayout (result, "account_accountaddr_list", Lists.newArrayList ("is_default_add", "is_ship_to_add"), layoutType);
        }
        handleDetailLayout (result, "account_accountfininfo_list", Lists.newArrayList ("is_default"), layoutType);
    }

    private void handleDetailLayout(Result result, String relatedListName, List<String> readOnlyFields, String layoutType) {
        if (result == null || CollectionUtils.empty (readOnlyFields)) {
            return;
        }
        List<DetailObjectListResult> detailObjectListResultList = result.getDetailObjectList ();
        if (CollectionUtils.empty (detailObjectListResultList)) {
            return;
        }
        Optional<DetailObjectListResult> detailObjectListResultOptional = detailObjectListResultList.stream ()
                .filter (it -> relatedListName.equals (it.getRelatedListName ()))
                .findFirst ();
        if (!detailObjectListResultOptional.isPresent ()) {
            return;
        }

        List<RecordTypeLayoutStructure> detailLayoutList = detailObjectListResultOptional.get ().getLayoutList ();
        if (CollectionUtils.notEmpty (detailLayoutList)) {
            for (RecordTypeLayoutStructure recordTypeLayoutStructure : detailLayoutList) {
                ILayout detailLayout = new Layout(recordTypeLayoutStructure.getDetail_layout());
                LayoutExt detailLayoutExt = LayoutExt.of(detailLayout);
                detailLayoutExt.getFormComponent().ifPresent(c -> {
                    FormComponent detailFormComponent =  (FormComponent) c.getFormComponent();
                    if (LAYOUT_TYPE_ADD.equals(layoutType)) {
                        PreDefLayoutUtil.removeSomeFields(detailFormComponent, Sets.newHashSet(readOnlyFields));
                        PreDefLayoutUtil.removeSomeFields(detailFormComponent, Sets.newHashSet("contact_id"));
                    } else {
                        PreDefLayoutUtil.setFormComponentFieldReadOnly(detailFormComponent, readOnlyFields);
                    }
                });

                ILayout listLayout = new Layout(recordTypeLayoutStructure.getList_layout());
                LayoutExt listLayoutExt = LayoutExt.of(listLayout);
                if ("account_accountaddr_list".equals(relatedListName) && LAYOUT_TYPE_ADD.equals(layoutType)) {
                    listLayoutExt.getTableComponent().ifPresent(tableComponent -> {
                        List<ITableColumn> fields = tableComponent.getIncludeFields();
                        fields.removeIf(field -> Objects.equals(field.getName(), "contact_id"));
                        fields.removeIf(field -> readOnlyFields.contains(field.getName()));
                        tableComponent.setIncludeFields(fields);
                    });

                    List<IButton> detailButtonList = detailLayoutExt.getButtons();
                    detailButtonList.removeIf(m -> "Batch_Lookup_Add_button_contact_id".equals(m.getName()));
                    detailLayoutExt.setButtons(detailButtonList);

                    List<IButton> listButtonList = listLayoutExt.getButtons();
                    listButtonList.removeIf(m -> "Batch_Lookup_Add_button_contact_id".equals(m.getName()));
                    listLayoutExt.setButtons(listButtonList);
                }
            }
        }
    }

    private void setReadOnly(LayoutExt layoutExt, List<String> fieldApiNames, boolean readOnly) {
        fieldApiNames.stream().forEach(x->{
            Optional<IFormField> nameField = layoutExt.getField(x);
            nameField.ifPresent(iFormField -> iFormField.setReadOnly(readOnly));
        });
    }

    private void setRequired(LayoutExt layoutExt, List<String> fieldApiNames, boolean required) {
        fieldApiNames.stream().forEach(x->{
            Optional<IFormField> nameField = layoutExt.getField(x);
            nameField.ifPresent(iFormField -> iFormField.setRequired(required));
        });
    }

    private boolean getAllowUpdataNameConfig(User user){
        String queryRst = configService.findTenantConfig(user, ConfigType.IS_ALLOWED_TO_EDIT_CUSTOMER_NAME.getKey());
        if(Strings.isNullOrEmpty(queryRst) || "0".equals(queryRst)){
            return false;
        }
        return true;
    }
}
