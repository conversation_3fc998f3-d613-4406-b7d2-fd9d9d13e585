package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.LeadsTransferLogUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedDuplicateSearchController;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetRelatedResults;

public class ContactRelatedDuplicateSearchController extends StandardRelatedDuplicateSearchController {
    @Override
    protected GetRelatedResults.Result after(GetRelatedResults.Arg arg, GetRelatedResults.Result result) {
        result = super.after(arg, result);
        String dataId = arg.getObjectData().getId();
        LeadsTransferLogUtil.dealDuplicatedDataForList(controllerContext.getUser(), controllerContext.getObjectApiName(), dataId, result);
        return  result;
    }
}

