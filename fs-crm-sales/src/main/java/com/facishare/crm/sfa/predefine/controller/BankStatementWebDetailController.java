package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.receivedpayment.ReceivedPaymentCoreService;
import com.facishare.crm.sfa.predefine.service.receivedpayment.ReceivedPaymentCoreServiceImpl;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class BankStatementWebDetailController extends SFAWebDetailController {
    ReceivedPaymentCoreService receivedPaymentCoreService = SpringUtil.getContext().getBean(ReceivedPaymentCoreServiceImpl.class);
    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        if (Objects.isNull(rst.getLayout())) {
            return rst;
        }
        ILayout layout = new Layout(result.getLayout());

        processButton(layout, data);

        return rst;
    }

    private void processButton(ILayout layout, IObjectData data) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
        if (!headInfoComponentOp.isPresent()) {
            return;
        }
        List<IButton> buttons = headInfoComponentOp.get().getButtons();
        IObjectData receivedPayment = receivedPaymentCoreService.getReceivedPaymentByBankStatementId(controllerContext.getUser(), data.getId());
        if (Objects.nonNull(receivedPayment)) {
            buttons.removeIf(b -> b.getAction().equals(ObjectAction.IDENTIFY_ACCOUNT.getActionCode()));
        } else {
            Iterator<IButton> iterator = buttons.iterator();
            while (iterator.hasNext()) {
                IButton button = iterator.next();
                if ("IdentifyAccount_button_default".equals(button.getName())) {
                    iterator.remove(); // 使用迭代器的remove()方法来安全删除
                    buttons.add(0, button); // 然后将元素添加到列表开头
                    break; // 如果只移动第一个匹配项，则找到后跳出循环
                }
            }
        }
        WebDetailLayout.of(layout).setButtons(buttons);
    }
}
