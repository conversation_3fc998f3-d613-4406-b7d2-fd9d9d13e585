package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import org.apache.commons.lang3.StringUtils;

public class AccountNewLogInfoListForWebController extends SFANewLogInfoListForWebController {
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogInfo logInfo = record.getLogInfo();
        if (logInfo == null) {
            return super.modifyRecordToLogRecord(record);
        }
        String textMessage = logInfo.getTextMessage();
        if (StringUtils.isBlank(textMessage)) {
            return super.modifyRecordToLogRecord(record);
        }
        String subStr = "[master-data-app]";
        if (textMessage.contains(subStr)) {
            int startIndex = textMessage.indexOf(subStr);
            if (startIndex == -1) {
                return super.modifyRecordToLogRecord(record);
            }
            String newLabel = textMessage.substring(startIndex + subStr.length());
            String originalOperationLabel = record.getOperationLabel();
            record.setOperationLabel(originalOperationLabel + newLabel);
        }
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        // 处理工作流和审批流的退回转移动作
        LogUtil.moveAndReturnFlowRecord(record, logRecord);
        return logRecord;
    }
}
