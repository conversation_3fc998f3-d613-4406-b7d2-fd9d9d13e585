package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;


public class BomCoreInstanceWebDetailController extends SFAWebDetailController {
    private static final List<String> ACTIONS = Lists.newArrayList(
            ObjectAction.INVALID.getActionCode(),
            ObjectAction.LOCK.getActionCode(),
            ObjectAction.UNLOCK.getActionCode(),
            ObjectAction.CONFIRM.getActionCode(),
            ObjectAction.REJECT.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.CLONE.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.CREATE.getActionCode(),
            ObjectAction.PRINT.getActionCode(),
            ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(),
            ObjectAction.CHANGE_PARTNER.getActionCode(),
            ObjectAction.DELETE_PARTNER.getActionCode(),
            ObjectAction.ENTER_ACCOUNT.getActionCode(),
            ObjectAction.CANCEL_ENTRY.getActionCode(),
            ObjectAction.ADD_TO_PRICEBOOK.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        removeDetailButtons(layout);
        if(Objects.equals(arg.getLayoutAgentType(), "mobile")){
            removeMobileButtons(layout);
        }
        return newResult;
    }

    private void removeMobileButtons(ILayout layout){
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(button -> ACTIONS.contains(button.getAction()));
        layout.setButtons(buttons);
    }

    private void removeDetailButtons(ILayout layout) {
        WebDetailLayout.of(layout).removeButtonsByActionCode(ACTIONS);
    }

}
