package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.CampaignMembersConstants;
import com.facishare.crm.sfa.utilities.util.CampaignMembersUtil;
import com.facishare.crm.sfa.utilities.util.ObjectUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by renlb on 2018/11/28.
 */
public class CampaignMembersListHeaderController extends StandardListHeaderController {

    private final List<String> listHeaderFieldApiNames = Lists.newArrayList(CampaignMembersConstants.Field.ACCOUNT_ID,
            CampaignMembersConstants.Field.CONTACT_ID, CampaignMembersConstants.Field.LEADS_ID,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_LEADS_COM_NAME,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_CONTACT_COM_NAME,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_ACCOUNT_COM_NAME,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_LEADS_JOB_TITLE,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_CONTACT_JOB_TITLE,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_LEADS_OWNER,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_CONTACT_OWNER,
            CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_ACCOUNT_OWNER);

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (result == null) {
            return result;
        }
        if (result.getLayout() != null && arg != null) {
            ILayout layout = result.getLayout().toLayout();
            List<IButton> buttonList = layout.getButtons();
            if (StringUtils.isBlank(arg.getTargetObjectApiName())) {
                if (CollectionUtils.notEmpty(buttonList)) {
                    buttonList.removeIf(m -> "Add".equals(m.getAction()) || "ExportFile".equals(m.getAction()));
                    layout.setButtons(buttonList);
                }
            } else {
                CampaignMembersUtil.specialLayout(arg.getTargetObjectApiName(), controllerContext.getUser(), layout);
                if (CollectionUtils.notEmpty(arg.getRelatedListComponent()) && arg.getRelatedListComponent().containsKey(ListComponentExt.BUTTON_INFO)) {
                    CampaignMembersUtil.specialButton(arg.getTargetObjectApiName(), controllerContext.getUser(), layout, arg.getRelatedListComponent());
                }
            }
        }
        if (CollectionUtils.notEmpty(result.getButtons()) && arg != null) {
            if (ObjectUtils.allNotEmpty(arg.getTargetObjectApiName()) && CampaignMembersUtil.referenceObjectApiNames.contains(arg.getTargetObjectApiName())) {
                result.setButtons(Lists.newArrayList());
            }
        }
        if (CollectionUtils.notEmpty(result.getTemplates())) {
            List<QueryTemplateDocument> templates = result.getTemplates();
            for (QueryTemplateDocument template : templates) {
                List<Map> fieldList = (List<Map>) template.get("field_list");
                if (CollectionUtils.notEmpty(fieldList)) {
                    //2022年830版本支持这些字段
                    //fieldList.removeIf(x -> listHeaderFieldApiNames.contains(x.get("field_name").toString()));
                    template.put("field_list", fieldList);
                }
            }
            result.setTemplates(templates);
        }

        return result;
    }
}
