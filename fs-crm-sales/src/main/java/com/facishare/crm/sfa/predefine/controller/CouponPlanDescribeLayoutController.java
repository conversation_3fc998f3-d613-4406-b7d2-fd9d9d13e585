package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.config.CouponProductConditionConfig;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

public class CouponPlanDescribeLayoutController extends SFADescribeLayoutController {
    private final CouponProductConditionConfig couponProductConditionConfig = SpringUtil.getContext().getBean(CouponProductConditionConfig.class);

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        if (LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            setCannotEditFields(arg, result);
        }
        issueProductConditionType(result);
        Optional<List<SelectOption>> selectOptionOpt = couponProductConditionConfig.getSelectOption(controllerContext.getTenantId());
        if (!selectOptionOpt.isPresent()) {
            return;
        }
        Optional.of(result).map(Result::getObjectDescribe)
                .map(ObjectDescribeDocument::toObjectDescribe)
                .map(IObjectDescribe::getFieldDescribeMap)
                .map(fieldDescribeMap -> fieldDescribeMap.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName()))
                .ifPresent(typeDescribe -> {
                    SelectOneFieldDescribe typeSelectOneFieldDescribe = (SelectOneFieldDescribe) typeDescribe;
                    for (SelectOption selectOption : selectOptionOpt.get()) {
                        typeSelectOneFieldDescribe.addSelectOption(selectOption);
                    }
                });
    }

    /**
     * 对产品条件类型 特殊处理，在描述里，此字段不是必填的，因为复杂产品，这个选项不再option中，设置为必填的时候，保存就会报选项不存在
     * 但是在布局中，却可以隐藏此字段，前端不下发此字段就会报错，所以，不管此字段如果被隐藏都要进行下发
     *
     * @param result 结果
     */
    private void issueProductConditionType(Result result) {
        CouponUtils.formFieldConsumer(result, fields -> {
            boolean hasProductConditionType = fields.stream().anyMatch(field -> CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName().equals(field.getFieldName()));
            if (hasProductConditionType) {
                return;
            }
            FormField productConditionTypeField = new FormField();
            productConditionTypeField.setFieldName(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName());
            productConditionTypeField.setRenderType(IFieldType.SELECT_ONE);
            productConditionTypeField.setRequired(true);
            productConditionTypeField.setReadOnly(false);
            fields.add(productConditionTypeField);
        });
    }

    /**
     * 不支持保存草稿
     *
     * @return boolean
     */
    @Override
    protected boolean supportSaveDraft() {
        return false;
    }

    /**
     * 设置已经产生优惠券实例，无法编辑字段
     *
     * @param result 结果
     */
    private void setCannotEditFields(Arg arg, Result result) {
        String dataId = arg.getData_id();
        if (StringUtils.isBlank(dataId)) {
            return;
        }
        //没有优惠券实例，则返回
        Set<String> hasCouponInstanceIds = CouponUtils.hasCouponInstance(controllerContext.getUser(), Lists.newArrayList(dataId));
        if (CollectionUtils.empty(hasCouponInstanceIds)) {
            return;
        }
        Consumer<List<IFormField>> consumer = fields -> {
            for (IFormField field : fields) {
                if (CouponConstants.EDIT_CAN_MODIFY_FIELD.contains(field.getFieldName())) {
                    continue;
                }
                if (StringUtils.isNotBlank(field.getFieldName()) && field.getFieldName().endsWith("__c")) {
                    continue;
                }
                //如果不是可编辑字段，设置只读
                field.setReadOnly(true);
            }
        };
        Optional.ofNullable(result)
                .map(StandardDescribeLayoutController.Result::getLayout)
                .map(LayoutExt::of)
                .flatMap(LayoutExt::getFormComponent)
                .ifPresent(x -> {
                    FormComponentExt ext = FormComponentExt.of(x);
                    List<IFieldSection> fieldSections = ext.getFieldSections();
                    for (IFieldSection fieldSection : fieldSections) {
                        if (!("base_field_section__c".equals(fieldSection.getName()) || "sysinfo_section__c".equals(fieldSection.getName()))) {
                            continue;
                        }
                        List<IFormField> fields = fieldSection.getFields();
                        if (CollectionUtils.empty(fields)) {
                            return;
                        }
                        consumer.accept(fields);
                        fieldSection.setFields(fields);
                    }
                    ext.setFieldSections(fieldSections);
                });
    }
}
