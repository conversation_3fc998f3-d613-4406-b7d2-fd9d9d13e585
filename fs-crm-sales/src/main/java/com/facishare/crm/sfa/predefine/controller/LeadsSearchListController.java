package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.InvisibleFieldUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardSearchListController;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

public class LeadsSearchListController extends StandardSearchListController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        AccountUtil.handleRemainingTime(objectDataList);
        handleMemberInvisibleFields(objectDataList);
        result.setDataList(ObjectDataDocument.ofList(objectDataList));
        return result;
    }

    //处理线索池普通成员不可见字段
    private void handleMemberInvisibleFields(List<IObjectData> objectDataList) {
        if(CollectionUtil.isEmpty(objectDataList)) {
            return;
        }
        List<String> poolIds = LeadsUtils.getPoolIds(objectDataList);
        if(CollectionUtil.isEmpty(poolIds)) {
            return;
        }
        Map<String, ObjectPoolPermission.ObjectPoolPermissions> poolPermissionsMap = SearchListUtil.getPoolPermission(controllerContext.getUser(), controllerContext.getObjectApiName(), poolIds);
        if(CollectionUtils.empty(poolPermissionsMap)) {
            return;
        }

        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
        if (clientInfo == null) {
            clientInfo = "";
        }
        for (String poolId : poolIds) {
            List<IObjectData> poolDataList = objectDataList.stream().filter(x -> poolId.equals(LeadsUtils.getPoolId(x)) && !AccountUtil.hasOwner(x)).collect(Collectors.toList());
            if(CollectionUtils.empty(poolDataList)) {
                continue;
            }
            ObjectPoolPermission.ObjectPoolPermissions poolPermissions = poolPermissionsMap.get(poolId);
            if (Objects.equals(poolPermissions, ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER)) {
                InvisibleFieldUtil.handleMemberInvisibleFields(controllerContext.getUser(), poolDataList,
                        "17", poolId, clientInfo);
            }
        }
    }
}
