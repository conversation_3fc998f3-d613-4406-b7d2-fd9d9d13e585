package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;

public class AccountTodoListController extends SFATodoListController {

    @Override
    protected void buildUnProcessedQuery(SearchTemplateQuery query) {
        AccountUtil.handleSearchQuery(query, controllerContext.getUser());
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
        List<IFilter> filters = AccountUtil.getToDoListFilters(controllerContext.getUser());
        SearchListUtil.setSearchTemplateQueryPattern(searchQuery, filters);
        searchQuery.setPermissionType(0);
        return searchQuery;
    }
}
