package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.attributepricebook.AttributePriceBookServiceImpl;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * 属性价目表明细列表
 *
 * <AUTHOR>
 */
public class AttributePriceBookLinesListController extends StandardListController {

    AttributePriceBookServiceImpl attributePriceBookService = SpringUtil.getContext().getBean(AttributePriceBookServiceImpl.class);

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> dataQueryResult = super.getQueryResult(query);
        attributePriceBookService.assembleObjectDataDocument(controllerContext.getUser(), dataQueryResult.getData());
        return dataQueryResult;
    }
}
