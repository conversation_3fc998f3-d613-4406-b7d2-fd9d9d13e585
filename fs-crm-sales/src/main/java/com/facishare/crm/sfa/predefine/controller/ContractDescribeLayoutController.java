package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;

import java.util.ArrayList;
import java.util.List;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

/**
 * Created by zhaopx on 2017/11/15.
 */
public class ContractDescribeLayoutController extends SFADescribeLayoutController {
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        switch (arg.getLayout_type()) {
            case LAYOUT_TYPE_EDIT:
                //编辑时需要将客户置为只读
                List<String> readonlyFieldNames = new ArrayList<String>();
                readonlyFieldNames.add("account_id");
                PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, readonlyFieldNames);
                break;
            default:
                break;
        }
    }
}
