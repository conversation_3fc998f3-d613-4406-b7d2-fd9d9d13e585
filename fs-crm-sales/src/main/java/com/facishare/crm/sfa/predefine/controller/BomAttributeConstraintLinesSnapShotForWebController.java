package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.model.BomConstraintLineModel;
import com.facishare.crm.sfa.utilities.util.ProductConstraintUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;


public class BomAttributeConstraintLinesSnapShotForWebController extends StandardSnapShotForWebController {

    private static final List<String> fields = Lists.newArrayList("condition_range", "result_range");
    @Override
    protected void fillFieldInfo(User user, IObjectDescribe objectDescribe, Map<String, Object> objData) {
        super.fillFieldInfo(user, objectDescribe, objData);
        fields.forEach(x->{
            Object obj = objData.get(x);
            if (Objects.nonNull(obj)) {
                List<BomConstraintLineModel> bomConstraint = JSON.parseArray(obj.toString(), BomConstraintLineModel.class);
                if (CollectionUtils.isNotEmpty(bomConstraint)) {
                    objData.put(x,ProductConstraintUtil.constraintProcess(bomConstraint));
                }
            }
        });

    }
}
