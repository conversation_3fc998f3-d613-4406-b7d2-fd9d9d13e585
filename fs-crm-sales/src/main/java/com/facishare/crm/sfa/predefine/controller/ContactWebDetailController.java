package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.PartnerConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

/**
 * Created by quzf  合作伙伴联系人业务类型的特殊过滤
 */
@Slf4j
public class ContactWebDetailController extends SFAWebDetailController {
    private static final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataDocument objectData = result.getData();
        if (objectData != null) {
            //web处理是否决策人这个字段类型时，无法采用字符串方式
            Object primaryContact = objectData.get("primary_contact");
            if (primaryContact != null && primaryContact instanceof Integer) {
                objectData.put("primary_contact", String.valueOf(primaryContact));
                if (Objects.equals(primaryContact, 0)) {
                    objectData.put("primary_contact", "");
                }
            }
            handleInvalidBirthDayField(result);
            handleInvalidCardField(result);
        }

        if (result.getData() != null) {
            ContactUtil.concatenateBirthDay(Lists.newArrayList(result.getData().toObjectData()));
        }
        if (result.getLayout() == null) {
            return result;
        }
        ILayout layout = new Layout(result.getLayout());
        specialLogicForLayout(layout);

        //1、如果是合作伙伴业务类型，布局中隐藏客户字段、外部来源、外部负责人字段,同时相关对象列表中移除预制的关联联系人的页签
        //2、如果不是合作伙伴业务类型，布局中隐藏合作伙伴字段
        if (PartnerConstants.RECORD_TYPE_CONTACT_PARTNER.equals(this.data.getRecordType())) {
            removeDetailInfoFields(layout, Lists.newArrayList(PartnerConstants.ACCOUNT_ID,
                    PartnerConstants.OUT_RESOURCES, "enable_partner_view"));
            doSupportPartnerRelateTab(layout);
        } else {
            removeDetailInfoFields(layout, Lists.newArrayList(PartnerConstants.OWNED_PARTNER_ID));
            removeTopInfoFields(layout, Lists.newArrayList(PartnerConstants.OWNED_PARTNER_ID));
        }

        //合作伙伴联系人  移除 是否合作伙伴可见 按钮
        modifyLayoutButtonAddPartnerViewBtn(result.getLayout().toLayout(), data);

        //A版企业干掉工单对象
        GetVersion.VersionInfo versionInfo = serviceFacade.getVersionInfo(controllerContext.getTenantId());
        if (versionInfo != null) {
            LayoutUtils.handleRelatedCasesObj(versionInfo.getCurrentVersion(), "CasesObj_contact_id_related_list", layout);
        }
        try {
            //修改相关中地址管理的展示字段，走默认列表布局
            List<IComponent> componentList = layout.getComponents();
            if (CollectionUtils.notEmpty(componentList)) {
                Optional<IComponent> component = componentList.stream().filter(s -> "relatedObject".equals(s.getName())).findFirst();
                if (component.isPresent()) {
                    AccountAddrUtil.changeIncludeFields(controllerContext.getUser(), component.get());
                }
            }
        } catch (MetadataServiceException ex) {
            log.warn("ContactDetailController->ChangeComponentOrder  error", ex);
        }
//        LeadsUtils.handleLeadsRelatedComponents(layout, "LeadsObj_contact_id_related_list");
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("LeadsObj_contact_id_related_list"));

        LeadsUtils.handleLeadsTransferLogRelatedComponents(layout, "LeadsTransferLogObj_contact_id_related_list");
        if (result.getData().get("mobile__s") != null) {//手机字段不可见，同时隐藏手机1-5
            ContactUtil.deleteLayout(result.getLayout(), "mobile");
            ContactUtil.hideTelAndMobile(Lists.newArrayList(result.getData()), "mobile");
        }
        if (result.getData().get("tel__s") != null) {//电话字段不可见，同时隐藏电话1-5
            ContactUtil.deleteLayout(result.getLayout(), "tel");
            ContactUtil.hideTelAndMobile(Lists.newArrayList(result.getData()), "tel");
        }
        //移动端移除rfm 或者联系人关系图谱
        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isWXMiniProgram() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("contact_member_relationship"));
        }

        if (RequestUtil.isH5Request() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("rfm_analysis"));
        }
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("qywx_conversion"));
        return result;
    }

    private void handleInvalidBirthDayField(Result result) {
        //处理生日字段 生日字段可能存在 0000-00-00/0000-12-17/1991-10-00//1991-00-00这四种状态,需要特殊处理
        String birthDay = (String) result.getData().get("date_of_birth");
        if (StringUtils.isEmpty(birthDay) || "0000-00-00".equals(result.getData().get("date_of_birth"))) {
            result.getData().put("date_of_birth", "");
        } else if (birthDay.startsWith("0000-")) {
            result.getData().put("date_of_birth", birthDay.replaceAll("0000-", ""));
        } else {
            result.getData().put("date_of_birth", birthDay.replaceAll("-00", ""));
        }
    }

    private void handleInvalidCardField(Result newResult) {
        Object card = (newResult.getData()).get("card");
        if (card instanceof List) {
            List cards = (List) card;
            if (cards.isEmpty()) {
                newResult.getData().put("card", Lists.newArrayList());
            } else {
                cards.stream().findFirst().ifPresent(x -> {
                    if (x instanceof Map) {
                        Object path = ((Map) x).get("path");
                        if (path == null || "".equals(path)) {
                            newResult.getData().put("card", Lists.newArrayList());
                        }
                    } else {
                        newResult.getData().put("card", Lists.newArrayList());
                    }
                });
            }
        }
    }

    protected void doSupportPartnerRelateTab(ILayout layout) {
        LayoutExt.of(layout).getRelatedComponent().ifPresent(relatedComponent -> {
            try {
                List<IComponent> childComponents = relatedComponent.getChildComponents();
                childComponents.removeIf(childComponent -> {
                    String relatedListName = childComponent.get("related_list_name", String.class);
                    return StringUtils.isNotBlank(relatedListName) && !relatedListName.endsWith("__c");
                });
                relatedComponent.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.warn("ContactDetailController getChildComponents error", e);
            }
        });
    }

    public void modifyLayoutButtonAddPartnerViewBtn(ILayout layout, IObjectData objectData) {
        if (objectData == null || layout == null) {
            return;
        }
        if (ObjectDataExt.of(objectData).isLock()) {
            return;
        }
        if (!Strings.isNullOrEmpty(objectData.getRecordType()) && objectData.get("record_type", String.class).equals(PartnerConstants.RECORD_TYPE_CONTACT_PARTNER)) {
            List<IButton> buttons = layout.getButtons();
            if (CollectionUtils.notEmpty(buttons)) {
                buttons.removeIf(f -> "enable_partner_view__c".equals(f.getAction()));
                layout.setButtons(buttons);
            }
        }
    }

    protected void specialLogicForLayout(ILayout layout) {
        try {
            List<IComponent> childComponents = layout.getComponents();
            Optional<IComponent> leadsComponentOptional = childComponents.stream().filter(x -> "LeadsObj".equals(x.get("ref_object_api_name", String.class))).findFirst();
            if (leadsComponentOptional.isPresent()) {
                IComponent leadsComponent = leadsComponentOptional.get();
                List<IButton> childComponentButtons = leadsComponent.getButtons();
                childComponentButtons.removeIf(button -> ObjectAction.CREATE.getActionCode().equals(button.getAction())
                        || ObjectAction.BULK_RELATE.getActionCode().equals(button.getAction())
                        || ObjectAction.BULK_DISRELATE.getActionCode().equals(button.getAction()));
                leadsComponent.setButtons(childComponentButtons);
            }
        } catch (MetadataServiceException ex) {
            log.warn("ContactWebDetailController->specialLogicForLayout  error", ex);
        }
        Set<String> invisibeList = functionPrivilegeService.getUnauthorizedFields(controllerContext.getUser(), Utils.OPPORTUNITY_API_NAME);
        List<String> hideFieldList = Lists.newArrayList();
        if (invisibeList.contains("tel")) {
            hideFieldList.addAll(Sets.newHashSet("tel1", "tel2", "tel3", "tel4", "tel5"));
        }
        if (invisibeList.contains("mobile")) {
            hideFieldList.addAll(Sets.newHashSet("mobile1", "mobile2", "mobile3", "mobile4", "mobile5"));
        }
        removeDetailInfoFields(layout, hideFieldList);
        removeTopInfoFields(layout, hideFieldList);

        Set<String> readOnlyFieldsList = functionPrivilegeService.getReadonlyFields(controllerContext.getUser(), Utils.OPPORTUNITY_API_NAME);
        List<String> readOnlyFieldList = Lists.newArrayList();

        if (readOnlyFieldsList.contains("tel")) {
            readOnlyFieldList.addAll(Sets.newHashSet("tel1", "tel2", "tel3", "tel4", "tel5"));
        }
        if (readOnlyFieldsList.contains("mobile")) {
            readOnlyFieldList.addAll(Sets.newHashSet("mobile1", "mobile2", "mobile3", "mobile4", "mobile5"));
        }
        setDetailInfoReadOnlyFields(layout, readOnlyFieldList);
        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
        removeMobileClientAndPcButtons(layout, clientInfo);
    }

    protected void setDetailInfoReadOnlyFields(ILayout layout, List<String> readOnlyFieldList) {
        if (!CollectionUtils.notEmpty(readOnlyFieldList)) {
            IComponent component = getComponent(layout, "form", "detail_info");
            if (component == null) {
                return;
            }
            IFormComponent formComponent = (IFormComponent) component;
            List<IFieldSection> fieldSections = formComponent.getFieldSections();
            List<IFormField> fields;
            for (IFieldSection fieldSection : fieldSections) {
                fields = fieldSection.getFields();
                for (IFormField formField : fieldSection.getFields()) {
                    if (readOnlyFieldList.contains(formField.getFieldName())) {
                        formField.setReadOnly(Boolean.TRUE);
                    }
                }
                fieldSection.setFields(fields);
            }
            formComponent.setFieldSections(fieldSections);
        }
    }

    private void removeMobileClientAndPcButtons(ILayout layout, String clientInfo) {
        try {
            List<IComponent> components = layout.getComponents();
            components.forEach(component -> {
                if (Objects.equals(component.getName(), "head_info")) {
                    List<IButton> buttons = component.getButtons();
                    ContactUtils.removeCommonButtons(buttons);
                    //手机端需要做的特殊处理
                    if (!Strings.isNullOrEmpty(clientInfo) && RequestUtil.isMobileOrH5Request()) {
                        ContactUtils.removePCButtons(buttons);
                    } else {
                        // PC 端过来的请求，移除移动端的按钮
                        ContactUtils.removePhoneButtons(buttons);
                    }
                    component.setButtons(buttons);
                }
            });
        } catch (MetadataServiceException e) {
            log.error("removeMobileClientAndPcButtons:{}", layout.getTenantId(), e);
        }
    }

}
