package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.BankStatementObjConstants;
import com.facishare.crm.sfa.utilities.constant.ReceivedPaymentObjConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class BankStatementDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type () == null) {
            return;
        }
        ILayout layout = new Layout(result.getLayout());
        LayoutExt layoutExt = LayoutExt.of(layout);
        String layoutType = arg.getLayout_type();
        if (LayoutTypes.EDIT.equals(layoutType) || LayoutTypes.ADD.equals(layoutType)) {
            layoutExt.removeFields(Lists.newArrayList(BankStatementObjConstants.Field.IsRelatedReceivedPayment.getApiName()));
        }
    }
}

