package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.lto.activity.enums.ActivityResourceType;
import com.facishare.crm.sfa.lto.activity.service.ActivityResourceUsageService;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.license.arg.BatchQueryModuleParaArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.facishare.crm.sfa.utilities.constant.ActivityResourceUsageDetailConstants.*;

public class ActivityResourceUsageDetailListController extends StandardListController {
    private static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);
    private Scene scene;
    private String resourceType;
    private long totalSum;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (arg.getExtraParams() != null) {
            JSONObject extraParams = new JSONObject(arg.getExtraParams());
            Scene.from(extraParams.getString(PREDEFINE_SCENE)).ifPresent(scene -> this.scene = scene);
            resourceType = extraParams.getString(RESOURCE_TYPE);
        }
    }

    @Override
    protected void doFunPrivilegeCheck() {
        if (!serviceFacade.isAdmin(controllerContext.getUser())) {
            super.doFunPrivilegeCheck();
        }
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        if (resourceType != null) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, RESOURCE_TYPE, resourceType);
        }
        if (Scene.USER == scene || Scene.TOTAL == scene) {
            query.resetOrder(Collections.emptyList());
            query.setNeedReturnCountNum(false);
            query.setNeedReturnQuote(false);
        }
        query.setPermissionType(0);
        return query;
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        if (Scene.USER == scene) {
            return findUserData(query);
        } else if (Scene.TOTAL == scene) {
            return findTotalData();
        } else {
            return super.findData(query);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        JSONObject extendInfo = new JSONObject();
        extendInfo.put("total_usage", totalSum);
        after.setExtendInfo(extendInfo);
        return after;
    }

    private QueryResult<IObjectData> findTotalData() {
        String tenantId = controllerContext.getTenantId();
        IObjectData data = new ObjectData();
        data.set(SUM_RESOURCE_USAGE, 0);
        data.set(RESOURCE_USAGE, 0);
        data.set(USAGE_PERCENTAGE, 0);
        ActivityResourceType resourceType = ActivityResourceType.valueFrom(this.resourceType).orElse(ActivityResourceType.RECORDING);
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId(controllerContext.getAppId());
        licenseContext.setTenantId(tenantId);
        Map<String, Set<String>> moduleCodeParaKeyMap = new HashMap<>();
        moduleCodeParaKeyMap.put("ai_interactive_assistant_app", Sets.newHashSet(resourceType.getParaKey()));
        BatchQueryModuleParaArg batchQueryModuleParaArg = new BatchQueryModuleParaArg();
        batchQueryModuleParaArg.setContext(licenseContext);
        batchQueryModuleParaArg.setModuleCodeParaKeyMap(moduleCodeParaKeyMap);
        @SuppressWarnings("unchecked")
        com.facishare.paas.license.common.Result<Map<String, List<ModuleParaPojo>>> batchQueryModulePara = serviceFacade.getBean(LicenseClient.class).batchQueryModulePara(batchQueryModuleParaArg);
        batchQueryModulePara.getResult().get("ai_interactive_assistant_app").stream().findFirst().ifPresent(pojo -> {
            long total = Long.parseLong(pojo.getParaValue()) * 3600;
            long totalUsage = serviceFacade.getBean(ActivityResourceUsageService.class).getAllUsage(tenantId, resourceType);
            data.set(SUM_RESOURCE_USAGE, total);
            data.set(RESOURCE_USAGE, totalUsage);
            data.set(USAGE_PERCENTAGE, calculatePercent(BigDecimal.valueOf(totalUsage), BigDecimal.valueOf(total)));
        });
        QueryResult<IObjectData> res = new QueryResult<>();
        res.setTotalNumber(1);
        res.setData(Collections.singletonList(data));
        return res;
    }

    private QueryResult<IObjectData> findUserData(SearchTemplateQuery query) {
        AggFunctionArg aggFunctionArg1 = new AggFunctionArg();
        aggFunctionArg1.setAggField(RESOURCE_USAGE);
        aggFunctionArg1.setAggFunction(Count.TYPE_SUM);
        IGroupByParameter groupByParameter = new GroupByParameter();
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunctionArg1));
        groupByParameter.setGroupBy(Lists.newArrayList(USER_ID));
        SearchTemplateQuery aggQuery = new SearchTemplateQuery();
        aggQuery.setPermissionType(query.getPermissionType());
        aggQuery.setGroupByParameter(groupByParameter);
        aggQuery.setFilters(query.getFilters());
        BigDecimal totalSum = getTotalUsage(query);
        this.totalSum = totalSum.longValue();
        List<IObjectData> dataList = serviceFacade.aggregateFindBySearchQuery(controllerContext.getUser(), aggQuery, objectDescribe.getApiName());
        int size = dataList.size();
        dataList = getPage(dataList, query.getOffset(), query.getLimit());
        for (IObjectData data : dataList) {
            BigDecimal userSum = data.get(SUM_RESOURCE_USAGE, BigDecimal.class);
            data.set(USAGE_PERCENTAGE, calculatePercent(userSum, totalSum));
        }
        QueryResult<IObjectData> res = new QueryResult<>();
        res.setTotalNumber(size);
        res.setData(dataList);
        return res;
    }

    private BigDecimal getTotalUsage(SearchTemplateQuery query) {
        Count count = new CountFieldDescribe();
        count.setCountType(Count.TYPE_SUM);
        count.setSubObjectDescribeApiName(objectDescribe.getApiName());
        count.setCountFieldApiName(RESOURCE_USAGE);
        count.setDecimalPlaces(0);
        Object countValue = serviceFacade.getCountValue(controllerContext.getTenantId(), count, SearchUtil.copySearchTemplateQuery(query));
        return new BigDecimal(countValue.toString());
    }

    private static BigDecimal calculatePercent(BigDecimal userSum, BigDecimal totalSum) {
        return userSum.divide(totalSum, 4, RoundingMode.HALF_UP).multiply(ONE_HUNDRED);
    }

    // 虚拟分页
    private static <T> List<T> getPage(List<T> list, int offset, int limit) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        if (offset < 0) {
            offset = 0;
        }
        if (limit <= 0) {
            return Collections.emptyList();
        }
        int fromIndex = offset;
        int toIndex = offset + limit;
        if (fromIndex >= list.size()) {
            return Collections.emptyList();
        }
        if (toIndex > list.size()) {
            toIndex = list.size();
        }
        return list.subList(fromIndex, toIndex);
    }
}