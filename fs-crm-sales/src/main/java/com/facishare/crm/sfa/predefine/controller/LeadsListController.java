package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

public class LeadsListController extends StandardListController {
    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext()
            .getBean("functionPrivilegeService", FunctionPrivilegeService.class);


    private final LeadsRemainingTimeReplace leadsRemainingTimeReplace = SpringUtil.getContext().getBean(LeadsRemainingTimeReplace.class);

    private String extendAttribute;
    private String leadsPoolId;
    private ISearchTemplate searchTemplate;
    private ObjectPoolPermission.ObjectPoolPermissions objectPoolPermissions;

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
		User user = controllerContext.getUser();
		searchTemplate = serviceFacade.findSearchTemplateByIdAndType(user, this.getSearchTemplateId(), objectDescribe.getApiName(), arg.getSearchTemplateType());
        if (searchTemplate != null) {
            extendAttribute = searchTemplate.getExtendAttribute();
        }
        leadsPoolId = SearchListUtil.getLeadsPoolIdByQuery(query, extendAttribute);
		objectPoolPermissions = SearchListUtil.getLeadsPoolPermissionByUserId(user, leadsPoolId);
        SearchListUtil.handleLeadsSearchQuery(controllerContext.getUser(), query, searchTemplate, objectPoolPermissions);

        SearchListUtil.buildKeywordQuery(query, arg.getKeyword());

        leadsRemainingTimeReplace.replaceRemainingTimeQuery(query);

        return query;
    }

    @Override
    protected Query defineQuery() {
        Query searchQuery = super.defineQuery();
        searchTemplate = searchQuery.getSearchTemplate();
        return searchQuery;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
        if (searchTemplate != null) {
            extendAttribute = searchTemplate.getExtendAttribute();
        }
		leadsPoolId = SearchListUtil.getLeadsPoolIdByQuery(searchQuery, extendAttribute);
		objectPoolPermissions = SearchListUtil
				.getLeadsPoolPermissionByUserId(controllerContext.getUser(), leadsPoolId);
		SearchListUtil.handleLeadsSupportOrSearchQuery(controllerContext.getUser(), searchQuery, searchTemplate, objectPoolPermissions);

        SearchListUtil.buildKeywordQuery(searchQuery, arg.getKeyword());

        return searchQuery;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        addButton(result);
        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        // 屏蔽线索回收规则查询
        if (CollectionUtils.empty(arg.getFieldProjection()) || arg.getFieldProjection().contains("is_remind_recycling")) {
            try {
                LeadsUtils.handleIsRemindRecycling(objectDataList);
            } catch (Exception e) {
                log.error("{}", e.getMessage());
            }
        }

        if (CollectionUtils.empty(arg.getFieldProjection()) || arg.getFieldProjection().contains("remaining_time")) {
            AccountUtil.handleRemainingTime(objectDataList);
        }

        handleMemberInvisibleFields(objectDataList);
        return result;
    }

    /**
     * 移动端列表增加扫名片按钮
     * 扫名片按钮权限利用新建权限
     *
     * @param result
     */
    private void addButton(Result result) {
        if (result == null || CollectionUtils.empty(result.getListLayouts())) {
            return;
        }
        Map<String, Boolean> actionFunMap = functionPrivilegeService.funPrivilegeCheck(controllerContext.getUser(),
                SFAPreDefineObject.Leads.getApiName(),
                Lists.newArrayList(ObjectAction.CREATE.getActionCode()));
        ILayout layout;
        if(RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()){
            for (LayoutDocument layoutDocument : result.getListLayouts()) {
                layout = new Layout((layoutDocument));
                if (actionFunMap.get(ObjectAction.CREATE.getActionCode())) {
                    layout.addButtons(ObjectAction.SCAN_CARD.createButton());
                }
            }
        }
    }

    //处理线索池普通成员不可见字段
    private void handleMemberInvisibleFields(List<IObjectData> objectDataList) {
        if(CollectionUtil.isEmpty(objectDataList)) {
            return;
        }
        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
        if (clientInfo == null) {
            clientInfo = "";
        }
        if (Objects.equals(ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER, objectPoolPermissions)
				&& !StringUtils.isEmpty(leadsPoolId)) {
            InvisibleFieldUtil.handleMemberInvisibleFields(controllerContext.getUser(), objectDataList,
                    "17", leadsPoolId, clientInfo);
        } else if("Allow_Choose_Leads".equals(searchTemplate.getApiName()) || "Allow_Choose_Leads".equals(searchTemplate.getBaseSceneApiName())) {
            List<String> poolIds = LeadsUtils.getPoolIds(objectDataList);
            for (String poolId : poolIds) {
                List<IObjectData> poolDataList = objectDataList.stream().filter(x -> poolId.equals(LeadsUtils.getPoolId(x))).collect(Collectors.toList());
                InvisibleFieldUtil.handleMemberInvisibleFields(controllerContext.getUser(), poolDataList,
                        "17", poolId, clientInfo);
            }
        }
    }
}
