package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.CampaignMembersUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/1/11 16:06
 */


public class CampaignMembersListController extends StandardListController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (CollectionUtils.notEmpty(result.getDataList())) {
            CampaignMembersUtil.doSpecialDataList(result.getDataList());
        }
        if (null != result.getLayout()){
            specialLayout(result.getLayout().toLayout());
        }
        return result;
    }

    public static void specialLayout(ILayout layout) {
        List<IButton> buttonList = layout.getButtons();
        if (CollectionUtils.notEmpty(buttonList)) {
            buttonList.removeIf(m -> "Add".equals(m.getAction()));
            layout.setButtons(buttonList);
        }
    }
}
