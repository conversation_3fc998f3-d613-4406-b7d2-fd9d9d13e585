package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-01-04
 */
    public class AttributeConstraintLinesListHeaderController extends SFAListHeaderController {
    @Override
    protected boolean needRemoveMobileButton() {
        return true;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        ILayout layout = new Layout(res.getLayout());
        filterButtons(layout);
        return res;
    }

    private void filterButtons(ILayout layout) {
        WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList("Import", "Export", "IntelligentForm", "ExportFile", "Add"));
    }
}
