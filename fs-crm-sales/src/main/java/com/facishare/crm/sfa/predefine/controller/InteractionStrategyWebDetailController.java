package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.InteractionStrategyConstants;
import com.facishare.crm.sfa.predefine.service.InteractionStrategyRenderService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

/**
 * InteractionStrategyWebDetailController
 *
 * <AUTHOR>
 */
public class InteractionStrategyWebDetailController extends SFAWebDetailController {
    private final InteractionStrategyRenderService interactionStrategyRenderService = SpringUtil.getContext().getBean(InteractionStrategyRenderService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        render(result);
        return result;
    }


    private void render(Result result) {
        Optional.ofNullable(result)
                .map(Result::getData)
                .map(ObjectDataDocument::toObjectData)
                .ifPresent(data ->
                        interactionStrategyRenderService.render(data, User.systemUser(controllerContext.getTenantId()), InteractionStrategyConstants.CONDITION)
                );
    }
}