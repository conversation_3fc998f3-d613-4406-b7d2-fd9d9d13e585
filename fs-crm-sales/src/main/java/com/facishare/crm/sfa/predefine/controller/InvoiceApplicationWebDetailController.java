package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/6/22 10:48 上午
 * @illustration
 */
public class InvoiceApplicationWebDetailController extends SFAWebDetailController {

    private final InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);

    private static final String COMPONENT_API_NAME = "invoice_app_sales_order_multi_related_list";

    private static final String MULTI_INVOICE_SALES_ORDER_JSON = "{\n" +
            "    \"buttons\": [],\n" +
            "    \"api_name\": \"invoice_app_sales_order_multi_related_list\",\n" +
            "    \"is_hidden\": false,\n" +
            "    \"header\": \"销售订单\",\n" +
            "    \"type\": \"relatedlist\",\n" +
            "    \"ref_object_api_name\": \"SalesOrderObj\",\n" +
            "    \"related_list_name\": \"invoice_app_sales_order_multi_related_list\",\n" +
            "    \"nameI18nKey\": \"SalesOrderObj.attribute.self.display_name\",\n" +
            "    \"order\": 999\n" +
            "}";


    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        super.setFinanceEmployeeIdInfo(rst);
        if (result.getLayout() == null) {
            return result;
        }
        ILayout layout = new Layout(result.getLayout());
        // 销售订单和开票申请多对多的处理
        if (invoiceService.isNewInvoiceOpen(controllerContext.getUser())) {
            addInvoiceAppLayoutStructure(layout);
        }
        addSoftWorkflowButtons(controllerContext.getUser(), data, layout);
        return rst;
    }

    /**
     * 添加订单和开票的多对多页签
     *
     * @param layout
     * @return
     */
    public void addInvoiceAppLayoutStructure(ILayout layout) {
        try {
            if (CollectionUtils.nullToEmpty(layout.getHiddenComponents()).contains(COMPONENT_API_NAME)) {
                return;
            }
            List<IComponent> components = LayoutExt.of(layout).getComponentsSilently();
            IComponent existComponent = components.stream()
                    .filter(x -> Objects.equals(x.getName(), COMPONENT_API_NAME))
                    .findFirst()
                    .orElse(null);
            if (existComponent != null) {
                existComponent.setType("relatedlist");
                layout.setComponents(components);
                return;
            }
            IComponent multiComponent = ComponentFactory.newInstance(MULTI_INVOICE_SALES_ORDER_JSON);
            multiComponent.setHeader(I18N.text("SalesOrderObj.attribute.self.display_name"));
            WebDetailLayout.of(layout).addComponents(Lists.newArrayList(multiComponent), 9999);
        } catch (Exception e) {
            log.error("InvoiceApplicationWebDetailController  addComponents error tenantId->{}, userId->{}, dataId->{}",
                    controllerContext.getTenantId(), controllerContext.getUser().getUpstreamOwnerIdOrUserId(), arg.getObjectDataId(), e);
        }
    }
}
