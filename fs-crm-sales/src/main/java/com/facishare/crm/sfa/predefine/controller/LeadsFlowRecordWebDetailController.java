package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class LeadsFlowRecordWebDetailController extends SFAWebDetailController {
    private final List<String> remainComponentType = Lists.newArrayList("tabs", "simple", "form", "navigation", "top_info");
    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void doDataPrivilegeCheck(Arg arg) {
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null || newResult.getData() == null) {
            return newResult;
        }
        try {
            ILayout layout = new Layout(newResult.getLayout());
            WebDetailLayout webDetailLayout = WebDetailLayout.of(layout);
            List<IComponent> components = layout.getComponents();
            if(CollectionUtils.notEmpty(components)) {
                List<String> remainApiNames = components.stream().filter(x -> remainComponentType.contains(x.getType())).map(x -> x.getName()).collect(Collectors.toList());
                webDetailLayout.retainComponents(remainApiNames);
            }
			// 只保留编辑按钮
			List<IButton> buttons = webDetailLayout.getButtons();
			buttons.removeIf(b -> !Objects.equals(b.getAction(), ObjectAction.UPDATE.getActionCode()));
			webDetailLayout.setButtons(buttons);
        } catch (Exception e){
            log.error("LeadsFlowRecordWebDetailController get component error", e);
        }
        return newResult;
    }
}
