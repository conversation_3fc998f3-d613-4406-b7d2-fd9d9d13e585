package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;

import java.util.List;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

/**
 * <AUTHOR>
 * @date 2018/3/22 11:06
 */
public class ContactDescribeLayoutController extends ContactPartnerDescribeLayoutFilterController {
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        if (!arg.getInclude_layout()) {
            return;
        }

        ContactUtil.setTelMobileFieldProperty(controllerContext.getUser(), arg.getApiname(), formComponent);

        switch (arg.getLayout_type()) {
            case LAYOUT_TYPE_EDIT:
                removeSystemInfoFieldSection();
                break;
            case LAYOUT_TYPE_ADD:
                //removeSystemInfoFieldSection();
                break;
            default:
                break;
        }
    }

    @Override
    protected void promptUpgrade(Arg arg, Result result) {
        super.promptUpgrade(arg, result);

    }

    //删除系统字段
    private void removeSystemInfoFieldSection() {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        if (formComponent == null) {
            return;
        }
        List<IFieldSection> tmpFieldSections = formComponent.getFieldSections();
        String fieldSectionApiName;
        List<IFormField> fields;
        for (IFieldSection fieldSection : tmpFieldSections) {

            fieldSectionApiName = fieldSection.get("api_name", String.class);
            if (!"system_form_field_generate_by_UDObjectServer__c".equals(fieldSectionApiName) && !"sysinfo_section__c".equals(fieldSectionApiName)) {
                if ("base_field_section__c".equals(fieldSectionApiName)) {
                    fields = fieldSection.getFields();
                    fields.removeIf(field -> "lock_status".equals(field.getFieldName()));
                    fieldSection.setFields(fields);
                }
                fieldSections.add(fieldSection);
            }
        }
        formComponent.setFieldSections(fieldSections);
    }

}
