package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ForecastTaskDetailConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.InfraServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardListController;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ForecastTaskDetailListController extends AbstractStandardListController<ForecastTaskDetailListController.Arg> {

	private static final InfraServiceFacade INFRA_SERVICE_FACADE = SpringUtil.getContext().getBean(InfraServiceFacadeImpl.class);

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        if (arg.getForecastModelApiName() != null) {
            SearchUtil.fillFilterIsNotNull(searchTemplateQuery.getFilters(), arg.getForecastModelApiName());
        }
        return searchTemplateQuery;
    }

    @Override
    protected void doFunPrivilegeCheck() {
        // Do nothing because of X and Y.
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        List<IObjectData> dataList = queryResult.getData();
        if (dataList.isEmpty()) {
            return res;
        }
        List<String> ids = dataList.stream().map(data -> data.get(ForecastTaskDetailConstants.Field.FORECAST_OBJECT_ID, String.class)).collect(Collectors.toList());
        String tenantId = controllerContext.getTenantId();
        String forecastObjectApiName = arg.getForecastObjectApiName();
        User user = controllerContext.getUser();
        List<IObjectData> forecastObjects = serviceFacade.findObjectDataByIds(tenantId, ids, forecastObjectApiName);
        IObjectDescribe forecastObjectDescribe = serviceFacade.findObject(tenantId, forecastObjectApiName);
        infraServiceFacade.fillQuoteFieldValue(user, forecastObjects, forecastObjectDescribe, true);
        serviceFacade.fillObjectDataWithRefObject(forecastObjectDescribe, forecastObjects, user);
        serviceFacade.fillCountryAreaLabel(forecastObjectDescribe, forecastObjects);
        Map<String, IObjectData> forecastObjectMap = forecastObjects.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        List<ObjectDataDocument> forecastObjectDataList = new ArrayList<>(dataList.size());
        for (IObjectData data : dataList) {
            IObjectData forecastObject = forecastObjectMap.get(data.get(ForecastTaskDetailConstants.Field.FORECAST_OBJECT_ID, String.class));
            if (forecastObject != null) {
                IObjectData copied = ObjectDataExt.of(forecastObject).copy();
                copied.set(ForecastTaskDetailConstants.Field.FORECAST_OBJECT_ID, copied.getId());
                copied.set(ForecastTaskDetailConstants.Field.FORECAST_TASK_OBJECT_ID, data.get(ForecastTaskDetailConstants.Field.FORECAST_TASK_OBJECT_ID));
                copied.set(ForecastTaskDetailConstants.Field.IN_COMMITMENT_FORECAST, data.get(ForecastTaskDetailConstants.Field.IN_COMMITMENT_FORECAST));
                copied.setId(data.getId());
                forecastObjectDataList.add(ObjectDataDocument.of(copied));
            }
        }
        res.setDataList(forecastObjectDataList);
        replaceDeleteButtonLabel(res);
        return res;
    }

    private void replaceDeleteButtonLabel(Result result) {
        ButtonInfo buttonInfo = result.getButtonInfo();
        buttonInfo.getButtons().stream()
                .filter(button -> ObjectAction.DELETE.getActionCode().equals(button.get(IButton.ACTION))).findAny()
                .ifPresent(buttonDocument -> buttonDocument.put(IButton.LABLE, I18N.text("paas.udobj.action.action_remove"))); // 移除
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    static class Arg extends StandardListController.Arg {

        private String forecastModelApiName;
        private String forecastObjectApiName;

        public String getForecastObjectApiName() {
            return forecastObjectApiName;
        }

        public void setForecastObjectApiName(String forecastObjectApiName) {
            this.forecastObjectApiName = forecastObjectApiName;
        }

        public String getForecastModelApiName() {
            return forecastModelApiName;
        }

        public void setForecastModelApiName(String forecastModelApiName) {
            this.forecastModelApiName = forecastModelApiName;
        }

        @Override
        public boolean equals(Object o) {
            return super.equals(o);
        }

        @Override
        public int hashCode() {
            return super.hashCode();
        }
    }
}