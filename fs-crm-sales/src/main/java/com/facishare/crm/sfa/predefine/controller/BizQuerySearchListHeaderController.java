package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.metadata.dto.CommonFilterField;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/5/22 17:12
 * @description:
 */
public class BizQuerySearchListHeaderController extends BaseSFAListHeaderController{
    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        res.setListSingleExposed(2);
        List<CommonFilterField.FilterField> filterFields = new ArrayList<>();
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("companyStatus").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("reg_capital").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("categoryFirst").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("categorySecond").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("categoryThird").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("categoryFourth").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("province_s").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("city_s").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("district_s").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("employee_num").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("stockType").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("currency").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasProduct").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasZhixing").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasBid").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasPunish").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasXianxiao").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasAbnormal").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasPhone").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasEmail").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("hasRegHg").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("keywords").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("company_type_simple").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("sortWay").isShow(true).build());
        res.setFilterFields(filterFields);
        res.setSupportTag(false);
        return res;
    }
}
