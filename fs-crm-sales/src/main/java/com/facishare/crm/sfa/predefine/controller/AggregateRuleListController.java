package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants;
import com.facishare.crm.sfa.utilities.constant.AggregateRuleConstants.DataSourceEnum;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 聚合规则List
 */
public class AggregateRuleListController extends StandardListController {

    AggregateRuleService aggregateRuleService = SpringUtil.getContext().getBean(AggregateRuleService.class);

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        List<String> fieldNames = filters.stream().map(IFilter::getFieldName).collect(Collectors.toList());
        // data_source数据来源不能是system
        if(CollectionUtils.empty(fieldNames) || !fieldNames.contains(AggregateRuleConstants.Field.DATA_SOURCE)) {
            SearchUtil.fillFilterN(filters, AggregateRuleConstants.Field.DATA_SOURCE, DataSourceEnum.SYSTEM.getValue());
        }
        return query;
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> dataQueryResult = super.getQueryResult(query);
        StopWatch stopWatch = StopWatch.create("aggregateRuleQueryResult");
        aggregateRuleService.specialTreatmentData(controllerContext.getUser(), dataQueryResult.getData(), false);
        stopWatch.lap("specialTreatmentData");
        stopWatch.log();
        return dataQueryResult;
    }
}
