package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.sfa.predefine.service.accountsreceivable.dto.AccountsReceivableType;
import com.facishare.crm.sfa.utilities.util.AccountsReceivableNoteUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 过滤未开票或部分开票的应收单
 */
@Slf4j
public class AccountsReceivableNoteRelatedListController extends StandardRelatedListController {

    private final List<String> accountsReceivableDetailIds = Lists.newArrayList();
    private List<String> filterArIds = Lists.newArrayList();


    private boolean isAccountsReceivableModule = false;
    private boolean isAccountsReceivableModuleEdit = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        getAccountReceivableDataMode();
    }


    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        return addFilterSearchTemplateQuery(searchTemplateQuery);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        getCountAccountsReceivableDetail(result);
        return result;
    }

    private void getAccountReceivableDataMode() {
        List<String> accountsReceivableNoteIds = Lists.newArrayList();
        ObjectDataDocument objectData = arg.getObjectData();
        if (objectData == null) {
            return;
        }
        if (!objectData.containsKey("from_sales_invoice")) {
            return;
        }
        isAccountsReceivableModule = (Boolean) objectData.get("from_sales_invoice");
        if (!isAccountsReceivableModule) {
            return;
        }
        if (objectData.containsKey("from_sales_invoice_edit")) {
            isAccountsReceivableModuleEdit = (Boolean) objectData.get("from_sales_invoice_edit");
        }

//      List sales_order_ids = (List) objectData.get("accountsReceivableNoteIds");
//      salesOrderIds.addAll(sales_order_ids);

        Map<String, List<String>> arIdToArDetailIds = Maps.newHashMap();
        List accounts_receivable_detail_data = (List) objectData.get("accountsReceivableDetailData");
        if (accounts_receivable_detail_data != null) {
            accounts_receivable_detail_data.forEach(x -> {
                Map map = (Map) x;
                String accountsReceivableNoteId = map.get("accountsReceivableNoteId").toString();
                List<String> arDetailIdsOfArNoteId = (List) map.get("accountsReceivableDetailIds");
                accountsReceivableDetailIds.addAll(arDetailIdsOfArNoteId);
                // 为了计算应收单是否可以要被过滤。
                arIdToArDetailIds.put(accountsReceivableNoteId, arDetailIdsOfArNoteId);
                accountsReceivableNoteIds.add(accountsReceivableNoteId);
            });
        }

        filterArIds = AccountsReceivableNoteUtil.getArDetailCountGroupByNote(controllerContext.getTenantId(), accountsReceivableNoteIds, arIdToArDetailIds, accountsReceivableDetailIds);
    }


    private SearchTemplateQuery addFilterSearchTemplateQuery(SearchTemplateQuery searchTemplateQuery) {
        List<IFilter> filters = Lists.newArrayList();

        // 添加生命状态
        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName("life_status");
        lifeStatusFilter.setFieldValues(Lists.newArrayList(SystemConstants.LifeStatus.Invalid.value));
        lifeStatusFilter.setOperator(Operator.NEQ);
        filters.add(lifeStatusFilter);

        if (CollectionUtils.notEmpty(filterArIds)) {
            IFilter filterArDetail = new Filter();
            filterArDetail.setFieldName(IObjectData.ID);
            filterArDetail.setFieldValues(filterArIds);
            filterArDetail.setOperator(Operator.NIN);
            filters.add(filterArDetail);
        }

        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    /**
     * 应收管理
     */
    private void getCountAccountsReceivableDetail(Result result) {
        List<String> arIds = result.getDataList().stream().map(x -> x.getId()).collect(Collectors.toList());

        Map<String, AccountsReceivableType.AccountsReceivableDetailData> arId2ArDetail = AccountsReceivableNoteUtil.getArId2ArDetails(controllerContext.getTenantId(), arIds, accountsReceivableDetailIds);

        result.getDataList().forEach(x -> {
            String orderId = x.getId();
            x.put("accounts_receivable_detail_data", arId2ArDetail.get(orderId).getMap());
            x.put("accounts_receivable_detail_count", arId2ArDetail.get(orderId).getCount());
            x.put("accounts_receivable_detail_remove_count", arId2ArDetail.get(orderId).getRemoveCount());
            x.put("accounts_receivable_detail_choose_count", arId2ArDetail.get(orderId).getOtherCount());
        });
    }
}