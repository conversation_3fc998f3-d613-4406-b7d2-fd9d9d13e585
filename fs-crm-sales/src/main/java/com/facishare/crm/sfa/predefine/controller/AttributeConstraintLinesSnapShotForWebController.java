package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-02-21
 */
public class AttributeConstraintLinesSnapShotForWebController extends StandardSnapShotForWebController {
    public static final String KEY_FORM_FIELDS = "form_fields";
    public static final String KEY_FIELD_NAME = "field_name";
    @Override
    protected Map getSnapShotLayout(Map<String, Object> objData, IObjectDescribe objectDescribe, ControllerContext context) {
        Map map = super.getSnapShotLayout(objData, objectDescribe, context);
        LayoutExt layoutExt = LayoutExt.of(map);
        Optional<IComponent> component = layoutExt.getComponentByApiName("form_component");
        if(component.isPresent())  {
            List sections = component.get().get("field_section", List.class, Lists.newArrayList());
            for (int i = 0; i < sections.size(); i++) {
                Map section = (Map) sections.get(i);
                //过滤掉 属性和属性值 两个字段，不展示
                if("base_field_section__c".equals(section.get("api_name")) && section.get(KEY_FORM_FIELDS) != null) {
                    List<Map> fields = ((List<Map>)section.get(KEY_FORM_FIELDS)).stream()
                            .filter(f->!"attribute_id".equals(f.get(KEY_FIELD_NAME)) && !"attribute_value_ids".equals(f.get(KEY_FIELD_NAME)))
                            .collect(Collectors.toList());
                    section.put(KEY_FORM_FIELDS, fields);
                    break;
                }
            }
        }
        return map;
    }
}
