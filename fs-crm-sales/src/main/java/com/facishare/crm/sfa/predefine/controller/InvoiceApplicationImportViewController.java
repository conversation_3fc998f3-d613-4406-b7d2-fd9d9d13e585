package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
public class InvoiceApplicationImportViewController extends StandardImportViewController {

    private static final List<String> COMMON_REMOVE_FIELDS = Lists.newArrayList(
            "attach",
            "life_status",
            "owner_department",
            "biz_status",
            "is_fixed_flow",
            "status",
            "submit_time"
    );

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (StringUtils.equals(describe.getApiName(), Utils.INVOICE_APPLICATION_API_NAME)) {
            fieldDescribes.removeIf(field -> COMMON_REMOVE_FIELDS.contains(field.getApiName()));
        }
    }
} 