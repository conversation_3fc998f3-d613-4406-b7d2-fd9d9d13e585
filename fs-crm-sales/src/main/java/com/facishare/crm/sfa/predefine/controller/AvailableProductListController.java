package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.QueryUtils;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * <AUTHOR>
 */
public class AvailableProductListController extends StandardListController {
    private AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        availableRangeUtils.handleALL(AvailableConstants.ProductResultField.PRODUCT_ID, queryResult.getData());
        return result;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        QueryUtils.resetNameOrder2LastModifiedTime(query);
        return query;
    }
}
