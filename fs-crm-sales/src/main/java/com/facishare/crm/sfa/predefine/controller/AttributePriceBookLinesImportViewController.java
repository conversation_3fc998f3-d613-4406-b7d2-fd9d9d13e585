package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.AttributePriceBookImportUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2025-03-13
 */
public class AttributePriceBookLinesImportViewController extends StandardImportViewController {
    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (arg.getImportType() == IMPORT_TYPE_ADD) {
            if (StringUtils.equals(describe.getApiName(), SFAPreDefineObject.AttributePriceBookLines.getApiName())) {
                AttributePriceBookImportUtils.removeAndAddFields(controllerContext.getTenantId(), fieldDescribes);
            }
        }
    }
}
