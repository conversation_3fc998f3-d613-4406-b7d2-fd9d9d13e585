package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.ListHeaderUtils;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.SfaListHeaderUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/8/4 4:22 下午
 * @illustration
 */
public class BomListHeaderController extends StandardListHeaderController {
    private static final List<String> buttons = Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode(),ObjectAction.BATCH_EXPORT.getActionCode());
    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        if (Objects.isNull(res.getLayout())) {
            return res;
        }

        ILayout layout = new Layout(res.getLayout());
        SfaListHeaderUtil.specialLogicForLayout(layout,buttons);
        SfaListHeaderUtil.specialButtons(res.getButtons(),buttons);
        //不是选配页进来的，都移除掉这两个虚拟字段
        if (!Objects.equals(arg.getListType(), "selected" )) {
            ListHeaderUtils.removerField(res, Sets.newHashSet(BomConstants.FIELD_PRICE_BOOK_ID, BomConstants.FIELD_MODIFIED_ADJUST_PRICE));
        }
        return res;
    }
}
