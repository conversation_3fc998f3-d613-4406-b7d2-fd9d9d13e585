package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AccountMainDataListHeaderController extends BaseSFAListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
//        if(RequestUtil.isH5Request() || RequestUtil.isMobileRequest()) {
//            result.getButtons().removeIf(x -> x.toButton().getApiName().equals("AllocateMainData_button_default"));
//        }
        //修改 button
        ButtonUtils.editMergeButton(result);
        return result;
    }
}
