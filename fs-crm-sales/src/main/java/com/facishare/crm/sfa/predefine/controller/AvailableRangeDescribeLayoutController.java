package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class AvailableRangeDescribeLayoutController extends SFADescribeLayoutController {

    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        if (GrayUtil.isPriceBookReform(controllerContext.getTenantId()) && !bizConfigThreadLocalCacheService.isOpenAvailablePriceBook(controllerContext.getTenantId())) {
            //如果没有开启可售价目表，可售产品适用范围选项去掉跟随价目表范围
            removeAvailableRangeApplyRangeOption(result.getDetailObjectList());
        }
    }

    private void removeAvailableRangeApplyRangeOption(List<DetailObjectListResult> detailObjectList) {
        if (detailObjectList == null) {
            return;
        }
        for (DetailObjectListResult detailObject : detailObjectList) {
            IObjectDescribe objectDescribe = ObjectDescribeDocument.of(detailObject.getObjectDescribe())
                    .toObjectDescribe();
            if (!SFAPreDefineObject.AvailableProduct.getApiName().equals(objectDescribe.getApiName())) {
                continue;
            }
            SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe("apply_range");
            if (fieldDescribe != null) {
                List<ISelectOption> selectOptions = fieldDescribe.getSelectOptions();
                if (CollectionUtils.isNotEmpty(selectOptions)) {
                    selectOptions.removeIf(f -> f.getValue().equals("PRICE_BOOK"));
                    fieldDescribe.setSelectOptions(selectOptions);
                }
            }
        }
    }
}