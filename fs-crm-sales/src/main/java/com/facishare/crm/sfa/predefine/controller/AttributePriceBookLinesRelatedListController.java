package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.attributepricebook.AttributePriceBookServiceImpl;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;

import java.util.List;


public class AttributePriceBookLinesRelatedListController extends StandardRelatedListController {
    AttributePriceBookServiceImpl attributePriceBookService = SpringUtil.getContext().getBean(AttributePriceBookServiceImpl.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String searchQueryInfo = arg.getSearchQueryInfo();
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        // 前台没有指定排序规则 默认编号顺序以及商品顺序
        if (CollectionUtils.empty(query.getOrders())) {
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("name",true));
            orderByList.add(new OrderBy("product_id", true));
            query.setOrders(orderByList);
            arg.setSearchQueryInfo(JsonUtil.toJson(query));
        }
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> dataQueryResult = super.getQueryResult(query);
        attributePriceBookService.assembleObjectDataDocument(controllerContext.getUser(), dataQueryResult.getData());
        return dataQueryResult;
    }
}
