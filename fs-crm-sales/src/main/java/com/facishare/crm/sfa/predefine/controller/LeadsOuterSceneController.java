package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.enums.ObjectApiNameEnum;
import com.facishare.crm.sfa.predefine.service.PoolSearchTemplateService;
import com.facishare.paas.appframework.core.predef.controller.StandardOuterSceneController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.Map;

/**
 * <AUTHOR>
 * @time 2024-03-07 14:58
 * @Description
 */
public class LeadsOuterSceneController extends StandardOuterSceneController {
    private final PoolSearchTemplateService poolSearchTemplateService = SpringUtil.getContext().getBean(PoolSearchTemplateService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);

        Map extendResult = poolSearchTemplateService.buildExtendResultData(controllerContext.getUser(),
                ObjectApiNameEnum.LeadsPool.getApiName(),
                Lists.newArrayList(after.getScene()));
        after.setExtraResult(extendResult);
        return after;
    }
}
