package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date 2023/9/13 1:52 下午
 * @illustration
 */
public class AccountsReceivableDetailWebDetailController extends SFAWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if(newResult.getLayout() != null){
            ILayout layout = new Layout(newResult.getLayout());
            removeDetailInfoFields(layout, Lists.newArrayList("receivable_object_data_id", "receivable_object_detail_data_id"));
        }
        return newResult;
    }
}
