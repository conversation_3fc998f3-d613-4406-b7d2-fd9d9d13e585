package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.CampaignMembersConstants;
import com.facishare.crm.sfa.utilities.util.CampaignMembersUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Maps;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by renlb on 2018/11/28.
 */
public class CampaignMembersRelatedListController extends StandardRelatedListController {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (result == null) {
            return result;
        }
        if (!CollectionUtils.isEmpty(result.getDataList())) {
            specialDataList(result.getDataList());
        }
        if (result.getLayout() != null && arg != null) {
            ILayout layout = result.getLayout().toLayout();
            CampaignMembersUtil.specialLayout(arg.getTargetObjectApiName(), controllerContext.getUser(), layout);
            if (!CollectionUtils.isEmpty(arg.getRelatedListComponent()) && arg.getRelatedListComponent().containsKey(ListComponentExt.BUTTON_INFO)) {
                CampaignMembersUtil.specialButton(arg.getTargetObjectApiName(), controllerContext.getUser(), layout, arg.getRelatedListComponent());
            }
        }
        return result;
    }

    private void specialDataList(List<ObjectDataDocument> objectDataDocumentsList) {
        if (CollectionUtils.isEmpty(objectDataDocumentsList)) {
            return;
        }
        objectDataDocumentsList.forEach(m -> {
            IObjectData objectData = m.toObjectData();
            String campaignMembersType = objectData.get(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_TYPE, String.class);
            String referenceId = "";
            String jobTitle = "";
            String referenceOwner = "";
			if (StringUtils.isEmpty(campaignMembersType)) {
				return;
			}
            switch (campaignMembersType) {
                case Utils.LEADS_API_NAME:
                    referenceId = objectData.get(CampaignMembersConstants.Field.LEADS_ID, String.class);
                    jobTitle = objectData.get(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_LEADS_JOB_TITLE, String.class);
                    referenceOwner = objectData.get(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_LEADS_OWNER, String.class);
                    break;
                case Utils.CONTACT_API_NAME:
                    referenceId = objectData.get(CampaignMembersConstants.Field.CONTACT_ID, String.class);
                    jobTitle = objectData.get(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_CONTACT_JOB_TITLE, String.class);
                    referenceOwner = objectData.get(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_CONTACT_OWNER, String.class);
                    break;
                case Utils.ACCOUNT_API_NAME:
                    referenceId = objectData.get(CampaignMembersConstants.Field.ACCOUNT_ID, String.class);
                    referenceOwner = objectData.get(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_ACCOUNT_OWNER, String.class);
                    break;
                default:
                    break;
            }
            Map map = Maps.newHashMap();
            map.put(DBRecord.ID, referenceId);
            map.put("api_name", campaignMembersType);
            objectData.set(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_NAME + "__r", map);
            objectData.set(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_JOB_TITLE, jobTitle);
            objectData.set(CampaignMembersConstants.Field.CAMPAIGN_MEMBERS_OWNER, referenceOwner);
        });
    }
}
