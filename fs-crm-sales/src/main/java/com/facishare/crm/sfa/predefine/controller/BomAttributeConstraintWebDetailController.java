package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstraintConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @illustration
 */
public class BomAttributeConstraintWebDetailController extends StandardWebDetailController {
    private final List<String> removeActionList = Lists.newArrayList(
            ObjectAction.CLONE.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        LayoutDocument layout = result.getLayout();
        if (Objects.nonNull(layout)) {
            specialLogicForLayout(layout.toLayout());
        }
        if (result.getData() != null && StringUtils.isEmpty(result.getData().toObjectData().get(BomConstraintConstants.CHECK_MODE, String.class))) {
            result.getData().toObjectData().set(BomConstraintConstants.CHECK_MODE, BomConstraintConstants.CheckMode.PRODUCT.getMode());
        }
        if(result.getData() != null){
            handleData(result.getData().toObjectData());
        }
        return result;
    }

    private void specialLogicForLayout(ILayout layout) {
        WebDetailLayout.of(layout).removeButtonsByActionCode(removeActionList);
    }

    private void handleData(IObjectData objectData) {
        if (Objects.isNull(objectData)) {
            return;
        }
        String coreId = objectData.get(BomConstants.FIELD_CORE_ID, String.class);
        if (StringUtils.isNotBlank(coreId)) {
            return;
        }
        String bomId = objectData.get(BomConstants.FIELD_BOM_ID, String.class);
        if (StringUtils.isBlank(bomId)) {
            return;
        }
        IObjectData bomData = serviceFacade.findObjectData(controllerContext.getUser(), bomId, Utils.BOM_API_NAME);
        if (Objects.isNull(bomData) || StringUtils.isBlank(bomData.get(BomConstants.FIELD_CORE_ID, String.class))) {
            return;
        }
        IObjectData coreData = serviceFacade.findObjectData(controllerContext.getUser(), bomData.get(BomConstants.FIELD_CORE_ID, String.class), Utils.BOM_CORE_API_NAME);
        if (Objects.isNull(coreData)) {
            return;
        }
        objectData.set(BomConstants.FIELD_CORE_ID, coreData.getId());
        objectData.set("core_id__r", coreData.getName());
        objectData.set("core_id__relation_ids", coreData.getId());
    }
}
