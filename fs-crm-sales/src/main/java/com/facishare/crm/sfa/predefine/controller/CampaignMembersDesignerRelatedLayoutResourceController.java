package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.CampaignMembersUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerRelatedLayoutResourceController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/11/24
 * @apiNote
 */
public class CampaignMembersDesignerRelatedLayoutResourceController extends StandardDesignerRelatedLayoutResourceController {
    public static List<String> referenceObjectApiNames = Lists.newArrayList(Utils.LEADS_API_NAME, Utils.CONTACT_API_NAME,
            Utils.ACCOUNT_API_NAME);

    @Override
    protected List<IButton> getRelatedNormalButtons() {
        List<IButton> buttonList = super.getRelatedNormalButtons();
        if (CollectionUtils.isEmpty(buttonList)) {
            return buttonList;
        }
        buttonList.removeIf(m -> ObjectAction.INTELLIGENTFORM.getActionCode().equals(m.getAction()));
        if (!Utils.MARKETING_EVENT_API_NAME.equals(arg.getLookupObjectApiName())) {
            return buttonList;
        }
        Optional<IButton> buttonOptional = buttonList.stream()
                .filter(m -> ObjectAction.CREATE.getActionCode().equals(m.getAction()))
                .findFirst();
        buttonOptional.ifPresent(m -> {
            buttonList.remove(m);
            referenceObjectApiNames.forEach(n -> {
                IButton button = CampaignMembersUtil.createButton(ObjectAction.CREATE.getActionCode() + n,
                        I18N.text(SFAI18NKeyUtil.SFA_UDOBJ_ACTION_SAVEFROMOBJECT,
                                I18N.text(n + ".attribute.self.display_name")), n);
                buttonList.add(button);
            });
        });
        return buttonList;
    }

    @Override
    protected List<IButton> getBatchButtons() {
        List<IButton> buttonList = super.getBatchButtons();
        if (CollectionUtils.isEmpty(buttonList)) {
            return buttonList;
        }
        buttonList.removeIf(m -> ObjectAction.PRINT.getActionCode().equals(m.getAction()));
        return buttonList;
    }
}
