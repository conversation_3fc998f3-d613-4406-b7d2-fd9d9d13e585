package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.utilities.util.ObjectUtils;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AccountMainDataWebDetailController extends SFAWebDetailController {
    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if(ObjectUtils.allNotEmpty(result) && ObjectUtils.allNotEmpty(result.getLayout()) ){
            ILayout newLayout = result.getLayout().toLayout();
            newLayout = removeComponents(newLayout);
            result.setLayout(LayoutDocument.of(newLayout));
        }
        return result;
    }

    public ILayout removeComponents(ILayout layout){
        try {
            log.warn("removeComponents tenantId->{}, userId->{}, dataId->{}", controllerContext.getTenantId(), controllerContext.getUser().getUpstreamOwnerIdOrUserId(), arg.getObjectDataId());
            if (RequestUtil.isMobileOrH5Request() || RequestUtil.isWXMiniProgram() || AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(controllerContext.getAppId())) {
                WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("account_main_sub_data_org_hierarchy"));
                log.warn("removeComponents tenantId is mobile ->{}, userId->{}, dataId->{}", controllerContext.getTenantId(), controllerContext.getUser().getUpstreamOwnerIdOrUserId(), arg.getObjectDataId());
            }
        } catch (Exception e) {
            log.error("AccountMainDataWebDetailController  removeComponents error tenantId->{}, userId->{}, dataId->{}",
                    controllerContext.getTenantId(), controllerContext.getUser().getUpstreamOwnerIdOrUserId(), arg.getObjectDataId(), e);
        }
        return layout;
    }
}
