package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.MarketingAttributionUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.ui.layout.ILayout;


/**
 * Created by yuanjl on 2021/12/2.
 */
public class MarketingEventInfluenceRelatedListController extends StandardRelatedListController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (result == null) {
            return result;
        }
        if (result.getLayout() != null) {
            ILayout layout = result.getLayout().toLayout();
            MarketingAttributionUtil.specialLayout(controllerContext.getObjectApiName(), arg.getTargetObjectApiName(), controllerContext.getUser(), layout);
			if (RequestUtil.isMobileOrH5Request()) {
				layout.getButtons().removeIf(b -> ObjectAction.RECALCULATE.getActionCode().equals(b.getAction()));
			}
        }
        return result;
    }
}
