package com.facishare.crm.sfa.predefine.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.GetImportObjectList;
import com.facishare.paas.appframework.metadata.importobject.MatchingType;

import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider.UPDATE_IMPORT;

public class BankStatementImportObjectController extends StandardImportObjectController {

    @Override
    protected GetImportObjectList.ImportObjectDTO getImportObject(String objectCode) {
        GetImportObjectList.ImportObjectDTO result = super.getImportObject(objectCode);
        result.setIsOpenWorkFlow(true);
        Map<String, List<MatchingType>> matchingTypes = result.getMatchingTypes();
        matchingTypes.put(UPDATE_IMPORT, Lists.newArrayList(MatchingType.ID, MatchingType.NAME, MatchingType.SPECIFIED_FIELD));
        return result;
        }
}
