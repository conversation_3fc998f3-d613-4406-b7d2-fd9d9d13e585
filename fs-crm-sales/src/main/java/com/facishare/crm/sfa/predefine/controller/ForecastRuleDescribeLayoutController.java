package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.ForecastRuleConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;

import java.util.Collections;

import static com.facishare.paas.appframework.metadata.layout.LayoutTypes.ADD;
import static com.facishare.paas.appframework.metadata.layout.LayoutTypes.EDIT;

public class ForecastRuleDescribeLayoutController extends SFADescribeLayoutController {
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (formComponent != null && (EDIT.equals(arg.getLayout_type()) || ADD.equals(arg.getLayout_type()))) {
            PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Collections.singletonList(ForecastRuleConstants.Field.FORECAST_OBJECT_API_NAME));
        }
    }
}

