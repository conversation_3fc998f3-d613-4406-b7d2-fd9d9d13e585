package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.log.dto.ObjectInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

public class LeadsNewLogInfoListForWebController extends StandardNewLogInfoListForWebController {

	@Override
	protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
		LogRecord logRecord = super.modifyRecordToLogRecord(record);
		// 归集
		if (ActionType.COLLECTED_TO.getId().equals(record.getOperationType())) {
			LogInfo logInfo = record.getLogInfo();
			if (logInfo != null && logInfo.getSnapshot() != null) {
				ObjectInfo.ObjectData data = ObjectInfo.ObjectData.builder()
						.dataName(StringUtils.isNotEmpty(logInfo.getSnapshot().getDataName()) ? logInfo.getSnapshot().getDataName() : "---")
						.dataId(logInfo.getSnapshot().getObjectId()).build();
				ObjectInfo objectInfo = ObjectInfo.builder()
						.objectLabel(logInfo.getSnapshot().getDisplayName())
						.objectApiName(logInfo.getSnapshot().getObjectApiName())
						.objectDatas(Lists.newArrayList(data)).build();
				logRecord.setObjectInfo(objectInfo);
				// 打开详情页
				logRecord.setSnapShotType(2);
			}
		}
		// 处理工作流和审批流的退回转移动作
		LogUtil.moveAndReturnFlowRecord(record, logRecord);
		return logRecord;
	}

}
