package com.facishare.crm.sfa.predefine.enums;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/23 10:55
 * @description:
 */
public enum CompanyStatusEnum {
    UNLIMITED(-1, "不限"),
    NORMAL(0, "正常"),
    REVOKED(1, "吊销"),
    CANCELLED(2, "注销"),
    SUSPENDED(3, "休止活动"),
    MOVED_OUT(4, "迁出"),
    REVOKED_2(5, "撤销"),
    CLOSED(6, "停业"),
    TERMINATED_BUSINESS_PLACE(7, "已终止营业地点"),
    MOVED_IN(8, "迁入"),
    LIQUIDATION(9, "清算"),
    ABOLISHED(10, "废止"),
    BANKRUPT(11, "破产"),
    OTHER(12, "其他");

    private Integer value;
    private String label;

    CompanyStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static CompanyStatusEnum fromValue(Integer value) {
        for (CompanyStatusEnum companyStatus : values()) {
            if (companyStatus.value.equals(value)) {
                return companyStatus;
            }
        }
        return null;
    }
}
