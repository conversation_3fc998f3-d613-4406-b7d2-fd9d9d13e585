package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.PartnerConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.GroupComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

@Slf4j
public class ContactNewDetailController extends SFANewDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);

        ObjectDataDocument objectData = result.getData();
        //web处理是否决策人这个字段类型时，无法采用字符串方式
        Object primaryContact = objectData.get("primary_contact");
        if (primaryContact != null && primaryContact instanceof Integer) {
            objectData.put("primary_contact", String.valueOf(primaryContact));
            if (Objects.equals(primaryContact, 0)) {
                objectData.put("primary_contact", "");
            }
        }
        handleInvalidBirthDayField(result);
        handleInvalidCardField(newResult);

        if (newResult.getLayout() != null) {
            ILayout layout = new Layout(newResult.getLayout());
            specialLogicForLayout(layout);
        }
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        //1、如果是合作伙伴业务类型，布局中隐藏客户字段、外部来源、外部负责人字段,同时相关对象列表中移除预制的关联联系人的页签
        //2、如果不是合作伙伴业务类型，布局中隐藏合作伙伴字段
        if (PartnerConstants.RECORD_TYPE_CONTACT_PARTNER.equals(this.data.getRecordType())) {
            removeDetailInfoFields(layout, Lists.newArrayList(PartnerConstants.ACCOUNT_ID,
                    PartnerConstants.OUT_RESOURCES, "enable_partner_view"));
            doSupportPartnerRelateTab(layout);
        } else {
            removeDetailInfoFields(layout, Lists.newArrayList(PartnerConstants.OWNED_PARTNER_ID));
            removeLayoutTopInfoField(layout, Lists.newArrayList(PartnerConstants.OWNED_PARTNER_ID));
        }

        //合作伙伴联系人  移除 是否合作伙伴可见 按钮
        modifyLayoutButtonAddPartnerViewBtn(newResult.getLayout().toLayout(), this.data);

        //A版企业干掉工单对象
        GetVersion.VersionInfo versionInfo = serviceFacade.getVersionInfo(controllerContext.getTenantId());
        if (versionInfo != null) {
            LayoutUtils.handleRelatedCasesObjForNewDetail(versionInfo.getCurrentVersion(), "CasesObj_contact_id_related_list", layout);
        }
        if (newResult.getData() != null) {
            ContactUtil.concatenateBirthDay(Lists.newArrayList(result.getData().toObjectData()));
        }
        LeadsUtils.handleLeadsRelatedComponentsForNewDetail(layout,"LeadsObj_contact_id_related_list");
        LeadsUtils.handleLeadsTransferLogRelatedComponentsForNewDetail(layout, "LeadsTransferLogObj_contact_id_related_list");
        if(newResult.getData().get("mobile__s")!=null){//手机字段不可见，同时隐藏手机1-5
            ContactUtil.deleteLayout(newResult.getLayout(),"mobile");
            ContactUtil.hideTelAndMobile(Lists.newArrayList(newResult.getData()),"mobile");
        }
        if(newResult.getData().get("tel__s")!=null){//电话字段不可见，同时隐藏电话1-5
            ContactUtil.deleteLayout(newResult.getLayout(),"tel");
            ContactUtil.hideTelAndMobile(Lists.newArrayList(newResult.getData()),"tel");
        }
        if(layout != null){
            LayoutDocument sortedLayout = this.getSortedLayout(layout, SFAPreDefineObject.Contact.getApiName());
            if(sortedLayout != null){
                newResult.setLayout(sortedLayout);
            }
        }
        if (RequestUtil.isMobileRequest()) {
            AccountUtil.removeRFMResultCard(layout);
        }
        return newResult;
    }

    private void specialLogicForLayout(ILayout layout) {
        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
        LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m-> {
            GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent)m);
            try {
                List<IComponent> ch_comps  = groupComponent.getChildComponents();
                removeLeadsObjButtons(ch_comps);
                ch_comps.stream().filter((component) -> {
                    return component instanceof IFormComponent;
                }).map((component) -> {
                    return FormComponentExt.of((IFormComponent)component);
                }).findFirst().ifPresent(com->{
                    FormComponent formComponent = (FormComponent) com.getFormComponent();
                    ContactUtil.setTelMobileFieldProperty(controllerContext.getUser(), Utils.OPPORTUNITY_API_NAME, formComponent);
                });
                groupComponent.setChildComponents(ch_comps);
            } catch (MetadataServiceException e) {
                log.error("getComponents error", e);
            }
        });
        removeMobileClientAndPcButtons(layout, clientInfo);
    }

    protected void doSupportPartnerRelateTab(ILayout layout) {
        LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m-> {
            GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent)m);
            try {
                List<IComponent> ch_comps  = groupComponent.getChildComponents();
                ch_comps.removeIf(childComponent -> {
                    String relatedListName = childComponent.get("related_list_name", String.class);
                    return org.apache.commons.lang3.StringUtils.isNotBlank(relatedListName) && !relatedListName.endsWith("__c");
                });
                groupComponent.setChildComponents(ch_comps);

            } catch (MetadataServiceException e) {
                log.error("getComponents error", e);
            }
        });
    }

    public void modifyLayoutButtonAddPartnerViewBtn(ILayout layout, IObjectData objectData) {
        if (objectData == null || layout == null) {
            return;
        }
        if (ObjectDataExt.of(objectData).isLock()) {
            return;
        }
        if (!Strings.isNullOrEmpty(objectData.getRecordType()) && objectData.get("record_type", String.class).equals(PartnerConstants.RECORD_TYPE_CONTACT_PARTNER)) {
            List<IButton> buttons = layout.getButtons();
            if (CollectionUtils.notEmpty(buttons)) {
                buttons.removeIf(f -> "enable_partner_view__c".equals(f.getAction()));
                layout.setButtons(buttons);
            }
        }

    }

    private void handleInvalidBirthDayField(Result result) {
        //处理生日字段 生日字段可能存在 0000-00-00/0000-12-17/1991-10-00//1991-00-00这四种状态,需要特殊处理
        String birthDay = (String) result.getData().get("date_of_birth");
        if (StringUtils.isEmpty(birthDay) || "0000-00-00".equals(result.getData().get("date_of_birth"))) {
            result.getData().put("date_of_birth", "");
        } else if (birthDay.startsWith("0000-")) {
            result.getData().put("date_of_birth", birthDay.replaceAll("0000-", ""));
        } else {
            result.getData().put("date_of_birth", birthDay.replaceAll("-00", ""));
        }
    }

    private void handleInvalidCardField(Result newResult) {
        Object card = (newResult.getData()).get("card");
        if (card instanceof List) {
            List cards = (List) card;
            if (cards.isEmpty()) {
                newResult.getData().put("card", Lists.newArrayList());
            } else {
                cards.stream().findFirst().ifPresent(x -> {
                    if (x instanceof Map) {
                        Object path = ((Map) x).get("path");
                        if (path == null || "".equals(path)) {
                            newResult.getData().put("card", Lists.newArrayList());
                        }
                    } else {
                        newResult.getData().put("card", Lists.newArrayList());
                    }
                });
            }
        }
    }

    private void removeMobileClientAndPcButtons(ILayout layout, String clientInfo) {
        List<IButton> buttons = layout.getButtons();
        ContactUtils.removeCommonButtons(buttons);

        //手机端需要做的特殊处理
        if (!Strings.isNullOrEmpty(clientInfo) && RequestUtil.isMobileOrH5Request()) {
            ContactUtils.removePCButtons(buttons);
        }else{
            // PC 端过来的请求，移除移动端的按钮
            ContactUtils.removePhoneButtons(buttons);
        }
        layout.setButtons(buttons);
    }

    private void removeLeadsObjButtons(List<IComponent> childComponents) {
        childComponents.stream().forEach(childComponent -> {
            if ("LeadsObj".equals(childComponent.get("ref_object_api_name", String.class))) {
                List<IButton> childComponentButtons = childComponent.getButtons();
                childComponentButtons.removeIf(button -> ObjectAction.CREATE.getActionCode().equals(button.getAction())
                        || ObjectAction.BULK_RELATE.getActionCode().equals(button.getAction())
                        || ObjectAction.BULK_DISRELATE.getActionCode().equals(button.getAction()));
                childComponent.setButtons(childComponentButtons);
            }
        });
    }
}
