package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListCountController;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;


public class AccountListCountController extends StandardListCountController {
    private ISearchTemplate searchTemplate;
	private ObjectPoolPermission.ObjectPoolPermissions poolPermissions;
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
		User user = controllerContext.getUser();
		searchTemplate = serviceFacade.findSearchTemplateByIdAndType(user, this.getSearchTemplateId(), objectDescribe.getApiName(), arg.getSearchTemplateType());
		poolPermissions = SearchListUtil.getHighSeasPermissionByUserId(user,
				SearchListUtil.getHighSeaIdByQuery(query, searchTemplate.getExtendAttribute()));
        SearchListUtil.handleAccountSearchQuery(user, query, searchTemplate, poolPermissions);
        return query;
    }

    @Override
    protected Query defineQuery() {
        Query searchQuery = super.defineQuery();
        searchTemplate = searchQuery.getSearchTemplate();
        return searchQuery;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
		String extendAttribute = "";
		if (searchTemplate != null) {
			extendAttribute = searchTemplate.getExtendAttribute();
		}
		String leadsPoolId = SearchListUtil.getHighSeaIdByQuery(searchQuery, extendAttribute);
		if (poolPermissions == null) {
			poolPermissions = SearchListUtil.getHighSeasPermissionByUserId(controllerContext.getUser(), leadsPoolId);
		}
        SearchListUtil.handleAccountSupportOrSearchQuery(controllerContext.getUser(), searchQuery, searchTemplate,  poolPermissions);
        return searchQuery;
    }
}
