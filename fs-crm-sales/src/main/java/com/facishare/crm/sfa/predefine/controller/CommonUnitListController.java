package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.core.util.RequestUtil;

public class CommonUnitListController extends StandardListController {
    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_710)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT.getMessage(), SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
    }
}
