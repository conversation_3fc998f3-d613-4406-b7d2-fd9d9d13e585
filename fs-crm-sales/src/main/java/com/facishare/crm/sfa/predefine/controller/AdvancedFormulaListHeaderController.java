package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public class AdvancedFormulaListHeaderController extends StandardListHeaderController {
    private static final List<String> filter_buttons = Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        if (Objects.isNull(res.getLayout())) {
            return res;
        }
        ILayout layout = new Layout(res.getLayout());
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(x -> (filter_buttons.contains(x.getAction())));
        layout.setButtons(buttons);
        return res;
    }


}
