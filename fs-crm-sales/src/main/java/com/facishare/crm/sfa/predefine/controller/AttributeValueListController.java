package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;

/**
 * Created by luxin on 2018/11/30.
 */
public class AttributeValueListController extends StandardListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        List<OrderBy> orderBys = query.getOrders();
        OrderBy orderBy = new OrderBy("order_field", true);
        orderBys.add(orderBy);
        return query;
    }
}
