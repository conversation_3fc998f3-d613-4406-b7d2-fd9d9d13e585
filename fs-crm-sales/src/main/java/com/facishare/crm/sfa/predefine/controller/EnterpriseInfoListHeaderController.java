package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;


/**
 * Created by zhangtao on 2019/1/2.
 */
public class EnterpriseInfoListHeaderController extends BaseSFAListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //修改 button
        ButtonUtils.editMergeButton(result);
        return result;
    }
}
