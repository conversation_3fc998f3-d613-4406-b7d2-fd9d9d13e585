package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 市场活动详情 class
 *
 * <AUTHOR>
 * @date 2019/2/27
 */

@Slf4j
public class MarketingEventWebDetailController extends SFAWebDetailController {
    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);
    private final static MetaDataService metaDataService = SpringUtil.getContext().getBean(MetaDataServiceImpl.class);

    @Override
    protected ILayout getLayout() {
        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(Utils.MARKETING_EVENT_API_NAME),
                Lists.newArrayList(ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));
        ILayout layout = super.getLayout();
        if (layout != null) {
            try {
                List<IComponent> componentList = layout.getComponents();
                if (!componentList.isEmpty()) {
                    Optional<IComponent> attachComponent = layout.getComponents().stream().filter(x -> ATTACH_COMPONENT.equals(x.getName())).findFirst();
                    if(attachComponent.isPresent()) {
                        if (objApiNameAndActionCodePrivilegeMapping.get(Utils.MARKETING_EVENT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                            if (objApiNameAndActionCodePrivilegeMapping.get(Utils.MARKETING_EVENT_API_NAME).get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
                                List<IButton> buttons = getButtons();
                                WebDetailLayout.of(layout).addButtons(buttons, ATTACH_COMPONENT);
                            }
                        } else {
                            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList(ATTACH_COMPONENT));
                        }
                    }

                    layout.setComponents(componentList);
                }
            } catch (MetadataServiceException ex) {
                log.warn("MarketingEventWebDetailController->getLayout  error", ex);
            }
        }

        return layout;
    }

    private List<IButton> getButtons() {
        Map<String, Map<String, Permissions>> privilege = metaDataService.checkDataPrivilege(
                controllerContext.getUser(), Lists.newArrayList(data), ObjectDescribeExt.of(describe),
                Lists.newArrayList(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

        List<IButton> buttons = Lists.newArrayList();
        if (privilege.containsKey(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
            Map<String, Permissions> permissions = privilege.get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode());
            if (permissions.containsKey(arg.getObjectDataId())
                    && permissions.get(arg.getObjectDataId()).equals(Permissions.READ_WRITE)) {
                IButton addButton = new Button();
                addButton.setAction("Add");
                addButton.setActionType("default");
                addButton.setLabel(I18N.text(I18NKey.action_upload));
                addButton.setName("MarketingEventAttObj_Add_button_default");
                buttons.add(addButton);
                IButton deleteButton = new Button();
                deleteButton.setAction("Delete");
                deleteButton.setActionType("default");
                deleteButton.setLabel(I18N.text(I18NKey.action_delete));
                deleteButton.setName("MarketingEventAttObj_Delete_button_default");
                buttons.add(deleteButton);
            }
        }
        return buttons;
    }
}
