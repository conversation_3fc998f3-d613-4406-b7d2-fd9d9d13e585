package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.lto.procurement.qianlima.QlmProcurementService;
import com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.fxiaoke.functions.utils.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;


@Slf4j
public class BiddingSubscriptionRulesDescribeLayoutController extends StandardDescribeLayoutController {

    @Override
    protected String getLayoutApiName() {
        if (BiddingSubscriptionRulesConstants.QLM.equals(arg.getRecordType_apiName())) {
            return "layout_qlm__c";
        }
        return "BiddingSubscriptionRulesObj_layout_generate_by_UDObjectServer__c";
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        // 千里马添加1、2级行业组合描述，千里马订阅器和搜索页面需要的描述
        if (BiddingSubscriptionRulesConstants.QLM.equals(arg.getRecordType_apiName())) {
            Map fieldsMap = (Map) result.getObjectDescribe().get("fields");
            fieldsMap.put("one_level_two", Maps.of(
                    "label", I18N.text("ProcurementInfoObj.field.caller_type.label"),
                    "api_name", "one_level_two",
                    "label__r", I18N.text("ProcurementInfoObj.field.caller_type.label"),
                    "type", "mycascader",
                    "options", QlmProcurementService.getI18nOneAndTwoIndustries(controllerContext.getLang())
            ));
        }
        return result;
    }

}
