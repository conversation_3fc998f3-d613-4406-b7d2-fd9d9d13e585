package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

/**
 * 行为积分明细 list
 * <AUTHOR>
 */
public class BehaviorIntegralDetailListController extends StandardListController {
    @Override
    protected void doFunPrivilegeCheck() {
        //Don't check function privilege
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        //Don't check data privilege
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        searchTemplateQuery.setDataRightsParameter(null);
        return searchTemplateQuery;
    }
}
