package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

public class CouponInstanceImportViewController extends StandardImportViewController {

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        // Remove fields that are not supported for import
        if(arg.getImportType() == IMPORT_TYPE_ADD) {
            if(describe.getApiName().equals(CouponConstants.COUPON_INSTANCE_API_NAME)) {
                fieldDescribes.removeIf(field -> CouponConstants.IMPORT_CAN_NOT_SUPPORT_FIELD.contains(field.getApiName()));
            }
        }
    }
} 