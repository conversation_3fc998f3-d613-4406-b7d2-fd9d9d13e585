package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.BiddingRuleService;
import com.facishare.crm.sfa.predefine.service.SettingRuleMemberService;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementRuleMemberModel;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.UseRangeFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Optional;

public class BiddingSubscriptionRulesWebDetailController extends SFAWebDetailController {

    private static final BiddingRuleService BIDDING_RULE_SERVICE = SpringUtil.getContext().getBean(BiddingRuleService.class);
    private static final SettingRuleMemberService settingRuleMemberService = SpringUtil.getContext().getBean(SettingRuleMemberService.class);

    private static final UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext().getBean(UseRangeFieldDataRender.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }


    @Override
    protected Result doService(Arg arg) {
        return super.doService(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (result.getLayout() != null) {
            Optional.ofNullable(result.getData()).ifPresent(data -> {
                describe.getFieldDescribes().stream().filter(k -> IFieldType.UseRange.equals(k.getType()))
                        .forEach(k -> {
                            data.put(k.getApiName(),
                                    useRangeFieldDataRender.render(data.get(k.getApiName()),
                                            ((UseRangeFieldDescribe) k).getTargetApiName()));
                        });
            });
        }
        addRuleMember();
        return super.after(arg, result);
    }

    private void addRuleMember(){
        List<ProcurementRuleMemberModel> procurementRuleMembers = settingRuleMemberService.getSettingRuleMember(controllerContext.getTenantId(),
                controllerContext.getUser().getUpstreamOwnerIdOrUserId(), data.getId(), SFAPreDefineObject.BiddingSubscriptionRules.getApiName());
        data.set("member_list", procurementRuleMembers);
    }
}
