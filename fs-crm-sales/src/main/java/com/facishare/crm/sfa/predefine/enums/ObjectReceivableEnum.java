package com.facishare.crm.sfa.predefine.enums;

public enum ObjectReceivableEnum {
    SalesOrderObj("SalesOrderObj", "销售订单"),
    DeliveryNoteObj("DeliveryNoteObj", "发货单"),
    GoodsReceivedNoteObj("GoodsReceivedNoteObj", "入库单"),
    RebateObj("RebateObj", "返利单");

    private final String value;
    private final String label;

    ObjectReceivableEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
