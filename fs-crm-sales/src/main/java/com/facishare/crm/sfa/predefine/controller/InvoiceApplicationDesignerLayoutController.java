package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/1/20
 */
public class InvoiceApplicationDesignerLayoutController extends StandardDesignerLayoutController {

    private final InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);

    private static final String COMPONENT_API_NAME = "invoice_app_sales_order_multi_related_list";

    private static final String MULTI_INVOICE_SALES_ORDER_JSON = "{\n" +
            "    \"buttons\": [],\n" +
            "    \"api_name\": \"invoice_app_sales_order_multi_related_list\",\n" +
            "    \"is_hidden\": false,\n" +
            "    \"header\": \"销售订单\",\n" +
            "    \"type\": \"invoice_app_sales_order_multi_related_list\",\n" +
            "    \"ref_object_api_name\": \"SalesOrderObj\",\n" +
            "    \"related_list_name\": \"invoice_app_sales_order_multi_related_list\",\n" +
            "    \"nameI18nKey\": \"SalesOrderObj.attribute.self.display_name\",\n" +
            "    \"order\": 999\n" +
            "}";

    @Override
    protected void processLayout(ILayout layout) {
        super.processLayout(layout);
        if (Objects.isNull(layout)) {
            return;
        }
        if (!LayoutExt.of(layout).isDetailLayout()) {
            return;
        }
        if (CollectionUtils.nullToEmpty(layout.getHiddenComponents()).contains(COMPONENT_API_NAME)) {
            return;
        }
        if (!invoiceService.isNewInvoiceOpen(controllerContext.getUser())) {
            return;
        }
        addInvoiceAppLayoutStructure(layout);
    }

    private void addInvoiceAppLayoutStructure(ILayout layout) {
        try {
            List<IComponent> components = LayoutExt.of(layout).getComponentsSilently();
            IComponent existComponent = components.stream()
                    .filter(x -> Objects.equals(x.getName(), COMPONENT_API_NAME))
                    .findFirst()
                    .orElse(null);
            if (existComponent != null) {
                return;
            }
            IComponent multiComponent = ComponentFactory.newInstance(MULTI_INVOICE_SALES_ORDER_JSON);
            multiComponent.setHeader(I18N.text("SalesOrderObj.attribute.self.display_name"));
            WebDetailLayout.of(layout).addComponents(Lists.newArrayList(multiComponent), 9999);
        } catch (Exception e) {
            log.error("addInvoiceAppLayoutStructure error tenantId {}", controllerContext.getTenantId(), e);
        }
    }
}
