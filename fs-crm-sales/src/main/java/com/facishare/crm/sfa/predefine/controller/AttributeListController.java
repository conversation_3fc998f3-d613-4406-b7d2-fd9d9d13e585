package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by luxin on 2018/11/30.
 */
public class AttributeListController extends StandardListController {
    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> mobileLayouts = super.findMobileLayouts();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(mobileLayouts)) {
            return mobileLayouts;
        }
        for (ILayout layout : mobileLayouts) {
            layout.set("buttons", Lists.newArrayList());
        }
        return mobileLayouts;
    }
}
