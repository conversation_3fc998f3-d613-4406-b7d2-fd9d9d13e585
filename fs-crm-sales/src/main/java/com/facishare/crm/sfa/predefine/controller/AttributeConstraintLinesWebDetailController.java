package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

/**
 * @描述说明： 屏蔽按钮
 * @作者：chench
 * @创建日期：2024-01-04
 */
public class AttributeConstraintLinesWebDetailController extends SFAWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        filterButtons(newResult.getLayout().toLayout());
        return newResult;
    }

    private void filterButtons(ILayout layout) {
        WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList("Edit", "Print"));
    }
}
