package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.translate.TranslateManager;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetSnapShotForWeb;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

public class CouponPlanSnapShotForWebController extends StandardSnapShotForWebController {

    private final TranslateManager translateManager = SpringUtil.getContext().getBean(TranslateManager.class);

    @Override
    protected GetSnapShotForWeb.Result doService(GetSnapShotForWeb.Arg arg) {
        GetSnapShotForWeb.Result result = super.doService(arg);
        render(result);
        return result;
    }

    private void render(GetSnapShotForWeb.Result result) {
        Optional.ofNullable(result)
                .map(GetSnapShotForWeb.Result::getData)
                .map(x -> ObjectDataExt.of(x).getObjectData())
                .ifPresent(data ->
                        translateManager.getTranslateService(data.get(CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName(), String.class))
                                .render(data, controllerContext.getUser())
                );
    }
}