package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.aggregatevalue.AggregateRuleService;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * 聚合规则布局特殊处理
 */
public class AggregateRuleDescribeLayoutController extends SFADescribeLayoutController {

    AggregateRuleService aggregateRuleService = SpringUtil.getContext().getBean(AggregateRuleService.class);

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        aggregateRuleService.buildAggregateObjectDescribe(controllerContext.getUser(), result.getObjectDescribe().toObjectDescribe());
        result.setObjectDescribe(result.getObjectDescribe());
    }

}
