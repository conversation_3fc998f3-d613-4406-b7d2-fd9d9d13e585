package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardValidRecordTypeController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.impl.DocumentBasedBean;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class EnterpriseRiskValidRecordTypeController extends StandardValidRecordTypeController {

    @Override
    protected Result doService(Arg arg) {
        Result result = new Result();
        if (arg.isForImport()) {
            if (ObjectDescribeExt.UnsupportedImportRecordTypeDescribes.contains(controllerContext.getObjectApiName())) {
                return result;
            }
        }
        List<IRecordTypeOption> recordTypeList = serviceFacade.findRecordTypeOptionList(controllerContext.getTenantId(), controllerContext.getObjectApiName(), true);
        List<Map> dataList = recordTypeList.stream().map(recordType -> (((DocumentBasedBean) recordType).getContainerDocument())).collect(Collectors.toList());
        result.setRecordList(dataList);
        return result;
    }

}
