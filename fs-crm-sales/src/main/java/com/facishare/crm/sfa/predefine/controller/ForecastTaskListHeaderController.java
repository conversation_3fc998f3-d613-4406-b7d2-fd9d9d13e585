package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.ForecastRuleConstants;
import com.facishare.crm.sfa.utilities.constant.ForecastTaskConstants;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ForecastTaskListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        String tenantId = controllerContext.getTenantId();
        String forecastRuleId = arg.getTargetObjectDataId();
        IObjectData rule = serviceFacade.findObjectData(User.systemUser(tenantId), forecastRuleId, SFAPreDefineObject.ForecastRule.getApiName());
        // objectDescribe.fields
        IObjectDescribe objectDescribe = objectDescribeExt.copy();
        // layout.components.0.field_section
        FormComponent formComponent = null;
        try {
            List<IComponent> components = result.getLayout().toLayout().getComponents();
            for (IComponent component : components) {
                if (component instanceof FormComponent) {
                    formComponent = (FormComponent) component;
                    break;
                }
            }
        } catch (MetadataServiceException e) {
            log.error("", e);
            return result;
        }
        if (formComponent == null) {
            log.error("formComponent is null");
            return result;
        }
        // 根据规则动态返回列
        showFieldsByRule(rule, arg.getTargetObjectApiName(), objectDescribe, formComponent);
        // 在template.all加上GROUP_RESULT_LABEL
        addGroupResultLabel(result);
        result.setObjectDescribe(ObjectDescribeDocument.of(objectDescribe));
        return result;
    }

    private void showFieldsByRule(IObjectData rule, String targetObjectApiName, IObjectDescribe objectDescribe, FormComponent formComponent) {
        ObjectDescribeExt originFields = ObjectDescribeExt.of(objectDescribe);
        List<IFormField> newFormFields = new ArrayList<>();
        ObjectDescribeExt newFields = ObjectDescribeExt.of(new HashMap<>());
        IFieldSection fieldSection = formComponent.getFieldSections().get(0);
        List<IFormField> originFormFields = fieldSection.getFields();
        if (targetObjectApiName == null) {
            Integer dateSplitType = rule.get(ForecastRuleConstants.Field.FORECAST_DATE_SPLIT_TYPE, Integer.class, ForecastRuleConstants.DateSplitType.BY_MONTH.getValue());
            if (ForecastRuleConstants.DateSplitType.BY_MONTH.getValue().equals(dateSplitType)) {
                newFields.addFieldDescribe(originFields.getFieldDescribe(ForecastTaskConstants.Field.FORECAST_MONTH_GROUP));
            }
            if (ForecastRuleConstants.DateSplitType.BY_QUARTER.getValue().equals(dateSplitType)) {
                newFields.addFieldDescribe(originFields.getFieldDescribe(ForecastTaskConstants.Field.FORECAST_QUARTER_GROUP));
            }
        } else {
            if (SFAPreDefineObject.ForecastRule.getApiName().equals(targetObjectApiName)) {
                IFieldDescribe fieldDescribe = FieldDescribeExt.copy(originFields.getFieldDescribe(ForecastTaskConstants.Field.FORECAST_QUARTER_GROUP));
                fieldDescribe.setLabel(null);
                fieldDescribe.setDescription(null);
                fieldDescribe.setApiName(ForecastTaskListController.GROUP_RESULT_LABEL);
                newFields.addFieldDescribe(fieldDescribe);
                FormField formField = new FormField();
                formField.setFieldName(ForecastTaskListController.GROUP_RESULT_LABEL);
                formField.setReadOnly(false);
                formField.setRequired(false);
                formField.setRenderType("text");
                newFormFields.add(formField);
            }
        }
        parseAndPutBestPracticesForecastModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL1);
        parseAndPutBestPracticesForecastModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL2);
        parseAndPutBestPracticesForecastModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL3);
        parseAndPutBestPracticesForecastModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL4);
        parseAndPutBestPracticesForecastModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL5);
        parseAndPutBooleanFieldModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.ARTIFICIAL_COMMITMENT_FORECAST_MODEL);
        parseAndPutBooleanFieldModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.STAGE_WEIGHT_FORECAST_MODEL);
        parseAndPutBooleanFieldModel(newFields, originFields, newFormFields, originFormFields, rule, ForecastRuleConstants.Field.ARTIFICIAL_WEIGHT_FORECAST_MODEL);
        parseAndPutAiWeightForecastModel(newFields, originFields, newFormFields, originFormFields, rule);
        objectDescribe.set(IObjectDescribe.FIELDS, newFields.get(IObjectDescribe.FIELDS));
        fieldSection.setFields(newFormFields);
    }

    private void parseAndPutBestPracticesForecastModel(ObjectDescribeExt newFields, ObjectDescribeExt originFields, List<IFormField> newFormFields, List<IFormField> originFormFields, IObjectData rule, String fieldApiName) {
        String target = parseBestPracticesForecastModelName(rule.get(fieldApiName, String.class));
        if (target != null) {
            newFields.addFieldDescribe(replaceLabelAndDescription(originFields.getFieldDescribe(fieldApiName), target));
            newFormFields.add(findFormField(originFormFields, fieldApiName));
        }
    }

    private String parseBestPracticesForecastModelName(String raw) {
        JSONObject object = JSON.parseObject(raw);
        if (object == null) {
            return null;
        }
        return object.getString("name");
    }

    private void parseAndPutBooleanFieldModel(ObjectDescribeExt newFields, ObjectDescribeExt originFields, List<IFormField> newFormFields, List<IFormField> originFormFields, IObjectData rule, String fieldApiName) {
        boolean flag = rule.get(fieldApiName, Boolean.class, Boolean.FALSE);
        if (flag) {
            newFields.addFieldDescribe(originFields.getFieldDescribe(fieldApiName));
            newFormFields.add(findFormField(originFormFields, fieldApiName));
        }
    }

    private void parseAndPutAiWeightForecastModel(ObjectDescribeExt newFields, ObjectDescribeExt originFields, List<IFormField> newFormFields, List<IFormField> originFormFields, IObjectData rule) {
        Object flag = rule.get(ForecastRuleConstants.Field.AI_WEIGHT_FORECAST_MODEL);
        if (flag != null) {
            newFields.addFieldDescribe(originFields.getFieldDescribe(ForecastRuleConstants.Field.AI_WEIGHT_FORECAST_MODEL));
            newFormFields.add(findFormField(originFormFields, ForecastRuleConstants.Field.AI_WEIGHT_FORECAST_MODEL));
        }
    }

    private IFieldDescribe replaceLabelAndDescription(IFieldDescribe field, String target) {
        field.setDescription(target);
        field.setLabel(target);
        return field;
    }

    private IFormField findFormField(List<IFormField> formFields, String fieldName) {
        return formFields.stream().filter(formField -> formField.getFieldName().equals(fieldName)).findFirst().orElse(null);
    }

    @SuppressWarnings("rawtypes")
    private void addGroupResultLabel(Result res) {
        List<QueryTemplateDocument> templates = res.getTemplates();
        for (QueryTemplateDocument template : templates) {
            ISearchTemplate searchTemplate = template.toSearchTemplate();
            if ("All".equals(searchTemplate.getApiName())) {
                List<Map> fieldList = searchTemplate.getFieldList();
                Map<String, Object> map = new HashMap<>();
                map.put("field_name", ForecastTaskListController.GROUP_RESULT_LABEL);
                map.put("is_show", true);
                fieldList.add(0, map);
                break;
            }
        }
    }
}