package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;

import java.util.Arrays;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

public class AttributeDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if(arg.getLayout_type() ==null){
            return;
        }
        if(formComponent != null ) {
            switch (arg.getLayout_type()) {
                case LAYOUT_TYPE_EDIT:
                    PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Arrays.asList("status"));
                    break;
            }
        }
    }

}

