package com.facishare.crm.sfa.predefine.enums;

import java.util.Optional;

/**
 * 核销方式
 */
public enum MatchWayEnum {
    AccountsReceivableNote(1, "按应收单核销"),
    AccountsReceivableDetail(2, "按应收单明细核销");

    private int status;
    private String message;

    MatchWayEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public static Optional<MatchWayEnum> get(int status) {
        for (MatchWayEnum switchEnum : MatchWayEnum.values()) {
            if (switchEnum.status == status) {
                return Optional.of(switchEnum);
            }
        }
        return Optional.empty();
    }

    public int getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }
}