package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.enums.ObjectApiNameEnum;
import com.facishare.crm.sfa.predefine.service.PoolSearchTemplateService;
import com.facishare.paas.appframework.core.predef.controller.StandardOuterSceneListController;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2024-03-07 14:57
 * @Description
 */
public class LeadsOuterSceneListController extends StandardOuterSceneListController {
    private final PoolSearchTemplateService poolSearchTemplateService = SpringUtil.getContext().getBean(PoolSearchTemplateService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        List<? extends IScene> filterScene = poolSearchTemplateService.filterSceneByLeadsPool(controllerContext.getUser(), arg.getExtraParams(), after.getScenes());
        after.setScenes(filterScene);
        Map extendResult = poolSearchTemplateService.buildExtendResultData(controllerContext.getUser(),
                ObjectApiNameEnum.LeadsPool.getApiName(),
                after.getScenes());
        after.setExtraResult(extendResult);
        return after;
    }
}
