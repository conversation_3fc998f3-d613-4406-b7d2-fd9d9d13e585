package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.api.IdGenerator;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ForecastTaskWebDetailController extends SFAWebDetailController {

    @Override
    protected IObjectData findObjectData(Arg arg) {
        ObjectData objectData = new ObjectData();
        objectData.setId(IdGenerator.get());
        return objectData;
    }

}
