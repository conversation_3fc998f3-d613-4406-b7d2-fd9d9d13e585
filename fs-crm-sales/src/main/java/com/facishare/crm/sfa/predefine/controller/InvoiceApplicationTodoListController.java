package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.crm.sfa.utilities.util.InvoiceApplicationUtil;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

public class InvoiceApplicationTodoListController extends SFATodoListController {

    @Override
    protected void buildUnProcessedQuery(SearchTemplateQuery query) {
        InvoiceApplicationUtil.handleSearchQuery(query, controllerContext.getUser());
    }


    @Override
    protected void buildProcessedQuery(SearchTemplateQuery query) {
        List<String> owners = InvoiceApplicationUtil.getObjectOwnerIdList(controllerContext.getUser());
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName("life_status");
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(SystemConstants.LifeStatus.Normal.value);
        fieldValues.add(SystemConstants.LifeStatus.Ineffective.value);
        filter.setFieldValues(fieldValues);
        filter.setOperator(Operator.IN);
        if (owners.isEmpty()) {
            owners.add("");
        }
        filters.add(filter);
        IFilter filterIsFixedFlow = new Filter();
        filterIsFixedFlow.setFieldName(InvoiceApplicationConstants.InvoiceApplicationField.IS_FIXED_FLOW.getApiName());
        List<String> filedValueIsFixedFlow = Lists.newArrayList();
        filedValueIsFixedFlow.add("false");
        filterIsFixedFlow.setFieldValues(filedValueIsFixedFlow);
        filterIsFixedFlow.setOperator(Operator.EQ);
        filters.add(filterIsFixedFlow);
        SearchUtil.fillFilterIn(filters, "owner", new ArrayList<>(owners));
        query.setFilters(filters);
    }
}
