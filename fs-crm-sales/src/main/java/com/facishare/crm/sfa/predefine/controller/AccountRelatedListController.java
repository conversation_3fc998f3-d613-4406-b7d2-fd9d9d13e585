package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.custom.MarketRuleService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.AccountMultiKeyWordSearchUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by renlb on 2018/11/28.
 */
public class AccountRelatedListController extends SFARelatedListController {
    private static final MarketRuleService marketRuleService = SpringUtil.getContext().getBean(MarketRuleService.class);

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        // 多关键词搜索灰度
        if (GrayUtil.isAccountMultiKeyWordSearchUtil(controllerContext.getTenantId())) {
            AccountMultiKeyWordSearchUtil.handleSearchQuery(controllerContext.getUser(), query, "or");
        }
        return query;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
        if (GrayUtil.isAccountMultiKeyWordSearchUtil(controllerContext.getTenantId())) {
            AccountMultiKeyWordSearchUtil.handleSearchQuery(controllerContext.getUser(), searchQuery, "or");
        }
        AccountUtil.buildMultiLevelOrderSearchQuery(controllerContext.getUser(), searchQuery, arg.getRelatedListName(), arg.getMasterData());

        //----客开：营销工作台客开begin----
        ObjectDataDocument objectDataDocument = arg.getObjectData();
        if (objectDataDocument != null && SFAPreDefineObject.ServiceMarket.getApiName().equals(String.valueOf(objectDataDocument.get("object_describe_api_name")))) {
            buildSearchQuery(searchQuery);
        }
        //----客开：营销工作台end----
        return searchQuery;
    }

    @Override
    protected Result after(StandardRelatedListController.Arg arg, Result result) {
        super.after(arg, result);
        List<ObjectDataDocument> dataList = result.getDataList();

        List<IObjectData> objectDataList = dataList.stream().map(x -> x.toObjectData()).collect(Collectors.toList());
        AccountUtil.handleIsRemindRecycling(objectDataList);
        dataList = objectDataList.stream().map(x -> ObjectDataDocument.of(x)).collect(Collectors.toList());
        result.setDataList(dataList);
        return result;
    }

    public void buildSearchQuery(SearchTemplateQuery query) {
        List<String> marketAccountIds = marketRuleService.findAccountIdByMarketRule(controllerContext.getTenantId());
        if (CollectionUtils.isEmpty(marketAccountIds)) {
            marketAccountIds.add("");
        }
        log.info("findAccountIdByMarketRule:{}", marketAccountIds);
        List<IFilter> filters = query.getFilters();
        if (filters == null) {
            filters = Lists.newArrayList();
        }
        SearchUtil.fillFilterIn(filters, IObjectData.ID, marketAccountIds);
        query.setFilters(filters);
    }
}