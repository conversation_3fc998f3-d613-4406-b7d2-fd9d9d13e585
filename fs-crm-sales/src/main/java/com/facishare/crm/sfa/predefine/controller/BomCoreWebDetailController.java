package com.facishare.crm.sfa.predefine.controller;

import com.beust.jcommander.internal.Sets;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.NonstandardAttributeService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeCoreService;
import com.facishare.crm.sfa.predefine.service.attribute.AttributeRangeService;
import com.facishare.crm.sfa.predefine.service.attribute.model.Attribute;
import com.facishare.crm.sfa.predefine.service.attribute.model.AttributeRangeMode;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.AttributeUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.DBRecord.ID;


public class BomCoreWebDetailController extends SFAWebDetailController {

    private final NonstandardAttributeService nonstandardAttributeService = SpringUtil.getContext().getBean(NonstandardAttributeService.class);
    private final AttributeCoreService attributeCoreService = SpringUtil.getContext().getBean(AttributeCoreService.class);
    private final AttributeRangeService attributeRangeService = SpringUtil.getContext().getBean(AttributeRangeService.class);

    private static final List<String> FILTER_BUTTON = Lists.newArrayList(ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode(), ObjectAction.CLONE.getActionCode(), ObjectAction.ADVANCED_FORMULAS.getActionCode(), ObjectAction.SET_ATTR_RANGE.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }

        ILayout layout = new Layout(newResult.getLayout());
        filterButtons(layout);

        removeButtons(layout);

        IObjectData objectData = Optional.ofNullable(result.getData()).map(ObjectDataDocument::toObjectData).orElse(null);
        List<Attribute> attributeList = Lists.newArrayList();
        List<IObjectData> nonAttributeList = Lists.newArrayList();
        if (objectData != null) {
            handleAttr(objectData,attributeList,nonAttributeList);
            handleChildAttr(objectData);
            handleAttrRange(objectData);
            try {
                attributeRangeService.handleBomRootNodeAttrRange(data, (List<String>) data.get("rangeIds"), data.get(AttributeRangeMode.ATTR_RANGE), attributeList, nonAttributeList);
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }
        newResult.setLayout(LayoutDocument.of(layout));
        return newResult;
    }

    private void handleAttrRange(IObjectData objectData) {
        objectData.set(BomConstants.FIELD_CORE_ID, objectData.getId());
        attributeRangeService.queryAttrRange(controllerContext.getUser(), Lists.newArrayList(objectData), Utils.BOM_CORE_API_NAME);
    }

    private void handleAttr(IObjectData objectData, List<Attribute> attributeList, List<IObjectData> nonAttributeList) {
        if (data == null) {
            return;
        }
        String productId = objectData.get(BomConstants.FIELD_PRODUCT_ID, String.class);
        if (StringUtils.isBlank(productId)) {
            return;
        }
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(controllerContext.getTenantId(), Lists.newArrayList(productId), Utils.PRODUCT_API_NAME);
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        Map<String, List<Attribute>> attributeMap = attributeCoreService.getAttributeByProductIds(controllerContext.getUser(), null, false, dataList);
        attributeList.addAll(CollectionUtils.nullToEmpty(attributeMap.get(productId)));
        objectData.set(AttributeConstants.ATTRIBUTE, attributeMap.get(productId));
        Map<String, List<IObjectData>> nonstandardAttributeMap = nonstandardAttributeService.getNonstandardAttributeDataByProductIds(controllerContext.getUser(), null, dataList);
        nonAttributeList.addAll(CollectionUtils.nullToEmpty(nonstandardAttributeMap.get(productId)));
        List<Map<String, Object>> dataMapList = AttributeUtils.setAttr(nonstandardAttributeMap.get(productId));
        data.set(AttributeConstants.NON_ATTRIBUTE, dataMapList);
    }

    private void handleChildAttr(IObjectData objectData) {
        SearchTemplateQuery query = getSearchTemplateQuery(Lists.newArrayList(objectData.getId()));
        List<IObjectData> dataList = findBySearchQueryIgnoreAll(controllerContext.getUser(), Utils.BOM_API_NAME, query);
        if (CollectionUtils.empty(dataList)) {
            objectData.set(AttributeConstants.CHILD_NON_ATTRIBUTE, Lists.newArrayList());
            objectData.set(AttributeConstants.CHILD_ATTRIBUTE, Lists.newArrayList());
            return;
        }
        Set<String> productIds = Sets.newHashSet();
        Set<String> relatedCoreIds = Sets.newHashSet();
        Set<String> allRelatedCoreIds = Sets.newHashSet();
        dataList.forEach(x -> {
            productIds.add(x.get(BomConstants.FIELD_PRODUCT_ID, String.class));
            if (StringUtils.isNotBlank(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class))) {
                relatedCoreIds.add(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class));
                allRelatedCoreIds.add(x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class));
            }
        });
        if (CollectionUtils.notEmpty(relatedCoreIds)) {
            querySubBomList(relatedCoreIds, allRelatedCoreIds, productIds);
        }
        query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterIn(filters, ID, Lists.newArrayList(productIds));
        List<IObjectData> productList = findBySearchQueryIgnoreAll(controllerContext.getUser(), Utils.PRODUCT_API_NAME, query);
        if (CollectionUtils.notEmpty(productList)) {
            Map<String, List<Attribute>> attributeMap = attributeCoreService.getAttributeByProductIds(controllerContext.getUser(), null, false, productList);
            objectData.set(AttributeConstants.CHILD_ATTRIBUTE, attributeMap.values().stream().flatMap(Collection::stream)
                    .collect(Collectors.toList()));
            Map<String, List<IObjectData>> nonstandardAttributeMap = nonstandardAttributeService.getNonstandardAttributeDataByProductIds(controllerContext.getUser(), null, productList);
            List<Map<String, Object>> dataMapList = AttributeUtils.setAttr(nonstandardAttributeMap.values().stream().flatMap(Collection::stream)
                    .collect(Collectors.toList()));
            data.set(AttributeConstants.CHILD_NON_ATTRIBUTE, dataMapList);
        }
    }

    private void querySubBomList(Set<String> relatedCoreIds, Set<String> allRelatedCoreIds, Set<String> productIds) {
        List<IObjectData> dataList = findBySearchQueryIgnoreAll(controllerContext.getUser(), Utils.BOM_API_NAME, getSearchTemplateQuery(Lists.newArrayList(relatedCoreIds)));
        relatedCoreIds = Sets.newHashSet();
        String relatedCoreId;
        if (CollectionUtils.notEmpty(dataList)) {
            for (IObjectData x : dataList) {
                productIds.add(x.get(BomConstants.FIELD_PRODUCT_ID, String.class));
                relatedCoreId = x.get(BomConstants.FIELD_RELATED_CORE_ID, String.class);
                if (StringUtils.isNotBlank(relatedCoreId) && !allRelatedCoreIds.contains(relatedCoreId)) {
                    relatedCoreIds.add(relatedCoreId);
                }
            }
        }
        if (CollectionUtils.notEmpty(relatedCoreIds)) {
            allRelatedCoreIds.addAll(relatedCoreIds);
            querySubBomList(relatedCoreIds, allRelatedCoreIds, productIds);
        }
    }

    private SearchTemplateQuery getSearchTemplateQuery(List<String> coreId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = query.getFilters();
        if (coreId.size() == 1) {
            SearchUtil.fillFilterEq(filters, BomConstants.FIELD_CORE_ID, coreId.get(0));
        } else {
            SearchUtil.fillFilterIn(filters, BomConstants.FIELD_CORE_ID, coreId);
        }
        SearchUtil.fillFilterIsNotNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
        return query;
    }

    private List<IObjectData> findBySearchQueryIgnoreAll(User user, String objectApiName, SearchTemplateQuery query) {
        List<IObjectData> result = Lists.newArrayList();
        // 每次查询个数
        int limitSize = 2000;
        query.setLimit(limitSize);
        // 当前循环次数
        int loop = 0;
        int queryDataSize;
        // 最多循环10次
        do {
            query.setOffset(loop++ * limitSize);
            List<IObjectData> dataList = serviceFacade.findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
            queryDataSize = dataList.size();
            result.addAll(dataList);
        } while (queryDataSize >= limitSize && loop < 10);
        return result;
    }


    private void filterButtons(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        List<IButton> buttons = layoutExt.getButtons();
        if (RequestUtil.isMobileOrH5Request() || RequestUtil.isMobileDeviceRequest()) {
            buttons.removeIf(b -> FILTER_BUTTON.contains(b.getAction()));
            WebDetailLayout.of(layout).setButtons(buttons);
        }
        if (SFAConfigUtil.isSimpleBom(controllerContext.getTenantId())) {
            buttons.removeIf(b -> FILTER_BUTTON.contains(b.getAction()));
            WebDetailLayout.of(layout).setButtons(buttons);
        }
    }

    private void removeButtons(ILayout layout) {
        if (!SFAConfigUtil.isOpenBomCoreExplosionDiagramSwitch(controllerContext.getTenantId())) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.BOM_CORE_CONFIG_OCR.getActionCode()));
        }
    }

}
