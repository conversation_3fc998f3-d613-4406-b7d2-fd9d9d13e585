package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class BomImportViewController extends StandardImportViewController {
    private final List<String> filterFields = Lists.newArrayList("parent_bom_id", "bom_path", "root_id", BomConstants.FIELD_NODE_BOM_CORE_VERSION, BomConstants.FIELD_NODE_BOM_CORE_TYPE, BomConstants.FIELD_IS_PACKAGE);

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (StringUtils.equals(describe.getApiName(), Utils.BOM_API_NAME) && arg.getImportType() == IMPORT_TYPE_EDIT) {
            fieldDescribes.removeIf(field-> BomConstants.REMOVE_FIELD_FOR_UPDATE_IMPORT.contains(field.getApiName()));
        }
    }
}
