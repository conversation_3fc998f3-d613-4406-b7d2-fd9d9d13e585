package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.predef.controller.StandardCRMObjectListController;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/18
 * @apiNote
 */
public class BehaviorRecordCRMObjectListController extends StandardCRMObjectListController {

    private static List<String> RETAIN_OBJECT_LIST = Lists.newArrayList(Utils.LEADS_API_NAME);

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        result.getObjectList().removeIf(m -> !RETAIN_OBJECT_LIST.contains(m.getApiName()));
        return result;
    }
}
