package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.Optional;

/**
 * bom 详情页
 *
 * <AUTHOR>
 * @date 2020/04/30
 */
@Slf4j
public class BomDetailController extends StandardDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        ILayout iLayout = Optional.ofNullable(newResult).map(x->x.getLayout()).map(x->x.toLayout()).orElse(null);
        if (Objects.isNull(iLayout)) {
            return newResult;
        }
        iLayout.setButtons(Lists.newArrayList());
        LayoutExt.of(iLayout).getRelatedComponent().ifPresent(x -> {
            try {
                x.getChildComponents().forEach(childComponent -> {
                    childComponent.setButtons(Lists.newArrayList());
                });
            } catch (MetadataServiceException e) {
                log.error("getChildComponents error");
            }
        });
        return newResult;
    }
}
