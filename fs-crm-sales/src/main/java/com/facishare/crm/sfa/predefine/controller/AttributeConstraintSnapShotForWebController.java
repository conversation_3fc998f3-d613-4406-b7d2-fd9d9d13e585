package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Map;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-01-29
 */
public class AttributeConstraintSnapShotForWebController extends StandardSnapShotForWebController {
    @Override
    protected Map getSnapShotLayout(Map<String, Object> objData, IObjectDescribe objectDescribe, ControllerContext context) {
//        Map map = super.getSnapShotLayout(objData, objectDescribe, context);
//        LayoutExt layoutExt = LayoutExt.of(map);
//        Optional<IComponent> component = layoutExt.getComponentByApiName("form_component");
//        if(component.isPresent())  {
//            List sections = component.get().get("field_section", List.class, Lists.newArrayList());
//            for (int i = 0; i < sections.size(); i++) {
//                Map section = (Map) sections.get(i);
//                if("constraint_rule_field_section__c".equals(section.get("api_name"))) {
//                    sections.remove(i);
//                    break;
//                }
//            }
//        }
        return super.getSnapShotLayout(objData, objectDescribe, context);
    }
}
