package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetNewLogInfoListForWeb;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;

import java.util.List;

/**
 * 优惠券方案修改记录
 *
 * <AUTHOR>
 * @date 2022/01/13
 */
public class CouponPlanNewLogInfoListForWebController extends StandardNewLogInfoListForWebController {

    @Override
    protected GetNewLogInfoListForWeb.Result doService(GetNewLogInfoListForWeb.Arg arg) {
        GetNewLogInfoListForWeb.Result result = super.doService(arg);
        List<LogRecord> recordList = result.getModifyRecordList();
        if (CollectionUtils.empty(recordList)) {
            return result;
        }
        CouponUtils.renderRecordList(recordList, controllerContext.getUser(),
                CouponConstants.COUPON_PLAN_API_NAME,
                CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName(),
                CouponConstants.CouponPlanField.PRODUCT_CONDITION_CONTENT.getApiName());
        return result;
    }

}
