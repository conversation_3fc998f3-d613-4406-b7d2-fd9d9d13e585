package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

/**
 * 行为积分明细 detail
 * <AUTHOR>
 */
public class BehaviorIntegralDetailDetailController extends StandardDetailController {
    @Override
    protected void doFunPrivilegeCheck() {
        //Don't check function privilege
    }

    @Override
    protected void doDataPrivilegeCheck(Arg arg) {
        //Don't check data privilege
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        //清空详情页按钮
        layout.setButtons(Lists.newArrayList());
        //只保存详细信息卡片
        LayoutExt.of(layout).getDetailInfoComponent().ifPresent(
                detailInfoComponent -> layout.setComponents(Lists.newArrayList(detailInfoComponent)));
        return layout;
    }
}
