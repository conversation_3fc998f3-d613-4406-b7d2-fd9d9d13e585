package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @date 2019/12/11 11:49 上午
 */
@Slf4j
public class AmortizeInfoWebDetailController extends SFAWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        removeDetailButtons(layout);
        return newResult;
    }

    private void removeDetailButtons(ILayout layout) {
        WebDetailLayout.of(layout).removeButtonsByActionCode(SalesOrderConstants.SALES_ORDER_PRODUCT_WEB_DETAIL_REMOVE_ACTIONS);
    }

}
