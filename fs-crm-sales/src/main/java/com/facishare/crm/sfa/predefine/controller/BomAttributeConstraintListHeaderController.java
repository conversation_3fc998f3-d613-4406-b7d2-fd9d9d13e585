package com.facishare.crm.sfa.predefine.controller;


import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by yuepy
 */
public class BomAttributeConstraintListHeaderController extends SFAListHeaderController {
    private final List<String> removeActionList = Lists.newArrayList(
            ObjectAction.BULK_RELATE.getActionCode(),
            ObjectAction.BULK_DISRELATE.getActionCode(),
            ObjectAction.CREATE.getActionCode());

    boolean related = false;

    @Override
    protected void before(Arg arg) {
        if ("related".equals(arg.getListType())) {
            related = true;
        }
        super.before(arg);
    }

    @Override
    protected List<IButton> getButtons() {
        List<IButton> buttons = super.getButtons();
        if (related) {
            buttons.removeIf(x -> removeActionList.contains(x.getAction()));
        } else {
            buttons.removeIf(x -> StringUtils.equals(x.getName(), "Import_button_default"));
        }
        return buttons;
    }


    @Override
    protected boolean needRemoveMobileButton() {
        return true;
    }
}
