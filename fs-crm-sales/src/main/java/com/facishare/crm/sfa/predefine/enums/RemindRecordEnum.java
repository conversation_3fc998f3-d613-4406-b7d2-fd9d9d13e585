package com.facishare.crm.sfa.predefine.enums;

/**
 * Created by renlb on 2019/6/12.
 */
public enum RemindRecordEnum {
    ORDER_MODIFIED(30, "销售订单被修改"),
    ORDER_CONFIRMED(15, "销售订单被确"),
    ORDER_REJECTED(16, "销售订单被驳回"),
    ORDER_RECALLED(34, "销售订单被撤回"),
    RETURNED_ORDER_CONFIRMED(45, "退货单被确认"),
    RETURNED_ORDER_REJECTED(46, "退货单被驳回"),
    RETURNED_ORDER_RECALLED(47, "退货单被撤回");


    private final Integer value;
    private final String desc;

    RemindRecordEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return desc;
    }
}