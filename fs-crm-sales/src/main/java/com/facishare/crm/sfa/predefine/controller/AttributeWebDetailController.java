package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.AttributeConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class AttributeWebDetailController extends SFAWebDetailController {
    private  List<String> removeActionList = Lists.newArrayList(
            ObjectAction.INVALID.getActionCode(),
            ObjectAction.LOCK.getActionCode(),
            ObjectAction.UNLOCK.getActionCode(),
            ObjectAction.CONFIRM.getActionCode(),
            ObjectAction.REJECT.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.CLONE.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.CREATE.getActionCode(),
            ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(),
            ObjectAction.CHANGE_PARTNER.getActionCode(),
            ObjectAction.DELETE_PARTNER.getActionCode(),
            ObjectAction.AttributeEnable.getActionCode(),
            ObjectAction.AttributeDisEnable.getActionCode());
    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        if (Objects.isNull(rst.getLayout())) {
            return rst;
        }
        ILayout layout = new Layout(result.getLayout());

        processEnableButton(layout, data);

        return rst;
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (RequestUtil.isMobileRequest() || RequestUtil.isH5Request()) {
            removeMobileButton(layout);
        }
        return layout;
    }
    public void removeMobileButton(ILayout layout) {
        if (layout == null) {
            return;
        }
        List<IButton> buttons = layout.getButtons();
        if (CollectionUtils.empty(buttons)) {
            return;
        }
        buttons.removeIf(x -> removeActionList.contains(x.getAction()));
        layout.setButtons(buttons);
    }
    private void processEnableButton(ILayout layout, IObjectData data) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
        if (!headInfoComponentOp.isPresent()) {
            return;
        }

        IComponent headInfoComponent = headInfoComponentOp.get();
        List<IButton> buttons = headInfoComponent.getButtons();
        boolean hasEnablePrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.ATTRIBUTE_OBJ_API_NAME, ObjectAction.AttributeEnable.getActionCode());
        boolean hasDisEnablePrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.ATTRIBUTE_OBJ_API_NAME, ObjectAction.AttributeDisEnable.getActionCode());
        if (!hasEnablePrivilege) {
            buttons.removeIf(b -> b.getAction().equals(ObjectAction.AttributeEnable.getActionCode()));
        }
        if (!hasDisEnablePrivilege) {
            buttons.removeIf(b -> b.getAction().equals(ObjectAction.AttributeDisEnable.getActionCode()));
        }
        if (data.get("status", String.class).equals(AttributeConstants.Status.ON.getStatus())) {
            buttons.removeIf(b -> b.getAction().equals(ObjectAction.AttributeEnable.getActionCode()));
        } else {
            buttons.removeIf(b -> b.getAction().equals(ObjectAction.AttributeDisEnable.getActionCode()));
        }
        WebDetailLayout.of(layout).setButtons(buttons);
    }
}
