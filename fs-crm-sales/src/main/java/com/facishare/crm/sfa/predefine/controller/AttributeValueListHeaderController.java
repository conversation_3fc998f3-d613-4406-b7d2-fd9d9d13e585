package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 属性值只保留导入按钮
 *
 * <AUTHOR>
 */
public class AttributeValueListHeaderController extends StandardListHeaderController {
    private final List<String> invisible_buttons = Lists.newArrayList("Import_button_default","Export_button_default","ExportFile_button_default","ExportFile","IntelligentForm_button_default");

    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        List<IButton> btnList = result.getLayout().toLayout().getButtons();
        btnList.removeIf(o -> !invisible_buttons.contains(o.get("api_name")));
        result.getLayout().toLayout().setButtons(btnList);
        //result.getButtons().removeIf(d -> d.get("api_name").equals("Export_button_default"));
        return result;
    }
}
