package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.AccountsReceivableNoteObjConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class AccountsReceivableDetailDescribeLayoutController extends SFADescribeLayoutController {
    /**
     * 编辑时，客户只读
     */
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        String layoutType = arg.getLayout_type();
        List<FormComponent> list = LayoutExt.of(result.getLayout()).getFormComponents().stream().map(FormComponentExt::getFormComponent).filter(FormComponent.class::isInstance).map(FormComponent.class::cast).collect(Collectors.toList());
        for (FormComponent component : list) {
            if (layoutType.equals(LayoutTypes.EDIT) || layoutType.equals(LayoutTypes.ADD)) {
                PreDefLayoutUtil.removeSomeFields(component, Sets.newHashSet("receivable_object_data_id", "receivable_object_detail_data_id"));
            }
        }
    }
}
