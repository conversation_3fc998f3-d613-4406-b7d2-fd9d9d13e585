package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.SfaListHeaderUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;


/**
 * Created by renlb on 2018/12/25.
 */
public class AmortizeInfoListHeaderController extends StandardListHeaderController {

    protected FormComponent formComponent;

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        ILayout layout = new Layout(res.getLayout());
        SfaListHeaderUtil.specialLogicForLayout(layout,SfaListHeaderUtil.SALES_ORDER_PRODUCT_WHITE_LIST_BUTTONS);
        SfaListHeaderUtil.specialButtons(res.getButtons(),SfaListHeaderUtil.SALES_ORDER_PRODUCT_WHITE_LIST_BUTTONS);
        return res;
    }
}
