package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.LogInfoRecordService;
import com.facishare.crm.sfa.utilities.util.InteractionStrategyNewLogInfoUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetNewLogInfoListForWeb;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * InteractionStrategyNewLogInfoListForWebController
 *
 * <AUTHOR>
 */
public class InteractionStrategyNewLogInfoListForWebController extends StandardNewLogInfoListForWebController {
    private static final LogInfoRecordService LOG_INFO_RECORD_SERVICE = SpringUtil.getContext().getBean(LogInfoRecordService.class);

    @Override
    protected GetNewLogInfoListForWeb.Result doService(GetNewLogInfoListForWeb.Arg arg) {
        GetNewLogInfoListForWeb.Result result = super.doService(arg);
        List<LogRecord> recordList = result.getModifyRecordList();
        LOG_INFO_RECORD_SERVICE.renderRecordList(recordList, controllerContext.getUser(), arg.getApiName(), arg.getObjectId(), InteractionStrategyNewLogInfoUtil.consumer);
        return result;
    }
}