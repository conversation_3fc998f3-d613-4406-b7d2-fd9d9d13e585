package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;

public class BehaviorRecordRelatedListController extends StandardRelatedListController {

    @Override
    protected void before(Arg arg) {
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_780)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
    }
}
