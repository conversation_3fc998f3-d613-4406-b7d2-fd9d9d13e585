package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Optional;

@Slf4j
public class LeadsFlowRecordNewDetailController extends SFANewDetailController {
    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void doDataPrivilegeCheck(Arg arg) {
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null || newResult.getData() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        layout.setButtons(Lists.newArrayList());
        try {
            Optional<GroupComponent> optionalCom = layout.getComponents().stream().filter(x -> "detailInfo".equals(x.getName())).map(x ->
                    (GroupComponent) x
            ).findFirst();
            layout.getComponents().clear();
            if (optionalCom.isPresent()) {
                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m -> {
                    IGroupComponent groupComponent = (IGroupComponent) m;
                    groupComponent.setButtons(Lists.newArrayList());
                    groupComponent.get("child_components", ArrayList.class).clear();
                });
            }
        } catch (MetadataServiceException e) {
            log.error("getChildComponents error", e);
        }

        return newResult;
    }
}
