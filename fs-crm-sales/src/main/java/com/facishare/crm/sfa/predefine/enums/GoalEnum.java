package com.facishare.crm.sfa.predefine.enums;
/**
 * Created by zhaopx on 2018/4/25.
 */
public interface GoalEnum {
    enum GoalTypeValue {

        CIRCLE("1", "部门"), EMPLOYEE("2", "人员"), ACCOUNT("AccountObj", "客户"), PRODUCT("ProductObj", "产品"), PartnerObj("PartnerObj", "合作伙伴");

        private String value;
        private String label;

        GoalTypeValue(String value, String label) {
            this.value = value;
            this.label = label;
        }
//todo test
        public String getValue() {
            return this.value;
        }

        public String getLabel() {
            return this.label;
        }
    }

  enum ThemeTypeValue {

    PERSON("PersonnelObj", "人员"), ACCOUNT("AccountObj", "客户"), PRODUCT("ProductObj", "产品"), DEPT("DepartmentObj", "部门"), PartnerObj("PartnerObj", "合作伙伴");

    private String value;
    private String label;

    ThemeTypeValue(String value, String label) {
      this.value = value;
      this.label = label;
    }

    public String getValue() {
      return this.value;
    }

    public String getLabel() {
      return this.label;
    }

    public static ThemeTypeValue getEnumByValue(String value) {
      for (ThemeTypeValue typeValue : ThemeTypeValue.values()) {
        if (typeValue.getValue().equals(value)) {
          return typeValue;
        }
      }
      return PERSON;
    }
  }

  enum GoalCheckCycle {

    MONTH("month", "月"), WEEK("week", "周"), QUARTER("quarter", "季度"), YEAR("year", "年");

    private final String value;
    private final String label;

    GoalCheckCycle(String value, String label) {
      this.value = value;
      this.label = label;
    }
    public String getValue() {
      return this.value;
    }

    public String getLabel() {
      return this.label;
    }
  }
}
