package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

@Slf4j
public class CouponInstanceDescribeLayoutController extends SFADescribeLayoutController {
    private static final BizConfigThreadLocalCacheService configCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (LAYOUT_TYPE_ADD.equals(arg.getLayout_type()) || LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())) {
            removeUsedTime(result);
            CouponUtils.formFieldConsumer(result, fields -> {
                for (IFormField field : fields) {
                    if (CouponConstants.CouponInstanceField.ACCOUNT_ID.getApiName().equals(field.getFieldName())) {
                        //开启纸质优惠券，设置不必填，不开启设置必填
                        field.setRequired(!configCacheService.isOpenPaperCoupon(controllerContext.getTenantId()));
                    }
                }
            });

        }
        if(LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())){
            readOnlyPaperCouponNo(result);
        }
        return result;
    }

    private void readOnlyPaperCouponNo(Result result) {
        CouponUtils.formFieldConsumer(result, fields -> {
            for (IFormField field : fields) {
                if (CouponConstants.CouponInstanceField.PAPER_COUPON_NO.getApiName().equals(field.getFieldName())) {
                    field.setReadOnly(true);
                }
            }
        });
    }

    private void removeUsedTime(Result result) {
        CouponUtils.formFieldConsumer(result, fields ->
                fields.removeIf(field -> CouponConstants.CouponInstanceField.SEND_TIME.getApiName().equals(field.getFieldName()))
        );

    }

    /**
     * 不支持保存草稿
     *
     * @return boolean
     */
    @Override
    protected boolean supportSaveDraft() {
        return false;
    }

}
