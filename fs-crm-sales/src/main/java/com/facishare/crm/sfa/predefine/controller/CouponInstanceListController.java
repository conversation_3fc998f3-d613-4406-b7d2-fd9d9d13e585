package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class CouponInstanceListController extends StandardListController {
    private FunctionPrivilegeServiceImpl functionPrivilegeService = SpringUtil.getContext().getBean(FunctionPrivilegeServiceImpl.class);
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> mobileLayouts = super.findMobileLayouts();
        if (CollectionUtils.empty(mobileLayouts)) {
            return mobileLayouts;
        }
        for (ILayout layout : mobileLayouts) {
            layout.set("buttons", Lists.newArrayList());
        }
        return mobileLayouts;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        handleDataButton(after);
        return after;
    }

    private void handleDataButton(Result result) {
        if (!bizConfigThreadLocalCacheService.isOpenIncentive(controllerContext.getTenantId())) {
            return;
        }
        Map<String, Boolean> funPrivilegeMap = functionPrivilegeService.funPrivilegeCheck(controllerContext.getUser(), CouponConstants.COUPON_INSTANCE_API_NAME,
                Lists.newArrayList(ObjectAction.UPDATE.getActionCode()));
        List<ButtonDocument> buttonDescribes = Lists.newArrayList();
        List<String> buttons = Lists.newArrayList();
        if (Boolean.TRUE.equals(funPrivilegeMap.get(ObjectAction.UPDATE.getActionCode()))) {
            buttons.add("Redeem_button_default");
            buttonDescribes.add(getButton());
        }
        if (CollectionUtils.empty(buttons)) {
            return;
        }
        ButtonInfo buttonInfo = Optional.ofNullable(result.getButtonInfo())
                .orElse(new ButtonInfo());
        buttonInfo.setButtonMap(getButtonMap(result, buttons, buttonInfo));
        buttonInfo.setButtons(getButton(buttonDescribes, buttonInfo));
        result.setButtonInfo(buttonInfo);
    }

    private List<ButtonDocument> getButton(List<ButtonDocument> buttonDocuments, ButtonInfo buttonInfo) {
        List<ButtonDocument> originalButtonDocuments = buttonInfo.getButtons();
        if (CollectionUtils.notEmpty(originalButtonDocuments)) {
            buttonDocuments.addAll(originalButtonDocuments);
        }
        return buttonDocuments;
    }

    @NotNull
    private Map<String, List<String>> getButtonMap(Result result, List<String> buttons, ButtonInfo buttonInfo) {
        Map<String, List<String>> buttonMap = buttonInfo.getButtonMap();
        if (CollectionUtils.empty(buttonMap)) {
            buttonMap = Optional.ofNullable(result)
                    .map(Result::getDataList)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(data -> StringUtils.isNotBlank(data.getId()))
                    .collect(Collectors.toMap(ObjectDataDocument::getId, d -> buttons));
        } else {
            buttonMap.forEach((id, buttonList) -> buttonList.addAll(0, buttons));
        }
        return buttonMap;
    }

    public ButtonDocument getButton() {
        IButton button = new Button();
        button.setAction("Redeem");
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        button.setName("Redeem_button_default");
        button.setLabel(I18N.text("sfa.coupon.instance.redeem"));
        return ButtonDocument.fromButton(button);
    }
}