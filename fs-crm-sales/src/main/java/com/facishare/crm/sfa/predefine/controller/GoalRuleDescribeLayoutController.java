package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.GoalRuleConstants;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.GoalRuleObj;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Calendar;
import java.util.List;
import java.util.Map;

public class GoalRuleDescribeLayoutController extends StandardDescribeLayoutController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);

        setCountFiscalYearOptions(result.getObjectDescribe().toObjectDescribe());
        rebuildLayout(result.getLayout());
        return result;
    }

    private void rebuildLayout(LayoutDocument layoutDescribe) {
      List<Map<String, Object>> documents = (List) layoutDescribe.get("components");
      for (Map<String, Object> document : documents) {
        if (document.get("api_name").equals("form_component")) {
          List<Map<String, Object>> fieldSections = (List) document.get("field_section");
          if (fieldSections != null && fieldSections.size() != 0) {
            for (Map<String, Object> fieldSection : fieldSections) {
              if (fieldSection.get("api_name").equals("base_field_section__c")) {
                List<Map<String, Object>> formFields = (List) fieldSection.get("form_fields");
                if (CollectionUtils.notEmpty(formFields)) {
                  formFields.add(2, GoalRuleConstants.GOAL_RULE_LAYOUT_CHECK_OBJECT_FORM_FIELD);
                  formFields.add(3, GoalRuleConstants.GOAL_RULE_LAYOUT_CHECK_LEVEL_FORM_FIELD);
                  formFields.add(6, GoalRuleConstants.GOAL_RULE_LAYOUT_START_QUARTER);
                  formFields.add(7, GoalRuleConstants.GOAL_RULE_LAYOUT_START_WEEK);
                  formFields.add(8, GoalRuleConstants.GOAL_RULE_LAYOUT_CHECK_CYCLE);

                  formFields.add(10, GoalRuleConstants.GOAL_RULE_LAYOUT_PERSONNEL_RELATION_FORM_FIELD);
                  formFields.add(11, GoalRuleConstants.GOAL_RULE_LAYOUT_DEPT__FORM_FIELD);
                  formFields.add(9, GoalRuleConstants.GOAL_RULE_LAYOUT_CHECK_TIME_ZONE_FORM_FIELD);
                }
                break;
              }
            }
          }
          break;
        }
      }
    }



    private void setCountFiscalYearOptions(IObjectDescribe objectDescribe){
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(GoalRuleObj.COUNT_FISCAL_YEAR);
        fieldDescribe.set(SelectOneFieldDescribe .OPTIONS,getFiscalYearOptions());
    }

    private List<Map<String,String>> getFiscalYearOptions(){
        List<Map<String,String>> optionList = Lists.newArrayList();

        Calendar c = Calendar.getInstance();
        int currentYear = c.get(Calendar.YEAR);
        for (int year = currentYear-1 ; year<= currentYear + 2; year++){
            Map<String,String> option = Maps.newHashMap();
            option.put(ISelectOption.OPTION_VALUE,String.valueOf(year) );
            option.put(ISelectOption.OPTION_LABEL,String.valueOf(year) );
            optionList.add(option);
        }

        return optionList;
    }
}
