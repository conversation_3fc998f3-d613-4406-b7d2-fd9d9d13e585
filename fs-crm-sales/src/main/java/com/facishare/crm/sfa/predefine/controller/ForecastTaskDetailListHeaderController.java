package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.ForecastRuleConstants;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.util.RecordTypeUtil;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ForecastTaskDetailListHeaderController extends StandardListHeaderController {

    private IObjectData forecastRule;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String forecastRuleId = arg.getTargetObjectDataId();
        String tenantId = controllerContext.getTenantId();
        forecastRule = serviceFacade.findObjectData(User.systemUser(tenantId), forecastRuleId, SFAPreDefineObject.ForecastRule.getApiName());
        String forecastObjectName = forecastRule.get(ForecastRuleConstants.Field.FORECAST_OBJECT_API_NAME, String.class);
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, forecastObjectName);
        objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        RecordTypeUtil.extendRecordTypeFieldWithValue(objectDescribeExt);
    }

    @Override
    protected List<String> getAuthorizedFields() {
        String jsonStr = forecastRule.get(ForecastRuleConstants.Field.FORECAST_OBJECT_FIELDS_SHOW, String.class);
        return JSON.parseArray(jsonStr, String.class);
    }

    @Override
    @SuppressWarnings("rawtypes")
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        List<QueryTemplateDocument> templates = res.getTemplates();
        ISearchTemplate all = null;
        for (QueryTemplateDocument template : templates) {
            ISearchTemplate searchTemplate = template.toSearchTemplate();
            if ("All".equals(searchTemplate.getApiName())) {
                all = searchTemplate;
                break;
            }
        }
        if (all == null) {
            all = new SearchTemplate();
            templates.add(QueryTemplateDocument.of(all));
        }
        List<String> fieldsShowNames = JSON.parseArray(forecastRule.get(ForecastRuleConstants.Field.FORECAST_OBJECT_FIELDS_SHOW, String.class, "[]"), String.class);
        if (!fieldsShowNames.isEmpty()) {
            List<Map> fieldList = new ArrayList<>();
            for (String fieldsShowName : fieldsShowNames) {
                Map<String, Object> map = new HashMap<>();
                map.put("field_name", fieldsShowName);
                map.put("is_show", true);
                fieldList.add(map);
            }
            all.setFieldList(fieldList);
        }
        return res;
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
