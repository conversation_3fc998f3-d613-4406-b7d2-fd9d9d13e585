package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.ModifyRecord;

public class AccountNewLogInfoListForMobController extends SFANewLogInfoListForMobController {

    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        LogUtil.moveAndReturnFlowRecord(record, logRecord);
        return logRecord;
    }

}
