package com.facishare.crm.sfa.predefine.enums;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum InvoiceTypeEnum {
    VAT_General_Invoice("VAT_General_Invoice", "增值税普通发票"),
    VAT_Special_Invoice("VAT_Special_Invoice", "增值税专用发票"),
    National_Tax_General_Machine_Printed_Invoice("National_Tax_General_Machine_Printed_Invoice", "国税通用机打发票"),
    General_Machine_Printed_Invoice_Of_Local_Tax("General_Machine_Printed_Invoice_Of_Local_Tax", "地税通用机打发票"),
    Receipt("Receipt", "收据");
    ;
    private final String value;
    private final String label;

    InvoiceTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static InvoiceTypeEnum getByCode(String status) {
        for (InvoiceTypeEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static List<String> getAllStatus() {
        List<String> allStatus = Lists.newArrayList();
        for (InvoiceTypeEnum d : InvoiceTypeEnum.values()) {
            allStatus.add(d.getValue());
        }
        return allStatus;
    }
}
