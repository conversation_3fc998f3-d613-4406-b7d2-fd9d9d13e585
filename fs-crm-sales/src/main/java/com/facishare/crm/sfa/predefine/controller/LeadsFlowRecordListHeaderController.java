package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.util.Safes;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.model.QueryTemplateDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

public class LeadsFlowRecordListHeaderController extends StandardListHeaderController {
    private static final List<String> listHeaderFields = Lists.newArrayList("leads_owner", "flow_type",
            "flow_time", "leads_status", "leads_status_changed_time", "leads_stage", "leads_stage_changed_time",
            "leads_owner_department", "leads_back_reason");

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        removeFields(res);
        return res;
    }

    private void removeFields(Result result) {
		LayoutExt layoutExt = LayoutExt.of(result.getLayout().toLayout());
		layoutExt.getFormComponent().ifPresent(formComponent -> {
			List<IFieldSection> fieldSections = formComponent.getFieldSections();
			handleFieldSectionList(fieldSections);
		});
		layoutExt.getTableComponent().ifPresent(tableComponent -> {
			List<IFieldSection> fieldSections = tableComponent.getFieldSections();
			handleFieldSectionList(fieldSections);
		});

		List<String> visibleFields = result.getVisibleFields();
		Safes.of(visibleFields).removeIf(x -> !listHeaderFields.contains(x) && !x.endsWith("__c"));
		result.setVisibleFields(visibleFields);
		List<DocumentBaseEntity> visibleFieldsWidth = result.getVisibleFieldsWidth();
		Safes.of(visibleFieldsWidth).removeIf(x -> !listHeaderFields.contains(x.get("field_name").toString()) && !x.get("field_name").toString().endsWith("__c"));
		result.setVisibleFieldsWidth(visibleFieldsWidth);
		List<QueryTemplateDocument> templates = result.getTemplates();
		if (CollectionUtils.notEmpty(templates)) {
			List<Map> fieldList;
			for (QueryTemplateDocument template : templates) {
				fieldList = (List<Map>) template.get("field_list");
				fieldList.removeIf(x -> !listHeaderFields.contains(x.get("field_name").toString()) && !x.get("field_name").toString().endsWith("__c"));
				template.put("field_list", fieldList);
			}
			result.setTemplates(templates);
		}
	}

	private void handleFieldSectionList(List<IFieldSection> fieldSections) {
		for (IFieldSection fieldSection : fieldSections) {
			List<IFormField> formFields = fieldSection.getFields();
			formFields.removeIf(f -> !listHeaderFields.contains(f.getFieldName()) && !f.getFieldName().endsWith("__c"));
			fieldSection.setFields(formFields);
		}
	}
}
