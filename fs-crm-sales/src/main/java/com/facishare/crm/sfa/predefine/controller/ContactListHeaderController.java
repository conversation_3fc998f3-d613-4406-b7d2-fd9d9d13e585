package com.facishare.crm.sfa.predefine.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.SpuConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * Created by zhangtao on 2019/1/2.
 */
public class ContactListHeaderController extends BaseSFAListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult == null || newResult.getLayout() == null) {
            return newResult;
        }
        if(RequestUtil.isMobileRequest()) {
            addMobileButton(newResult);
        }
        ILayout layout = new Layout((newResult.getLayout()));
        if(RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()){
            if (!AccountUtil.isOpenManyOrganizations(controllerContext.getUser(), null)) {
                layout.addButtons(ObjectAction.SCAN_CARD.createButton());
            }
        }
        //修改 button
        ButtonUtils.editMergeButton(result);
        return newResult;
    }

    private void addMobileButton(Result newResult) {
        ILayout layout = new Layout((newResult.getLayout()));
        layout.addButtons(ObjectAction.IMPORT_FROM_ADDRESS_BOOK.createButton());
    }
    @Override
    protected List<String> getAuthorizedFields() {
        List<String> fieldList = super.getAuthorizedFields();
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), SFAPreDefineObject.Contact.getApiName());
        if (unauthorizedFields != null && unauthorizedFields.contains("tel")) {
            fieldList.removeAll(Lists.newArrayList("tel1", "tel2", "tel3", "tel4", "tel5"));
        }
        if (unauthorizedFields != null && unauthorizedFields.contains("mobile")) {
            fieldList.removeAll(Lists.newArrayList("mobile1", "mobile2", "mobile3", "mobile4", "mobile5"));
        }
        return fieldList;
    }
}
