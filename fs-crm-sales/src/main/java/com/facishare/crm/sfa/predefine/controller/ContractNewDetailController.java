package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.metadata.GroupComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class ContractNewDetailController extends SFANewDetailController {
    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);


    @Override
    protected Result after(Arg arg, Result result){
        Result newResult = super.after(arg, result);
        if(result.getLayout() != null){
            ILayout layout = new Layout(newResult.getLayout());
            LayoutDocument sortedLayout = this.getSortedLayout(layout, SFAPreDefineObject.Contract.getApiName());
            if(sortedLayout != null){
                newResult.setLayout(sortedLayout);
            }
        }
        return newResult;
    }

    @Override
    protected ILayout getLayout() {

        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(Utils.CONTRACT_API_NAME),
                Lists.newArrayList(ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));
        ILayout layout = super.getLayout();
        LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(x->{
            GroupComponentExt groupComponentExt = GroupComponentExt.of((IGroupComponent)x);
            try {
                List<IComponent> childComponents = groupComponentExt.getChildComponents();
                if (!objApiNameAndActionCodePrivilegeMapping.get(Utils.CONTRACT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                    childComponents.removeIf(c -> ATTACH_COMPONENT.equals(c.getName()));
                    GroupComponentExt.of((IGroupComponent) x).setChildComponents(childComponents);
                }
            } catch (MetadataServiceException e) {
                log.error("getChildComponents error", e);
            }

        });
        return layout;
    }
}
