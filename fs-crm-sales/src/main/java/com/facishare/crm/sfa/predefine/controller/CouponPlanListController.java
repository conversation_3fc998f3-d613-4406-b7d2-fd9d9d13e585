package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

public class CouponPlanListController extends StandardListController {
    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> mobileLayouts = super.findMobileLayouts();
        if (CollectionUtils.empty(mobileLayouts)) {
            return mobileLayouts;
        }
        for (ILayout layout : mobileLayouts) {
            layout.set("buttons", Lists.newArrayList());
        }
        return mobileLayouts;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        CouponUtils.setDefaultCouponType(result,controllerContext.getTenantId());
        return result;
    }


}