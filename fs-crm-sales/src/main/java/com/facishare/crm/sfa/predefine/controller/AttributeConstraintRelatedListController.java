package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.AttributeConstaintConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.util.JsonUtil;

/**
 * @描述说明：查询数据时，加过滤条件
 * @作者：chench
 * @创建日期：2024-01-04
 */
public class AttributeConstraintRelatedListController extends StandardRelatedListController {
    @Override
    protected void before(Arg arg) {
        String searchQueryInfo = arg.getSearchQueryInfo();
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        if (CollectionUtils.empty(query.getFilters())
                || query.getFilters().stream().anyMatch(
                x -> !AttributeConstaintConstants.FIELD_ACTIVE_STATUS.equals(x.getFieldName()))
        ) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, AttributeConstaintConstants.FIELD_ACTIVE_STATUS, "true");
        }
        arg.setSearchQueryInfo(JsonUtil.toJson(query));
        super.before(arg);
    }
}
