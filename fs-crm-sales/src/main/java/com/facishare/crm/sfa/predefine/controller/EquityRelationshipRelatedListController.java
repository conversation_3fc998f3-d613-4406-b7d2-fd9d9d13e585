package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.EquityRelationshipConstants;
import com.facishare.crm.sfa.utilities.util.EquityRelationshipRelatedUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lik
 * @date : 2023/8/7 16:34
 */

public class EquityRelationshipRelatedListController extends SFARelatedListController{
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        if(SFAPreDefineObject.Account.getApiName().equals(arg.getTargetObjectApiName()) || SFAPreDefineObject.AccountMainData.getApiName().equals(arg.getTargetObjectApiName())){
            SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(getSearchQueryInfo());
            List<IFilter> filtersMust = new ArrayList<>();
            SearchUtil.fillFilterEq(filtersMust, EquityRelationshipConstants.Field.ACCOUNT_MAIN_DATA_ID, arg.getTargetObjectDataId());
            query.setFilters(filtersMust);
            // 如果是客户主数据中的组件，需要设置参数
            EquityRelationshipRelatedUtil.handleSectionOfFind(controllerContext.getUser(),query,arg);
            return query;
        }else{
            SearchTemplateQuery query = super.buildSearchTemplateQuery();
            return query;
        }
    }

    @Override
    protected Result after(StandardRelatedListController.Arg arg, Result result) {
        super.after(arg, result);
        return result;
    }
    @Override
    protected List<IButton> getButtons(ILayout layout) {
        if (RequestUtil.isMobileOrH5Request()) {
            return Lists.newArrayList();
        }
        return super.getButtons(layout);
    }
}
