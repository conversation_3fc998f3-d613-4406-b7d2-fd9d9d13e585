package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 可售范围移动端修改记录
 * <AUTHOR>
 */
public class AvailableRangeNewLogInfoListForMobController extends StandardNewLogInfoListForMobController {
    private static  UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext().getBean(UseRangeFieldDataRender.class);
    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        List<LogInfo.DiffObjectData> objectDataList = logRecord.getObjectData();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return logRecord;
        }

        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), SFAPreDefineObject.AvailableRange.getApiName());
        Map<String, IFieldDescribe> useRangeFieldMap = describe.getFieldDescribes().stream()
                .filter(k -> IFieldType.UseRange.equals(k.getType())).collect(Collectors.toMap(IFieldDescribe::getApiName, o -> o));

        useRangeFieldDataRender.userRangeLogInfoHandle(objectDataList, useRangeFieldMap);
        logRecord.setObjectData(objectDataList);
        return logRecord;
    }
}
