package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class AttributeImportViewController extends StandardImportViewController {

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if(StringUtils.equals(describe.getApiName(), Utils.ATTRIBUTE_OBJ_API_NAME)&&arg.getImportType()==IMPORT_TYPE_ADD){
            List<String> toRemoveFields = Lists.newArrayList("field_num");
            fieldDescribes.removeIf(f->toRemoveFields.contains(f.getApiName()));
        }
    }
}
