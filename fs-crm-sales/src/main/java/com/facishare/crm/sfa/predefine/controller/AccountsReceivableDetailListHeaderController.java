package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

import java.util.List;

public class AccountsReceivableDetailListHeaderController extends StandardListHeaderController {
    @Override
    public Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        result.setHasEditPermission(false);
        return result;
    }
    @Override
    protected List<String> getAuthorizedFields() {
        List<String> authorizedFields = super.getAuthorizedFields();
        authorizedFields.remove("periodic_source");
        return authorizedFields;
    }
}
