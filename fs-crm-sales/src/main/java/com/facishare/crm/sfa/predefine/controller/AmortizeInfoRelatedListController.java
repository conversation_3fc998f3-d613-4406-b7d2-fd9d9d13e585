package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.pricepolicy.PricePolicyConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;


public class AmortizeInfoRelatedListController extends StandardRelatedListController {
    private static final BizConfigThreadLocalCacheService configService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected BaseListController.Result after(StandardRelatedListController.Arg arg, Result result) {
        Result result1 = super.after(arg, result);
        // 补充多币种对应币种信息。
        User user = RequestContextManager.getContext().getUser();
        if (configService.isCurrencyEnabled(user.getTenantId()) && CollectionUtils.notEmpty(result.getDataList())) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(searchTemplateQuery);
            ext.addDeletedFilterIfNoDeletedFilter();
            ext.addFilter(Operator.EQ, "id", (String) result.getDataList().get(0).get(PricePolicyConstants.AmortizeInfoField.SALES_ORDER_ID));
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, (String) result.getDataList().get(0).get(PricePolicyConstants.AmortizeInfoField.ORDER_API_NAME), searchTemplateQuery, Lists.newArrayList("mc_currency"));
            if (CollectionUtils.notEmpty(queryResult.getData())) {
                String mtCurr = queryResult.getData().get(0).get("mc_currency", String.class);
                if (StringUtils.isNotEmpty(mtCurr)) {
                    result.getDataList().forEach(data -> data.put("mc_currency", mtCurr));
                }
            }
        }

        return result1;
    }

}
