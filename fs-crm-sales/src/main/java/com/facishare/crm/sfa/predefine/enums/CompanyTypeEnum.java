package com.facishare.crm.sfa.predefine.enums;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/4/24 16:54
 * @description:
 *
 */
public enum CompanyTypeEnum {

    INDIVIDUAL(0, "个体户"),
    COMPANY(1, "企业");

    private Integer value;
    private String label;

    CompanyTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static CompanyTypeEnum fromValue(Integer value) {
        for (CompanyTypeEnum companyTypeEnum : CompanyTypeEnum.values()) {
            if (companyTypeEnum.getValue().equals(value)) {
                return companyTypeEnum;
            }
        }
        return null;
    }
}
