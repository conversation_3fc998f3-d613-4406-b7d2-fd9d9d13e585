package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.BankStatementObjConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class BankStatementImportViewController extends StandardImportViewController {
    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        List<String> toRemoveFields = Lists.newArrayList(BankStatementObjConstants.Field.IsRelatedReceivedPayment.getApiName());
        fieldDescribes.removeIf(f -> toRemoveFields.contains(f.getApiName()));
    }
}
