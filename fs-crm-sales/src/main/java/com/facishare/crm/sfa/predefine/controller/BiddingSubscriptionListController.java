package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;

import java.util.List;

import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.*;

/**
 * BiddingSubscriptionRulesListController
 *
 * <AUTHOR>
 */
public class BiddingSubscriptionListController extends StandardListController {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        return super.doService(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<ObjectDataDocument> dataList = result.getDataList();
        for (ObjectDataDocument objectDataDocument : dataList) {
            objectDataDocument.put(BID_SUB_TYPE_TEXT, objectDataDocument.get(BID_SUB_TYPE));
            objectDataDocument.put(AREA_PROVINCE_TEXT, objectDataDocument.get(AREA_PROVINCE));
            objectDataDocument.put(CALLER_TYPE_TEXT, objectDataDocument.get(CALLER_TYPE));
            objectDataDocument.put(KEYWORDS_FILTER, objectDataDocument.get(KEYWORDS));
            objectDataDocument.put(BID_METHOD_TEXT, objectDataDocument.get(BID_METHOD));
        }
        return result;
    }
}