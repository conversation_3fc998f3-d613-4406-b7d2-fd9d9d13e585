package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 合同详情 class
 *
 * <AUTHOR>
 * @date 2019/2/26
 */
@Slf4j
public class ContractWebDetailController extends SFAWebDetailController {
    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (Objects.isNull(newResult.getLayout())) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        specialLogicForLayout(layout, newResult);
        return newResult;
    }

    private void specialLogicForLayout(ILayout layout, Result result){
        try {
            Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                    Lists.newArrayList(Utils.CONTRACT_API_NAME),
                    Lists.newArrayList(ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));
            List<IComponent> componentList = layout.getComponents();
            if (!componentList.isEmpty()) {
                Optional<IComponent> attachComponent = layout.getComponents().stream().filter(x -> ATTACH_COMPONENT.equals(x.getName())).findFirst();
                if (attachComponent.isPresent()) {
                    if (objApiNameAndActionCodePrivilegeMapping.get(Utils.CONTRACT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                        if (objApiNameAndActionCodePrivilegeMapping.get(Utils.CONTRACT_API_NAME).get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
                            List<IButton> buttons = getButtons();
                            WebDetailLayout.of(layout).addButtons(buttons, ATTACH_COMPONENT);
                        }
                    } else {
                        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList(ATTACH_COMPONENT));
                    }
                }
            }
        } catch (Exception e){
            log.warn("ContractWebDetailController->getLayout  error", e);
        }
    }

    private List<IButton> getButtons() {
        List<IButton> buttons = Lists.newArrayList();
        IButton addButton = new Button();
        addButton.setAction("Add");
        addButton.setActionType("default");
        addButton.setLabel(I18N.text(I18NKey.action_upload));
        addButton.setName("ContractAttObj_Add_button_default");
        buttons.add(addButton);
        IButton deleteButton = new Button();
        deleteButton.setAction("Delete");
        deleteButton.setActionType("default");
        deleteButton.setLabel(I18N.text(I18NKey.action_delete));
        deleteButton.setName("ContractAttObj_Delete_button_default");
        buttons.add(deleteButton);
        return buttons;
    }
}
