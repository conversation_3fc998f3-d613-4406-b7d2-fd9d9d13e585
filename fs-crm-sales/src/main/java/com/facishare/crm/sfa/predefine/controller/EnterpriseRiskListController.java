package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.EnterpriseRiskService;
import com.facishare.crm.sfa.predefine.service.MemberService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.Map;

public class EnterpriseRiskListController extends StandardListController {

    private static final EnterpriseRiskService enterpriseRiskService = SpringUtil.getContext().getBean(EnterpriseRiskService.class);
    private static final MemberService memberService = SpringUtil.getContext().getBean(MemberService.class);

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String tenantId = controllerContext.getTenantId();
        Map<String, Object> map = result.getExtendInfo();
        map.put("maxLicensePara", enterpriseRiskService.getMaxCount(tenantId));
        map.put("usingLicensePara", enterpriseRiskService.getUsingCount(tenantId));

        Map<String, String> describeNameMap = serviceFacade.findDisplayNameByApiNames(tenantId, Lists.newArrayList("AccountObj", "PartnerObj", "SupplierObj"));
        for (ObjectDataDocument dataDocument : result.getDataList()) {
            String descApiName = (String) dataDocument.get("refer_to_obj");
            dataDocument.put("refer_to_obj_name", describeNameMap.getOrDefault(descApiName, descApiName));
        }
        memberService.setMemberName(tenantId, result.getDataList(), "member_list", "member_list_name");
        result.setExtendInfo(map);
        return result;
    }

}
