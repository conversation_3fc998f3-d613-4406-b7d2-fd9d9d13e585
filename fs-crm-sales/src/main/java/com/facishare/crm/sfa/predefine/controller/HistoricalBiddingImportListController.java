package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.ManagerSkipPrivilegeCheckAction;
import com.facishare.crm.sfa.predefine.service.BiddingRuleService;
import com.facishare.crm.sfa.predefine.service.Procurement.ProcurementService;
import com.facishare.crm.sfa.predefine.service.SettingRuleMemberService;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementRule;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementRuleMemberModel;
import com.facishare.crm.util.CommonSqlUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.*;


public class HistoricalBiddingImportListController extends ManagerSkipPrivilegeCheckAction.ListController {

    private static final SettingRuleMemberService settingRuleMemberService = SpringUtil.getContext().getBean(SettingRuleMemberService.class);

    private static final BiddingRuleService BIDDING_RULE_SERVICE = SpringUtil.getContext().getBean(BiddingRuleService.class);

    private static final ProcurementService procurementService = SpringUtil.getContext().getBean(ProcurementService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }


    @Override
    protected Result doService(Arg arg) {
        return super.doService(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        List<String> disabledRuleIds = new ArrayList<>();
        List<String> runningRuleIds = new ArrayList<>();
        List<String> finishedRuleIds = new ArrayList<>();
        for (IObjectData data : objectDataList) {
            List<ProcurementRuleMemberModel> ruleMember = settingRuleMemberService.getSettingRuleMember(controllerContext.getTenantId(),
                    controllerContext.getUser().getUpstreamOwnerIdOrUserId(), data.getId(), SFAPreDefineObject.HistoricalBiddingImport.getApiName());
            Map<String, Object> conditions = BIDDING_RULE_SERVICE.getProcurementRuleConditions(controllerContext.getTenantId(), data.get(ProcurementRule.CONDITIONS));
            data.set(BID_SUB_TYPE_TEXT, conditions.get(BID_SUB_TYPE));
            data.set(AREA_PROVINCE_TEXT, conditions.get(AREA_PROVINCE));
            data.set(CALLER_TYPE_TEXT, conditions.get(CALLER_TYPE));
            data.set(BID_METHOD_TEXT, conditions.get(BID_METHOD));
            if (conditions.get(AREA_CITY) != null) {
                List cityLabels = procurementService.getAreaCityLabelByCode(CommonSqlUtils.convert2ActionContext(controllerContext), new HashSet<>((ArrayList) conditions.get(AREA_CITY)));
                data.set(AREA_CITY_TEXT, cityLabels);
            }
            data.set(BIDING_END_DATE_TIME, conditions.get(BIDING_END_DATE_TIME));
            data.set(TENDER_END_DATE_TIME, conditions.get(TENDER_END_DATE_TIME));
            data.set("member_list", ruleMember);
            if (!(boolean) data.get(ProcurementRule.IS_ENABLED)) {
                disabledRuleIds.add(data.getId());
            }
            if (TaskStatus.RUNNING.getStatus().equals(data.get(TASK_STATUS)) || TaskStatus.SUSPEND.getStatus().equals(data.get(TASK_STATUS))) {
                runningRuleIds.add(data.getId());
            }
            if (TaskStatus.FINISHED.getStatus().equals(data.get(TASK_STATUS))) {
                finishedRuleIds.add(data.getId());
            }
            List<String> names = procurementService.getObjectDataNames(controllerContext.getTenantId(), data);
            if (CollectionUtils.notEmpty(names)) {
                data.set(ENTERPRISE_NAME, Joiner.on(",").join(names));
            }
        }

        Map<String, List<String>> buttonsMap = result.getButtonInfo().getButtonMap();
        if (CollectionUtils.notEmpty(buttonsMap)) {
            buttonsMap.entrySet().forEach(entry -> {
                if (!disabledRuleIds.contains(entry.getKey())) {
                    entry.getValue().removeIf(x -> ObjectAction.DELETE.getButtonApiName().equals(x) || ObjectAction.UPDATE.getButtonApiName().equals(x));
                }
                if (finishedRuleIds.contains(entry.getKey())) {
                    entry.getValue().removeIf(x -> ObjectAction.UPDATE.getButtonApiName().equals(x));
                }
                if (CollectionUtils.notEmpty(runningRuleIds) && runningRuleIds.contains(entry.getKey())) {
                    entry.getValue().removeIf(x -> ObjectAction.CHANGE_STATUS.getButtonApiName().equals(x));
                }
            });
        }
        return result;
    }

}