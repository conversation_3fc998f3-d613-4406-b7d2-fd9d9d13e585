package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.InvisibleFieldUtil;
import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardSearchListController;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

public class AccountSearchListController extends StandardSearchListController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        AccountUtil.handleRemainingTime(objectDataList);
        handleMemberInvisibleFields(objectDataList);
        result.setDataList(ObjectDataDocument.ofList(objectDataList));
        return result;
    }

    //处理公海普通成员不可见字段
    private void handleMemberInvisibleFields(List<IObjectData> objectDataList) {
        if(CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<String> highSeasIds = AccountUtil.getPoolIds(objectDataList);
        if(CollectionUtils.empty(highSeasIds)) {
            return;
        }
        Map<String, ObjectPoolPermission.ObjectPoolPermissions> poolPermissionsMap = SearchListUtil.getPoolPermission(controllerContext.getUser(), controllerContext.getObjectApiName(), highSeasIds);
        if(CollectionUtils.empty(poolPermissionsMap)) {
            return;
        }
        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
        for(String highSeasId : highSeasIds) {
            List<IObjectData> tempDataList = objectDataList.stream().filter(x -> highSeasId.equals(AccountUtil.getPoolId(x)) && !AccountUtil.hasOwner(x)).collect(Collectors.toList());
            if(CollectionUtils.empty(tempDataList)) {
                continue;
            }
            ObjectPoolPermission.ObjectPoolPermissions poolPermissions = poolPermissionsMap.get(highSeasId);
            if (Objects.equals(poolPermissions, ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER)) {
                InvisibleFieldUtil.handleMemberInvisibleFields(controllerContext.getUser(), tempDataList,
                        "18", highSeasId, clientInfo);
            }
        }
    }
}
