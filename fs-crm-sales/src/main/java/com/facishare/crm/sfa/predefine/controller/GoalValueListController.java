package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.enums.GoalEnum;
import com.facishare.crm.sfa.predefine.service.GoalValue.utilities.*;
import com.facishare.crm.sfa.predefine.service.GoalValue.service.GoalRuleCommonService;
import com.facishare.crm.sfa.predefine.service.GoalValue.service.GoalValueCommonService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.QueryMemberInfosByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhaopx on 2018/4/4.
 */
@SuppressWarnings("Duplicates")
public class GoalValueListController extends StandardListController {

    private Set<String> deptIds = new HashSet<>();
    private Set<String> empIds = new HashSet<>();
    private String tenantId;
    private String userId;
    private String checkObjectId;
    private String goalType;
    private List<IObjectData> accountInfos = new ArrayList<>();
    private List<IObjectData> productInfos = new ArrayList<>();
    private List<IObjectData> partnerInfos = new ArrayList<>();
    private List<IObjectData> objectInfos = new ArrayList<>();

    private boolean isGoalRuleManager;
    private boolean isCrmManager;
    private String name = "";
    private String goalRuleId;
    private boolean isCheckForLevel;

    private final GoalValueCommonService goalValueCommonService = (GoalValueCommonService) SpringUtil.getContext().getBean("goalValueCommonService");

    private final GoalRuleCommonService goalRuleCommonService = (GoalRuleCommonService) SpringUtil.getContext().getBean("goalRuleCommonService");

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        query.setFindExplicitTotalNum(null);
        tenantId = controllerContext.getUser().getTenantId();
        userId = controllerContext.getUser().getUserId();
        List<String> userRoleList = serviceFacade.getUserRole(controllerContext.getUser());
        isGoalRuleManager = !CollectionUtils.empty(userRoleList) && userRoleList.contains(GoalRoleConstants.GOAL_MANAGER_ROLE_CODE);
        isCrmManager = serviceFacade.isAdmin(controllerContext.getUser());
        List<IFilter> filters = query.getFilters();

        if (CollectionUtils.empty(filters)) {
            return query;
        }

        //        removeEmptyFilter(filters, GoalRuleObj.GOAL_RULE_DETAIL_ID);
        resetDetailFilter(filters);
        goalRuleId = getFieldValue(filters, GoalValueConstants.GOAL_RULE_ID, false);
        String goalRuleDetailId = getFieldValue(filters, GoalValueConstants.GOAL_RULE_DETAIL_ID, true);

        IObjectData goalRuleData = goalRuleCommonService.findGoalRule(controllerContext.getUser(), goalRuleId);
        String checkLevelFieldApiName = goalRuleData.get(GoalRuleObj.CHECK_LEVEL_FIELD_API_NAME, String.class);
        String checkLevelType = goalRuleData.get(GoalRuleObj.CHECK_LEVEL_TYPE, String.class);
        isCheckForLevel = GoalRuleConstants.OPTIONAL_LEVEL_TYPE.equals(checkLevelType) || GoalRuleConstants.PROVINCE_CITY_LEVEL_TYPE.equals(checkLevelType);
        List<String> subCodeList = Lists.newArrayList();
        goalType = getFieldValue(filters, GoalValueConstants.GOAL_TYPE, false);
        String fiscalYear = getFieldValue(filters, GoalValueConstants.FISCAL_YEAR, true);
        if (GoalEnum.GoalTypeValue.ACCOUNT.getValue().equals(goalType) || GoalEnum.GoalTypeValue.PRODUCT.getValue().equals(goalType) || GoalEnum.GoalTypeValue.PartnerObj.getValue().equals(goalType)) {
            name = getFieldValue(filters, GoalValueConstants.NAME, true);
            removeFilter(filters, GoalValueConstants.NAME);
        }
        if (goalType.equals(GoalEnum.GoalTypeValue.CIRCLE.getValue()) || goalType.equals(GoalEnum.GoalTypeValue.EMPLOYEE.getValue())) {
            checkObjectId = getFieldValue(filters, GoalValueConstants.CHECK_OBJECT_ID, false);
        } else if (isCheckForLevel) {
            checkObjectId = getFieldValue(filters, GoalValueConstants.CHECK_OBJECT_ID, true);
        }
        //        id = getFieldValue(filters, GoalValueConstants.ID_, true);
        List<Wheres> wheres = Lists.newArrayList();
        if (goalType.equals(GoalEnum.GoalTypeValue.CIRCLE.getValue())) {
            List<String> checkCircleIds = goalRuleCommonService.findGoalRuleApplyCircle(controllerContext.getUser(), goalRuleId).stream().map(data -> String.valueOf(
                    data.get(GoalRuleApplyCircleObj.FIELD_APPLY_CIRCLE_ID))).collect(Collectors.toList());
            //获取直属子部门和直属人员
            deptIds.addAll(serviceFacade.getSubDeptByDeptId(tenantId, userId, checkObjectId, false));
            deptIds.add(checkObjectId);
            deptIds.retainAll(checkCircleIds);
            empIds.addAll(getMemberByDeptId(checkObjectId));
            //拼装适配直属部门的wheres
            wheres.add(buildWheres(GoalEnum.GoalTypeValue.CIRCLE.getValue(), new ArrayList<>(deptIds)));
            if (CollectionUtils.notEmpty(empIds)) {
                //拼装适配直属员工的wheres
                wheres.add(buildWheres(GoalEnum.GoalTypeValue.EMPLOYEE.getValue(), new ArrayList<>(empIds)));
            }
            handleQueryFilters(query, filters, wheres);
        } else if (goalType.equals(GoalEnum.GoalTypeValue.ACCOUNT.getValue())) {
            if (isCheckForLevel) {
                if (CollectionUtils.notEmpty(subCodeList)) {
                    wheres.add(buildWheres(GoalEnum.GoalTypeValue.ACCOUNT.getValue(), new ArrayList<>(subCodeList)));
                }
            } else if ((isCrmManager || isGoalRuleManager) && StringUtils.isNotEmpty(name)) {
                accountInfos = goalValueCommonService.getAccountsByNameWithoutAuth(tenantId, name);
                List<String> accountIds = accountInfos.stream().map(accountData -> accountData.get("id", String.class)).collect(Collectors.toList());
                wheres.add(buildWheres(GoalEnum.GoalTypeValue.ACCOUNT.getValue(), new ArrayList<>(accountIds)));
            } else if (!isCrmManager && !isGoalRuleManager) {
                List<String> accountIds = getCheckObjIdListByAuth(goalRuleId, goalRuleDetailId, fiscalYear, name, GoalEnum.GoalTypeValue.ACCOUNT.getValue(), GoalValueConstants.ACCOUNT_SEARCH_ALL_TEMPLATE_ID);
                wheres.add(buildWheres(GoalEnum.GoalTypeValue.ACCOUNT.getValue(), new ArrayList<>(accountIds)));
            }
            handleQueryFilters(query, filters, wheres);
        } else if (goalType.equals(GoalEnum.GoalTypeValue.PRODUCT.getValue())) {
            if (isCheckForLevel) {
                if (CollectionUtils.notEmpty(subCodeList)) {
                    wheres.add(buildWheres(GoalEnum.GoalTypeValue.PRODUCT.getValue(), new ArrayList<>(subCodeList)));
                }
            } else if ((isCrmManager || isGoalRuleManager) && StringUtils.isNotEmpty(name)) {
                objectInfos = goalValueCommonService.getProductsByNameWithoutAuth(tenantId, name);
                List<String> productIds = objectInfos.stream().map(productData -> productData.get("id", String.class)).collect(Collectors.toList());
                wheres.add(buildWheres(GoalEnum.GoalTypeValue.PRODUCT.getValue(), new ArrayList<>(productIds)));
            } else if (!isCrmManager && !isGoalRuleManager) {
                List<String> productInfoIdList = getCheckObjIdListByAuth(goalRuleId, goalRuleDetailId, fiscalYear, name, GoalEnum.GoalTypeValue.PRODUCT.getValue(), GoalValueConstants.PRODUCT_SEARCH_ALL_TEMPLATE_ID);
                wheres.add(buildWheres(GoalEnum.GoalTypeValue.PRODUCT.getValue(), new ArrayList<>(productInfoIdList)));
            }

            handleQueryFilters(query, filters, wheres);
        } else if (goalType.equals(GoalEnum.GoalTypeValue.PartnerObj.getValue())) {
            if (isCheckForLevel) {
                if (CollectionUtils.notEmpty(subCodeList)) {
                    wheres.add(buildWheres(GoalEnum.GoalTypeValue.PartnerObj.getValue(), new ArrayList<>(subCodeList)));
                }
            } else if ((isCrmManager || isGoalRuleManager) && StringUtils.isNotEmpty(name)) {
                partnerInfos = goalValueCommonService.getPartnersByNameWithoutAuth(tenantId, name);
                List<String> partnerIds = partnerInfos.stream().map(partnerData -> partnerData.get("id", String.class)).collect(Collectors.toList());
                wheres.add(buildWheres(GoalEnum.GoalTypeValue.PartnerObj.getValue(), new ArrayList<>(partnerIds)));
            } else if (!isCrmManager && !isGoalRuleManager) {
                List<String> partnerIds = getCheckObjIdListByAuth(goalRuleId, goalRuleDetailId, fiscalYear, name, GoalEnum.GoalTypeValue.PartnerObj.getValue(), GoalValueConstants.Partner_SEARCH_ALL_TEMPLATE_ID);
                wheres.add(buildWheres(GoalEnum.GoalTypeValue.PartnerObj.getValue(), new ArrayList<>(partnerIds)));
            }

            handleQueryFilters(query, filters, wheres);
        } else {
            empIds.add(checkObjectId);
        }

        //不走数据权限
        query.setPermissionType(0);
        return query;
    }

    private List<String> getCheckObjIdListByAuth(String goalRuleId,
                                                 String goalRuleDetailId,
                                                 String fiscalYear,
                                                 String name,
                                                 String crmObjName,
                                                 String searchAllTemplateId) {
        List<IObjectData> goalValues = goalValueCommonService.findAllGoalValuesWithSql(controllerContext.getUser(),
                goalRuleId,
                goalRuleDetailId,
                fiscalYear);
        List<String> checkObjectIds = goalValues
                .stream()
                .map(data -> data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class))
                .collect(Collectors.toList());
        objectInfos = goalValueCommonService.getObjectsByIdsAndName(controllerContext.getUser(), checkObjectIds, name, crmObjName, searchAllTemplateId);
        switch (crmObjName) {
            case GoalRuleObj.ACCOUNT_API_NAME:
                accountInfos = objectInfos;
                break;
            case GoalRuleObj.PRODUCT_API_NAME:
                productInfos = objectInfos;
                break;
            case GoalRuleObj.PARTNER_API_NAME:
                partnerInfos = objectInfos;
                break;
        }

        return objectInfos.stream().map(o -> String.valueOf(o.getId())).collect(Collectors.toList());

    }

    private void handleQueryFilters(SearchTemplateQuery query, List<IFilter> filters, List<Wheres> wheres) {
        query.setWheres(wheres);
        removeFilter(filters, GoalValueConstants.GOAL_TYPE);
        removeFilter(filters, GoalValueConstants.CHECK_OBJECT_ID);
        removeFilter(filters, GoalValueConstants.ID_);

        query.getOrders().clear();
        List<OrderBy> orders = Lists.newArrayList();
        orders.add(new OrderBy(GoalValueConstants.LAST_MODIFIED_TIME, false));
        orders.add(new OrderBy(GoalValueConstants.GOAL_TYPE, true));
        orders.add(new OrderBy(GoalValueConstants.CHECK_OBJECT_ID, true));
        query.setOrders(orders);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);

        if (isCheckForLevel) {
            return handResultByLevel(rst);
        }

        if (GoalEnum.GoalTypeValue.ACCOUNT.getValue().equals(goalType)) {
            return handleResultByAccount(rst);
        }
        if (GoalEnum.GoalTypeValue.PRODUCT.getValue().equals(goalType)) {
            return handleResultByProduct(rst);
        }
        if (GoalEnum.GoalTypeValue.PartnerObj.getValue().equals(goalType)) {
            return handleResultByPartner(rst);
        }
        //填充人员或部门名称
        List<ObjectDataDocument> dataList = rst.getDataList();
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoList = serviceFacade.getDeptInfoNameByIds(tenantId, userId, new ArrayList<>(deptIds));
        List<UserInfo> edpInfoList = serviceFacade.getUserNameByIds(tenantId, userId, new ArrayList<>(empIds));
        int i = 1;
        for (ObjectDataDocument d : dataList) {
            ObjectData data = new ObjectData(d);
            //排序权重,父部门+1000;子部门+2000;员工+3000
            Integer order;
            String dataId = data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class);
            String dataName;
            if (data.get(GoalValueConstants.GOAL_TYPE, String.class).equals(GoalEnum.GoalTypeValue.CIRCLE.getValue())) {
                Optional<QueryDeptInfoByDeptIds.DeptInfo> dept = deptInfoList.stream().filter(x -> x.getDeptId().equals(dataId)).findFirst();
                dataName = dept.map(QueryDeptInfoByDeptIds.DeptInfo::getDeptName).orElse("");
                order = dept.map(QueryDeptInfoByDeptIds.DeptInfo::getParentId).orElse("").equals(checkObjectId) ? 2000 : 1000;

            } else {
                Optional<UserInfo> user = edpInfoList.stream().filter(x -> x.getId().equals(dataId)).findFirst();
                dataName = user.map(UserInfo::getName).orElse("");
                boolean isEnableUser = user.map(UserInfo::getStatus).orElse(0) == 0 ? true : false;
                dataName = isEnableUser ? dataName : dataName + "（已停用）";// ignoreI18n
                order = 3000;
            }
            data.set("check_object_id__r", dataName);
            //            data.set("row_num", (Integer) data.get("row_num") + order);
            data.set("row_num", i + order);
            i++;
        }
        dataList.sort(GoalValueListController::compareTo);
        return rst;
    }

    private Result handResultByLevel(Result result) {
        List<ObjectDataDocument> dataList = result.getDataList();
        int parentIndex = 0;
        int index = 0;
        for (ObjectDataDocument d : dataList) {
            ObjectData data = new ObjectData(d);
            String dataId = data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class);
            if (StringUtils.isNotEmpty(checkObjectId) && Objects.equals(dataId, checkObjectId)) {
                parentIndex = index;
            }
            index++;
        }
        if (StringUtils.isNotEmpty(checkObjectId) && parentIndex > 0) {
            Collections.swap(dataList, parentIndex, 0);
        }

        return result;
    }

    private Result handleResultByAccount(Result result) {
        List<ObjectDataDocument> dataList = result.getDataList();
        List<String> accountIds = dataList
                .stream()
                .map(data -> new ObjectData(data).get(GoalValueConstants.CHECK_OBJECT_ID, String.class))
                .collect(Collectors.toList());
        if ((isCrmManager || isGoalRuleManager) && StringUtils.isEmpty(name)) {
            accountInfos = goalValueCommonService.getAccountsByIdWithoutAuth(tenantId, accountIds);
        }
        for (ObjectDataDocument d : dataList) {
            ObjectData data = new ObjectData(d);
            String dataId = data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class);
            String dataName;
            boolean isDeleted = false;
            Optional<IObjectData> account = accountInfos.stream().filter(x -> x.get("accountIdKey").equals(dataId)).findFirst();
            dataName = account.map(IObjectData::getName).orElse("");
            isDeleted = account.isPresent() ? account.get().isDeleted() : false;
            data.set("check_object_id__r", dataName);
            data.set("is_deleted_for_check_obj", isDeleted);
        }
        result.setDataList(dataList);
        return result;
    }

    private Result handleResultByProduct(Result result) {
        List<ObjectDataDocument> dataList = result.getDataList();
        List<String> productIds = dataList
                .stream()
                .map(data -> new ObjectData(data).get(GoalValueConstants.CHECK_OBJECT_ID, String.class))
                .collect(Collectors.toList());
        if ((isCrmManager || isGoalRuleManager) && StringUtils.isEmpty(name)) {
            productInfos = goalValueCommonService.getProductsByIdWithoutAuth(tenantId, productIds);
        }
        for (ObjectDataDocument d : dataList) {
            ObjectData data = new ObjectData(d);
            String dataId = data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class);
            String dataName;
            boolean isDeleted = false;
            Optional<IObjectData> product = productInfos.stream().filter(x -> x.get("productIdKey").equals(dataId)).findFirst();
            dataName = product.map(IObjectData::getName).orElse("");
            isDeleted = product.isPresent() ? product.get().isDeleted() : false;
            data.set("check_object_id__r", dataName);
            data.set("is_deleted_for_check_obj", isDeleted);
        }
        result.setDataList(dataList);
        return result;
    }

    private Result handleResultByPartner(Result result) {
        List<ObjectDataDocument> dataList = result.getDataList();
        List<String> partnerIds = dataList
                .stream()
                .map(data -> new ObjectData(data).get(GoalValueConstants.CHECK_OBJECT_ID, String.class))
                .collect(Collectors.toList());
        if ((isCrmManager || isGoalRuleManager) && StringUtils.isEmpty(name)) {
            partnerInfos = goalValueCommonService.getPartnersByIdWithoutAuth(tenantId, partnerIds);
        }
        for (ObjectDataDocument d : dataList) {
            ObjectData data = new ObjectData(d);
            String dataId = data.get(GoalValueConstants.CHECK_OBJECT_ID, String.class);
            String dataName;
            boolean isDeleted = false;
            Optional<IObjectData> partner = partnerInfos.stream().filter(x -> x.get("partnerIdKey").equals(dataId)).findFirst();
            dataName = partner.map(IObjectData::getName).orElse("");
            isDeleted = partner.isPresent() ? partner.get().isDeleted() : false;
            data.set("check_object_id__r", dataName);
            data.set("is_deleted_for_check_obj", isDeleted);
        }
        result.setDataList(dataList);
        return result;
    }

    private static int compareTo(ObjectDataDocument o1, ObjectDataDocument o2) {
        Integer i1 = Integer.parseInt(o1.get("row_num").toString());
        Integer i2 = Integer.parseInt(o2.get("row_num").toString());
        if (i1 <= i2) {
            if (Objects.equals(i1, i2)) {
                return 0;
            } else {
                return -1;
            }
        } else {
            return 1;
        }
    }

    private List<String> getMemberByDeptId(String deptId) {
        if (Objects.equals(deptId, "999999")) {
            return Lists.newArrayList();
        }
        Map<String, List<QueryMemberInfosByDeptIds.Member>> memberMap = serviceFacade.getMemberInfoMapByDeptIds(controllerContext.getUser(),
                Lists.newArrayList(deptId),
                Boolean.FALSE,
                null,
                1);
        if (CollectionUtils.empty(memberMap)) {
            return Lists.newArrayList();
        }
        List<QueryMemberInfosByDeptIds.Member> memberList = memberMap.get(deptId);
        if (CollectionUtils.empty(memberList)) {
            return Lists.newArrayList();
        }
        return memberList.stream().map(QueryMemberInfosByDeptIds.Member::getId).distinct().collect(Collectors.toList());
    }

    private Wheres buildWheres(String goalType, List<String> ids) {
        Wheres wheres = new Wheres();
        wheres.setConnector(Where.CONN.OR.toString());
        List<IFilter> filters = Lists.newArrayList();
        filters.add(buildFilter(GoalValueConstants.GOAL_TYPE, Operator.EQ, Lists.newArrayList(goalType)));
        filters.add(buildFilter(GoalValueConstants.CHECK_OBJECT_ID, Operator.IN, ids));
        wheres.setFilters(filters);
        return wheres;
    }

    private Filter buildFilter(String fieldName, Operator operator, List<String> fieldValues) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }

    private void removeFilter(List<IFilter> filters, String fieldName) {
        Iterator<IFilter> iter = filters.iterator();
        while (iter.hasNext()) {
            IFilter item = iter.next();
            if (item.getFieldName().equals(fieldName)) {
                iter.remove();
                break;
            }
        }
    }

    private void resetDetailFilter(List<IFilter> filters) {
        if (CollectionUtils.empty(filters)) {
            return;
        }
        boolean hasDetailFilter = false;
        for (IFilter filter : filters) {
            if (GoalRuleObj.GOAL_RULE_DETAIL_ID.equals(filter.getFieldName())) {
                if (CollectionUtils.empty(filter.getFieldValues())) {
                    filter.setOperator(Operator.IS);
                    filter.setFieldValues(Lists.newArrayList());
                }
                hasDetailFilter = true;
                break;
            }
        }
        if (!hasDetailFilter) {
            IFilter filter = new Filter();
            filter.setFieldName(GoalRuleObj.GOAL_RULE_DETAIL_ID);
            filter.setFieldValues(Lists.newArrayList());
            filter.setOperator(Operator.IS);
            filters.add(filter);
        }
    }

    private String getFieldValue(List<IFilter> filters, String fieldName, Boolean isAllowEmptyValue) {
        String rst = "";
        List<IFilter> tmpFilter = filters.stream().filter(t -> fieldName.equals(t.getFieldName())).collect(Collectors.toList());
        if (CollectionUtils.empty(tmpFilter)) {
            if (isAllowEmptyValue) {
                return "";
            } else {
                throw new MetaDataBusinessException(String.format("%s 类型不合法", fieldName));// ignoreI18n
            }
        }
        List<String> fieldValues = tmpFilter.stream().findFirst().map(IFilter::getFieldValues).orElse(Lists.newArrayList());
        if (CollectionUtils.empty(fieldValues) && !isAllowEmptyValue) {
            throw new MetaDataBusinessException(String.format("%s 类型不合法", fieldName));// ignoreI18n
        }
        for (String fieldValue : fieldValues) {
            if (Strings.isNullOrEmpty(fieldValue) && !isAllowEmptyValue) {
                throw new MetaDataBusinessException(String.format("%s 类型不合法", fieldName));// ignoreI18n
            }else{
                return fieldValue;
            }
            /*rst = fieldValue;
            break;*/
        }
        return rst;
    }

}
