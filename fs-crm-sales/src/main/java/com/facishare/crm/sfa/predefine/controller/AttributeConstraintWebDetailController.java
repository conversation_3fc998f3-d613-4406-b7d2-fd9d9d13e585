package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.quoter.AttributeConstraintService;
import com.facishare.crm.sfa.predefine.service.quoter.model.AttributeConstraintModel;
import com.facishare.crm.sfa.utilities.constant.AttributeConstaintConstants;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2023-12-19
 */
public class AttributeConstraintWebDetailController  extends SFAWebDetailController {
    private final AttributeConstraintService attributeConstraintService = SpringUtil.getContext().getBean(AttributeConstraintService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        List<IObjectData> detailDatas = attributeConstraintService.getAttributeConstraintLinesByAttributeConstraintId(controllerContext.getUser(), arg.getObjectDataId());
        List<AttributeConstraintModel.AttributeConstraintNode> nodes = attributeConstraintService.list2MultiTree(detailDatas);
        if(CollectionUtils.isNotEmpty(nodes) && newResult.getData() != null) {
            String attributeJson = JsonUtil.toJson(nodes);
            result.getData().put(AttributeConstaintConstants.FIELD_ATTRIBUTE_JSON, attributeJson);
        }
        filterButtons(newResult.getLayout().toLayout());
        //移动端过滤字段
        filterFieldByMobile(newResult.getLayout().toLayout());
        return newResult;
    }

    private void filterFieldByMobile(ILayout layout) {
        if(RequestUtil.isMobileOrH5Request()||RequestUtil.isMobileDeviceRequest()) {
            LayoutExt layoutExt = LayoutExt.of(layout);
            Optional<IComponent> component = layoutExt.getComponentByApiName("form_component");
            if(component.isPresent())  {
                List sections = component.get().get("field_section", List.class, Lists.newArrayList());
                for (int i = 0; i < sections.size(); i++) {
                    Map section = (Map) sections.get(i);
                    if("constraint_rule_field_section__c".equals((String)section.get("api_name"))) {
                        sections.remove(i);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 如果是来自移动端或H5  过滤掉  "新建、编辑、复制"，统一过滤 "打印"
     * @param layout
     */
    private void filterButtons(ILayout layout) {
        WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList("Print"));
        if (RequestUtil.isMobileOrH5Request()||RequestUtil.isMobileDeviceRequest()) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList("Clone", "Edit"));
        }
    }
}
