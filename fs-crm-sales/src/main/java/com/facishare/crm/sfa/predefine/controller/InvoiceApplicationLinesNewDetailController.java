package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.google.common.collect.Sets;

import java.util.Set;

public class InvoiceApplicationLinesNewDetailController extends StandardNewDetailController {

    @Override
    protected Set<String> getUnauthorizedFields() {
        Set<String> unauthorizedFields = super.getUnauthorizedFields();
        // 详情页屏蔽待开票金额字段

        if (CollectionUtils.empty(unauthorizedFields)) {
            return Sets.newHashSet("no_invoice_amount");
        } else {
            unauthorizedFields.add("no_invoice_amount");
            return unauthorizedFields;
        }
    }
}
