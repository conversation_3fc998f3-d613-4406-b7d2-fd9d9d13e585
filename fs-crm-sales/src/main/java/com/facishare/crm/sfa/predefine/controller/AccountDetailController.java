package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.InfraServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 客户对象详情页
 * <p>
 * Created by liyiguang on 2017/7/12.
 */
@Slf4j
public class AccountDetailController extends SFADetailController {
    private final static MetaDataService metaDataService = SpringUtil.getContext().getBean(MetaDataServiceImpl.class);
	private static final InfraServiceFacade INFRA_SERVICE_FACADE = SpringUtil.getContext().getBean(InfraServiceFacadeImpl.class);
    private static final String ACCOUNT_HIERARCHY_COMPONENT = "account_hierarchy_component";

    private void specialLogicForLayout(ILayout layout, Result result) {
        try {
            //将客户的相关对象放在第二位
            List<IComponent> componentList = layout.getComponents();
            if (!componentList.isEmpty()) {
                Optional<IComponent> component = componentList.stream().filter(s -> "relatedObject".equals(s.getName())).findFirst();
                if (component.isPresent()) {
                    componentList.remove(component.get());
                    componentList.add(componentList.size() > 0 ? 1 : 0, component.get());
                }
                Optional<IComponent> componentAccountAddr = componentList.stream().filter(s -> s.getName().equals(ACCOUNT_ADDR_MD_GROUP_COMPONENT)).findFirst();
                if (componentAccountAddr.isPresent() && result.getData() != null) {
                    AccountAddrUtil.getButtons(controllerContext.getUser(), Utils.ACCOUNT_ADDR_API_NAME, componentAccountAddr.get(), result.getData());
                }
                Optional<IComponent> componentAccountFinInfo = componentList.stream().filter(s -> s.getName().equals(ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT)).findFirst();
                if (componentAccountFinInfo.isPresent()) {
                    AccountAddrUtil.getButtons(controllerContext.getUser(), Utils.ACCOUNT_FIN_INFO_API_NAME, componentAccountFinInfo.get(), result.getData());
                }
                layout.setComponents(componentList);
            }
        } catch (MetadataServiceException ex) {
            log.warn("AccountDetailController->ChangeComponentOrder  error", ex);
        }

        //移动端移除ViewFeedCard按钮
        if (RequestUtil.isMobileRequest()) {
            RemoveButtons(layout, Lists.newArrayList(ObjectAction.VIEW_FEED_CARD.getActionCode()));
            AccountUtil.removeRFMResultCard(layout);
            //移动端屏蔽人脉关系雷达卡片
            LayoutUtils.removeContactMemberRelationshipCard(layout);
        } else if (RequestUtil.isWebRequest()) {
            RemoveButtons(layout, Lists.newArrayList(ObjectAction.JOINROUT.getActionCode()));
        }

        //开启多币种，移除摘要卡片
        AccountUtil.removeAccountSummaryCard(controllerContext.getUser(), layout);
        AccountUtil.processAccountCostCard(controllerContext.getUser(), layout);

        LeadsUtils.handleLeadsRelatedComponents(layout, "LeadsObj_account_id_related_list");
        LeadsUtils.handleLeadsTransferLogRelatedComponents(layout, "LeadsTransferLogObj_account_id_related_list");
    }

    private void RemoveButtons(ILayout layout, List<String> objectActions) {
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(x -> objectActions.contains(x.getAction()));
        layout.setButtons(buttons);
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
        if (headInfoComponentOp.isPresent()) {
            IComponent headInfoComponent = headInfoComponentOp.get();
            List<IButton> headInfoButtons = headInfoComponent.getButtons();
            headInfoButtons.removeIf(x -> objectActions.contains(x.getAction()));
            WebDetailLayout.of(layout).setButtons(headInfoButtons, "head_info");
        }
    }

    private static final String ACCOUNT_ADDR_MD_GROUP_COMPONENT = Utils.ACCOUNT_ADDR_API_NAME + "_md_group_component";
    private static final String ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT = Utils.ACCOUNT_FIN_INFO_API_NAME + "_md_group_component";

    @Override
    protected Result after(Arg arg, Result result) {
        stopWatch.lap("account detail controller after-1");
        Result newResult = super.after(arg, result);
        IObjectData objectData = newResult.getData().toObjectData();
        //先对describe判空
        if (newResult.getDescribe() != null) {
            //拷贝对象描述
            IObjectDescribe copyDescribe = ObjectDescribeExt.of(result.getDescribe()).copyOnWrite();
            AccountUtil.handleRemainingTimeDesc(copyDescribe, Lists.newArrayList(objectData));;
            AccountUtil.calculateCompletionRate(copyDescribe, Lists.newArrayList(objectData));
            newResult.setDescribe(ObjectDescribeDocument.of(copyDescribe));
        }
   //     AccountUtil.handleRemainingTimeDesc(describe, Lists.newArrayList(objectData));
        AccountUtil.handleIsRemindRecycling(Lists.newArrayList(objectData));
      //  AccountUtil.calculateCompletionRate(describe, Lists.newArrayList(objectData));
        stopWatch.lap("account detail controller after-2");
        boolean isShowCompanyLyricalAll = AccountUtil.isShowCompanyLyricalAll(controllerContext.getTenantId(), objectData.getName());
        objectData.set("isShowCompanyLyricalAll", isShowCompanyLyricalAll);
        stopWatch.lap("account detail controller after-3");

        newResult.setData(ObjectDataDocument.of(objectData));
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        specialLogicForLayout(layout, newResult);

        String owner = CommonBizUtils.getOwner(objectData);
        if (StringUtils.isEmpty(owner)) {
            handleRelatedListByHighSeaSetting(layout);
        }

        GetVersion.VersionInfo versionInfo = serviceFacade.getVersionInfo(controllerContext.getTenantId());
        if (versionInfo != null) {
            LayoutUtils.handleRelatedCasesObj(versionInfo.getCurrentVersion(), "CasesObj_account_id_related_list", layout);
        }
        stopWatch.lap("account detail controller after-4");
        return newResult;
    }

    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    @Override
    protected ILayout getLayout() {
        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(Utils.ACCOUNT_API_NAME),
                Lists.newArrayList(ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

        ILayout layout = super.getLayout();
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> childComponents = x.getChildComponents();
                if (!objApiNameAndActionCodePrivilegeMapping.get(Utils.ACCOUNT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                    childComponents.removeIf(c -> ATTACH_COMPONENT.equals(c.getName()));
                    x.setChildComponents(childComponents);
                } else {
                    Optional<IComponent> attachComponent = childComponents.stream().filter(c -> ATTACH_COMPONENT.equals(c.getName())).findFirst();
                    if (attachComponent.isPresent()) {
                        if (objApiNameAndActionCodePrivilegeMapping.get(Utils.ACCOUNT_API_NAME).get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
                            List<IButton> buttons = getButtons();
                            attachComponent.get().setButtons(buttons);
                        }
                    }
                }
                if (RequestUtil.isH5Request()) {
                    childComponents = x.getChildComponents();
                    childComponents.removeIf(c -> ACCOUNT_HIERARCHY_COMPONENT.equals(c.getName()));
                    x.setChildComponents(childComponents);
                }
            } catch (MetadataServiceException e) {
                log.error("getChildComponents error", e);
            }

        });

        return layout;
    }

    private List<IButton> getButtons() {
        Map<String, Map<String, Permissions>> privilege = metaDataService.checkDataPrivilege(
                controllerContext.getUser(), Lists.newArrayList(data), ObjectDescribeExt.of(describe),
                Lists.newArrayList(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

        List<IButton> buttons = Lists.newArrayList();
        if (privilege.containsKey(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
            Map<String, Permissions> permissions = privilege.get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode());
            if (permissions.containsKey(arg.getObjectDataId())
                    && permissions.get(arg.getObjectDataId()).equals(Permissions.READ_WRITE)) {
                IButton addButton = new Button();
                addButton.setAction("Add");
                addButton.setActionType("default");
                addButton.setLabel(I18N.text(I18NKey.action_upload));
                addButton.setName("AccountAttObj_Add_button_default");
                buttons.add(addButton);
                IButton deleteButton = new Button();
                deleteButton.setAction("Delete");
                deleteButton.setActionType("default");
                deleteButton.setLabel(I18N.text(I18NKey.action_delete));
                deleteButton.setName("AccountAttObj_Delete_button_default");
                buttons.add(deleteButton);
            }
        }
        return buttons;
    }

    @Override
    protected boolean defaultEnableQixinGroup() {
        IObjectCluster cluster = INFRA_SERVICE_FACADE.find(controllerContext.getUser(), arg.getObjectDescribeApiName());
        if (Objects.isNull(cluster) || QixinGroupConfig.of(cluster).isObjectEnabled()) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}
