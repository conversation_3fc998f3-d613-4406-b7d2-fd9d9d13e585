package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

public class AccountMainDataDescribeLayoutController extends SFADescribeLayoutController {
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        switch (arg.getLayout_type()) {
            case LAYOUT_TYPE_EDIT:
                Optional<IFieldSection> fieldSection = formComponent.getFieldSections().stream()
                        .filter(it -> "base_field_section__c".equals(it.getName()))
                        .findFirst();
                if (fieldSection.isPresent()) {
                    List<IFormField> fieldsList = fieldSection.get().getFields();
                    if(CollectionUtils.isEmpty(fieldsList)) {
                        fieldsList = Lists.newArrayList();
                    }
                    IFormField formField = new FormField();
                    formField.setFieldName("data_own_organization");
                    formField.setRenderType(IFieldType.DEPARTMENT);
                    formField.setRequired(true);
                    formField.setReadOnly(false);
                    fieldsList.add(fieldsList.size(), formField);

//                    formField = new FormField();
//                    formField.setFieldName("data_own_department");
//                    formField.setRenderType(IFieldType.DEPARTMENT);
//                    formField.setRequired(true);
//                    formField.setReadOnly(false);
//                    fieldsList.add(fieldsList.size(), formField);

                    fieldSection.get().setFields(fieldsList);
                }
                break;
            default:
                break;
        }
    }
}
