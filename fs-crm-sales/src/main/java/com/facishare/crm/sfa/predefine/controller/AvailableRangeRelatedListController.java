package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.SaleContractConstants;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/6/29
 */
public class AvailableRangeRelatedListController extends StandardRelatedListController {

    private final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    protected String accountId = "";

    @Override
    protected void init() {
        super.init();
        if (arg.getObjectData() != null && arg.getObjectData().containsKey(SaleContractConstants.SaleContractField.ACCOUNTID.getApiName())) {
            accountId = Optional.ofNullable(arg.getObjectData().get(SaleContractConstants.SaleContractField.ACCOUNTID.getApiName())).orElse("").toString();
        }
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery query = super.customSearchTemplate(searchQuery);
        availableRangeUtils.buildSearchQueryForAvailableRange(query, arg.getRelatedListName(), accountId);
        return query;
    }
}
