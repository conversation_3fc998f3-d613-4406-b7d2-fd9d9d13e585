package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.model.BizQueryManageConstant;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/3/8 17:41
 * @description:
 */
public class BizQueryManageListController extends StandardListController {


    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }


    @Override
    protected Result after(Arg arg, Result result) {

        List<String> disabledRuleIds = new ArrayList<>();
        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        for (IObjectData data : objectDataList) {
            // 非启用状态都为禁用（包括为空的）
            if (!"1".equals(data.get(BizQueryManageConstant.BIZ_STATUS))) {
                disabledRuleIds.add(data.getId());
            }
        }
        Map<String, List<String>> buttonsMap = result.getButtonInfo().getButtonMap();
        if (CollectionUtils.notEmpty(buttonsMap)) {
            buttonsMap.forEach((key, value) -> {
                // 启用状态的数据移除编辑和删除按钮
                if (!disabledRuleIds.contains(key)) {
                    value.removeIf(x -> ObjectAction.DELETE.getButtonApiName().equals(x));
                }
            });
        }
        return super.after(arg, result);
    }
}
