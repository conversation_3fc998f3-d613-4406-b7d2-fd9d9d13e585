package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.InfraServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.metadata.GroupComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.QixinGroupConfig;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGroupComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Slf4j
public class AccountNewDetailController extends SFANewDetailController {
    private static final String ACCOUNT_HIERARCHY_COMPONENT = "account_hierarchy_component";
	private static final InfraServiceFacade INFRA_SERVICE_FACADE = SpringUtil.getContext().getBean(InfraServiceFacadeImpl.class);

    private void specialLogicForLayout(ILayout layout, StandardDetailController.Result result) {
        //移动端移除ViewFeedCard按钮
        if (RequestUtil.isMobileRequest()) {
            RemoveButtons(layout, Lists.newArrayList(ObjectAction.VIEW_FEED_CARD.getActionCode()));
            AccountUtil.removeRFMResultCard(layout);
        } else if (RequestUtil.isWebRequest()) {
            RemoveButtons(layout, Lists.newArrayList(ObjectAction.JOINROUT.getActionCode()));
        }

        //开启多币种，移除摘要卡片
        AccountUtil.removeAccountSummaryCard(controllerContext.getUser(), layout);
        AccountUtil.processAccountCostCard(controllerContext.getUser(), layout);

        LeadsUtils.handleLeadsRelatedComponentsForNewDetail(layout, "LeadsObj_account_id_related_list");
        LeadsUtils.handleLeadsTransferLogRelatedComponentsForNewDetail(layout, "LeadsTransferLogObj_account_id_related_list");
    }

    private void RemoveButtons(ILayout layout, List<String> objectActions) {
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(x -> objectActions.contains(x.getAction()));
        layout.setButtons(buttons);
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
        if (headInfoComponentOp.isPresent()) {
            IComponent headInfoComponent = headInfoComponentOp.get();
            List<IButton> headInfoButtons = headInfoComponent.getButtons();
            headInfoButtons.removeIf(x -> objectActions.contains(x.getAction()));
            WebDetailLayout.of(layout).setButtons(headInfoButtons, "head_info");
        }
    }

    @Override
    protected StandardDetailController.Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        this.stopWatch.lap("detail.after start");
        StandardDetailController.Result newResult = super.after(arg, result);
        this.stopWatch.lap("detail.after end");
        IObjectData objectData = newResult.getData().toObjectData();
        if (newResult.getDescribe() != null) {
            //拷贝对象描述
            IObjectDescribe copyDescribe = ObjectDescribeExt.of(result.getDescribe()).copyOnWrite();
            AccountUtil.handleRemainingTimeDesc(copyDescribe, Lists.newArrayList(objectData));;
            this.stopWatch.lap("handleRemainingTimeDesc start");
            AccountUtil.calculateCompletionRate(copyDescribe, Lists.newArrayList(objectData));
            this.stopWatch.lap("calculateCompletionRate start");
            newResult.setDescribe(ObjectDescribeDocument.of(copyDescribe));
        }
//        AccountUtil.handleRemainingTimeDesc(describe, Lists.newArrayList(objectData));
//        this.stopWatch.lap("handleRemainingTimeDesc start");
        AccountUtil.handleIsRemindRecycling(Lists.newArrayList(objectData));
        this.stopWatch.lap("handleIsRemindRecycling start");
//        AccountUtil.calculateCompletionRate(describe, Lists.newArrayList(objectData));
//        this.stopWatch.lap("calculateCompletionRate start");
        boolean isShowCompanyLyricalAll = AccountUtil.isShowCompanyLyricalAll(controllerContext.getTenantId(), objectData.getName());
        this.stopWatch.lap("isShowCompanyLyricalAll start");
        objectData.set("isShowCompanyLyricalAll", isShowCompanyLyricalAll);
        newResult.setData(ObjectDataDocument.of(objectData));
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        specialLogicForLayout(layout, newResult);
        this.stopWatch.lap("specialLogicForLayout start");
        String owner = CommonBizUtils.getOwner(objectData);
        if (StringUtils.isEmpty(owner)) {
            handleRelatedListByHighSeaSetting(layout);
        }

        GetVersion.VersionInfo versionInfo = serviceFacade.getVersionInfo(controllerContext.getTenantId());
        this.stopWatch.lap("getVersionInfo start");
        if (versionInfo != null) {
            LayoutUtils.handleRelatedCasesObj(versionInfo.getCurrentVersion(), "CasesObj_account_id_related_list", layout);
            this.stopWatch.lap("handleRelatedCasesObj start");
        }

        LayoutDocument sortedLayout = this.getSortedLayout(layout, SFAPreDefineObject.Account.getApiName());
        this.stopWatch.lap("getSortedLayout start");
        if (sortedLayout != null) {
            newResult.setLayout(sortedLayout);
        }

        return newResult;
    }

    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    @Override
    protected ILayout getLayout() {
        Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(Utils.ACCOUNT_API_NAME),
                Lists.newArrayList(ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

        ILayout layout = super.getLayout();
        LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(x -> {
            try {
                List<IComponent> childComponents = GroupComponentExt.of((IGroupComponent) x).getChildComponents();
                if (!objApiNameAndActionCodePrivilegeMapping.get(Utils.ACCOUNT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                    childComponents.removeIf(c -> ATTACH_COMPONENT.equals(c.getName()));
                }
                GroupComponentExt.of((IGroupComponent) x).setChildComponents(childComponents);
                if (RequestUtil.isH5Request()) {
                    childComponents = GroupComponentExt.of((IGroupComponent) x).getChildComponents();
                    childComponents.removeIf(c -> ACCOUNT_HIERARCHY_COMPONENT.equals(c.getName()));
                    GroupComponentExt.of((IGroupComponent) x).setChildComponents(childComponents);
                }
            } catch (MetadataServiceException e) {
                log.error("getChildComponents error", e);
            }
        });
        return layout;
    }

    /**
     * 根据公海的设置动态隐藏相关列表对象及隐藏字段
     */
    protected void handleRelatedListByHighSeaSetting(ILayout layout) {
        //移除外部企业字段
        SFADetailController.CheckNeedShowRelatedObjsResult checkNeedShowRelatedObjsResult = checkNeedShowRelatedObjs();
        if (!checkNeedShowRelatedObjsResult.isAllowMemberRelation()) {
            List<String> removeComponentNameList = Lists.newArrayList("operation_log", "account_hierarchy_component", "contact_relation_component");
            List<String> removeComponentTypeList = Lists.newArrayList("relatedlist", "multi_table");
            if (checkNeedShowRelatedObjsResult.isAllowMemberViewFeed()) {
                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m -> {
                    IGroupComponent groupComponent = (IGroupComponent) m;
                    try {
                        List<IComponent> ch_comps = groupComponent.getChildComponents();
                        ch_comps.removeIf(g -> !"sale_log".equals(g.getName()) &&
                                (removeComponentTypeList.contains(g.getType()) || removeComponentNameList.contains(g.getName())));
                        groupComponent.setChildComponents(ch_comps);
                    } catch (MetadataServiceException e) {
                        log.error("getComponents error", e);
                    }
                });
            } else {
                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m -> {
                    IGroupComponent groupComponent = (IGroupComponent) m;
                    try {
                        List<IComponent> ch_comps = groupComponent.getChildComponents();
                        ch_comps.removeIf(g -> "sale_log".equals(g.getName()) ||
                                removeComponentTypeList.contains(g.getType()) || removeComponentNameList.contains(g.getName()));
                        groupComponent.setChildComponents(ch_comps);
                    } catch (MetadataServiceException e) {
                        log.error("getComponents error", e);
                    }
                });
            }
            LayoutExt.of(layout).getComponentByApiName("top_component").ifPresent(m -> {
                IGroupComponent groupComponent = (IGroupComponent) m;
                try {
                    List<IComponent> ch_comps = groupComponent.getChildComponents();
                    ch_comps.removeIf(g -> "relevant_team_component".equals(g.getName()));
                    groupComponent.setChildComponents(ch_comps);
                } catch (MetadataServiceException e) {
                    log.error("getComponents error", e);
                }
            });
        }
        if (!checkNeedShowRelatedObjsResult.isAllowMemberViewFeed() && RequestUtil.isMobileRequest()) {
            if (!checkNeedShowRelatedObjsResult.isAllowMemberViewFeed()) {
                LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m -> {
                    IGroupComponent groupComponent = (IGroupComponent) m;
                    try {
                        List<IComponent> ch_comps = groupComponent.getChildComponents();
                        ch_comps.removeIf(g -> "sale_log".equals(g.getName()));
                        groupComponent.setChildComponents(ch_comps);
                    } catch (MetadataServiceException e) {
                        log.error("getComponents error", e);
                    }
                });
            }
        }
        if (!checkNeedShowRelatedObjsResult.isAllowMemberSendFeed()) {
            List<IButton> buttons = layout.getButtons();
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.ADD_EVENT.getActionCode()));
            layout.setButtons(buttons);
            LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m -> {
                IGroupComponent groupComponent = (IGroupComponent) m;
                try {
                    List<IComponent> ch_comps = groupComponent.getChildComponents();
                    Optional<IComponent> saleLogComponent = ch_comps.stream().filter(x -> "sale_log".equals(x.getName())).findFirst();
                    if (saleLogComponent.isPresent()) {
                        IComponent tempComponent = saleLogComponent.get();
                        List<IButton> tempButtons = tempComponent.getButtons();
                        tempButtons.removeIf(x -> x.getAction().equals(ObjectAction.ADD_EVENT.getActionCode()));
                        tempComponent.setButtons(tempButtons);
                    }
                } catch (MetadataServiceException e) {
                    log.error("getComponents error", e);
                }
            });
        }

        //隐藏详细信息和顶部信息中的字段
        if (!CollectionUtils.isEmpty(checkNeedShowRelatedObjsResult.getNeedHideFields())) {
            removeDetailInfoFields(layout, checkNeedShowRelatedObjsResult.getNeedHideFields());
            LayoutExt.of(layout).getComponentByApiName("top_component").ifPresent(m -> {
                try {
                    IGroupComponent groupComponent = (IGroupComponent) m;
                    List<IComponent> ch_comps = groupComponent.getChildComponents();
                    Optional<IComponent> topInfo = ch_comps.stream().filter(x -> x.get("api_name", String.class).equals("top_info")).findFirst();
                    if (topInfo.isPresent()) {
                        PreDefLayoutUtil.removeSomeFields((FormComponent) topInfo.get(), new HashSet<>(checkNeedShowRelatedObjsResult.getNeedHideFields()));
                    }
                    groupComponent.setChildComponents(ch_comps);
                } catch (MetadataServiceException e) {
                    log.error(e.getMessage(),e);
                }
            });
        }
    }

    @Override
    protected boolean defaultEnableQixinGroup() {
        IObjectCluster cluster = INFRA_SERVICE_FACADE.find(controllerContext.getUser(), arg.getObjectDescribeApiName());
        if (Objects.isNull(cluster) || QixinGroupConfig.of(cluster).isObjectEnabled()) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}
