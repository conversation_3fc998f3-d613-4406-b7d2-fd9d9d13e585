package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.attributepricebook.AttributePriceBookServiceImpl;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * 属性价目表明细列表
 *
 * <AUTHOR>
 */
public class AttributePriceBookLinesDetailListController extends StandardDetailListController {

    AttributePriceBookServiceImpl attributePriceBookService = SpringUtil.getContext().getBean(AttributePriceBookServiceImpl.class);

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> dataQueryResult = super.findData(query);
        attributePriceBookService.assembleObjectDataDocument(controllerContext.getUser(), dataQueryResult.getData());
        return dataQueryResult;
    }
}
