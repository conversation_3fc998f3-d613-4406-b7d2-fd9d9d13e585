package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.DhtUtil;
import com.facishare.crm.sfa.utilities.util.FunctionUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @描述说明：Bom版本选择扩展逻辑
 * @作者：chench
 * @创建日期：2024-01-15
 */
public class BomCoreRelatedListController extends StandardRelatedListController {

    private final QuoterCommonController quoterCommonController = SpringUtil.getContext().getBean(QuoterCommonController.class);
    private final FunctionUtils functionUtils = SpringUtil.getContext().getBean(FunctionUtils.class);

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        //支持apl函数对查询条件做扩展，只要设置此属性，不关心属性值
        if(Objects.nonNull(arg.getExtraData()) && arg.getExtraData().containsKey("biz_function")) {
            query = functionUtils.listFilterByFunction(controllerContext.getUser(), Utils.BOM_CORE_API_NAME, query);
        }
        return  query;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        //是否灰度了报价器功能
        boolean canUseQuoter = Boolean.TRUE.equals(GrayUtil.isGrayEnable(controllerContext.getTenantId(), GrayUtil.QUOTER_OPEN_TENANT_ID));
        //是否灰度了报价器功能
        if(canUseQuoter && quoterCommonController.isFromQuoter(arg.getExtraData())) {
             filterNotMatchBom(arg, rst);
        }
        return rst;
    }

    /**
     * 过滤掉不符合要求的版本信息
     * @param arg
     * @param rst
     */
    private void filterNotMatchBom(Arg arg, Result rst) {
        //如果没有数据或数据只有一条，则不用做多版本过滤处理
        if(CollectionUtils.isEmpty(rst.getDataList()) || rst.getDataList().size() == 1) {
            return;
        }
        List<String> bomCoreIds = Lists.newArrayList();
        bomCoreIds.addAll(rst.getDataList().stream().map(data -> data.toObjectData().get(IObjectData.ID, String.class)).collect(Collectors.toList()));
        List<String> realBomCoreIds = quoterCommonController.findBomListByBomCoreIdsAndAttr(controllerContext.getUser(), arg.getExtraData(), bomCoreIds);
        //过滤后的BomCoreIds
        if(CollectionUtils.isNotEmpty(realBomCoreIds)) {
            List<ObjectDataDocument> realResult = rst.getDataList().stream().filter(data -> realBomCoreIds.contains(data.toObjectData().get(IObjectData.ID, String.class))).collect(Collectors.toList());
            rst.setDataList(realResult);
        }
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        SearchTemplateQuery searchTemplateQuery = super.customSearchTemplate(searchQuery);
        if (DhtUtil.isDhtOrAccessoriesMallRequest(controllerContext.getPeerName(), controllerContext.getAppId())) {
            searchTemplateQuery.setPermissionType(0);
            searchTemplateQuery.setDataRightsParameter(null);
        }
        return searchTemplateQuery;
    }

    @Override
    protected void doFunPrivilegeCheck() {
        return;
    }
}
