package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

public class AttributePriceBookWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() != null) {
            specialLogicForLayout(newResult.getLayout().toLayout());
        }
        return newResult;
    }

    private void specialLogicForLayout(ILayout layout) {
        if (RequestUtil.isMobileOrH5Request()) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(
                ObjectAction.CREATE.getActionCode(),
                ObjectAction.UPDATE.getActionCode(),
                ObjectAction.DELETE.getActionCode(),
                ObjectAction.CLONE.getActionCode(),
                ObjectAction.INVALID.getActionCode())
            );
        }
        //根据配置隐藏关联对象列表的新建按钮
        PreDefLayoutUtil.invisibleNewWebRefObjectListAddButton(arg.getObjectDescribeApiName(), layout);
    }
}
