package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2020/4/17 4:28 下午
 * @illustration
 */
public class BomDescribeLayoutController extends StandardDescribeLayoutController {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        LayoutDocument layout = newResult.getLayout();
        if (Objects.nonNull(layout)) {
            LayoutExt layoutExt = LayoutExt.of(layout.toLayout());
            removeFieldFromLayout(layoutExt);
        }
        dealButton(newResult);
        return newResult;
    }

    private void dealButton(Result result) {
        ObjectDescribeDocument objectDescribe = result.getObjectDescribe();
        if (Objects.nonNull(objectDescribe)) {
            List<String> actionCodeList = Lists.newArrayList(
                    ObjectAction.DELETE.getActionCode(),
                    ObjectAction.CLONE.getActionCode(),
                    ObjectAction.UPDATE.getActionCode(),
                    ObjectAction.BATCH_EXPORT.getActionCode(),
                    ObjectAction.CREATE_GROUP.getActionCode(),
                    ObjectAction.SYNC_PRODUCT_STRUCTURE.getActionCode(),
                    ObjectAction.SYNC_STRUCTURE_TO_OTHER.getActionCode(),
                    ObjectAction.SET_GROUP.getActionCode(),
                    ObjectAction.DEL_GROUP.getActionCode(),
                    ObjectAction.CREATE.getActionCode());
            Map<String, Boolean> butMap = serviceFacade.funPrivilegeCheck(controllerContext.getUser(),
                    Utils.BOM_API_NAME, actionCodeList);
            actionCodeList.removeIf(x -> !butMap.get(x));
            objectDescribe.put("funButton", actionCodeList);
        }
    }

    /**
     * 针对 价目表改造的逻辑做特殊处理
     * 价目表改造全网后去掉此逻辑
     */
    private void removeFieldFromLayout(LayoutExt layoutExt) {
        layoutExt.getFormComponent().ifPresent(x -> {
            FormComponent formComponent = (FormComponent) x.getFormComponent();
            PreDefLayoutUtil.removeSomeFields(formComponent, BomConstants.DESCRIBE_FILTER_LAYOUT_FIELD);
        });
    }
}
