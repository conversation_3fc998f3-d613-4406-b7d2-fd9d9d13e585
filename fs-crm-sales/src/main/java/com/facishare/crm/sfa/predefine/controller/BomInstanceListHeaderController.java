package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.SfaListHeaderUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.Objects;

public class BomInstanceListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        if (Objects.isNull(res.getLayout())) {
            return res;
        }
        ILayout layout = new Layout(res.getLayout());
        SfaListHeaderUtil.specialLogicForLayout(layout,SfaListHeaderUtil.BOM_INSTANCE_WHITE_LIST_BUTTONS);
        SfaListHeaderUtil.specialButtons(res.getButtons(),SfaListHeaderUtil.BOM_INSTANCE_WHITE_LIST_BUTTONS);
        return res;
    }
}
