package com.facishare.crm.sfa.predefine.enums;

/**
 * <AUTHOR>
 * Created on 2020/11/23.
 */
public enum ReceivedPaymentStatusEnum {

    NoClaimed("1", "待认领"),
    Claimed("2", "已认领"),
    ;

    private final String value;
    private final String label;

    ReceivedPaymentStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

}
