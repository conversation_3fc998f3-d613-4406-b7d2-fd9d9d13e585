package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.utilities.constant.PartnerConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * Created by quzf  合作伙伴联系人业务类型的特殊过滤
 */
@Slf4j
public class ContactDetailController extends ContactDetailBaseController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getData() != null) {
            ContactUtil.concatenateBirthDay(Lists.newArrayList(result.getData().toObjectData()));
        }
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        //1、如果是合作伙伴业务类型，布局中隐藏客户字段、外部来源、外部负责人字段,同时相关对象列表中移除预制的关联联系人的页签
        //2、如果不是合作伙伴业务类型，布局中隐藏合作伙伴字段
        if (PartnerConstants.RECORD_TYPE_CONTACT_PARTNER.equals(this.data.getRecordType())) {
            removeDetailInfoFields(layout, Lists.newArrayList(PartnerConstants.ACCOUNT_ID,
                    PartnerConstants.OUT_RESOURCES, "enable_partner_view"));
            doSupportPartnerRelateTab(layout);
        } else {
            removeDetailInfoFields(layout, Lists.newArrayList(PartnerConstants.OWNED_PARTNER_ID));
            removeLayoutTopInfoField(layout, Lists.newArrayList(PartnerConstants.OWNED_PARTNER_ID));
        }

        //合作伙伴联系人  移除 是否合作伙伴可见 按钮
        modifyLayoutButtonAddPartnerViewBtn(newResult.getLayout().toLayout(), data);

        //A版企业干掉工单对象
        GetVersion.VersionInfo versionInfo = serviceFacade.getVersionInfo(controllerContext.getTenantId());
        if (versionInfo != null) {
            LayoutUtils.handleRelatedCasesObj(versionInfo.getCurrentVersion(), "CasesObj_contact_id_related_list", layout);
        }
        try {
            //修改相关中地址管理的展示字段，走默认列表布局
            List<IComponent> componentList = layout.getComponents();
            if (CollectionUtils.notEmpty(componentList)) {
                Optional<IComponent> component = componentList.stream().filter(s -> "relatedObject".equals(s.getName())).findFirst();
                if (component.isPresent()) {
                    AccountAddrUtil.changeIncludeFields(controllerContext.getUser(), component.get());
                }
            }
        } catch (MetadataServiceException ex) {
            log.warn("ContactDetailController->ChangeComponentOrder  error", ex);
        }
        LeadsUtils.handleLeadsRelatedComponents(layout, "LeadsObj_contact_id_related_list");
        LeadsUtils.handleLeadsTransferLogRelatedComponents(layout, "LeadsTransferLogObj_contact_id_related_list");
        if(newResult.getData().get("mobile__s")!=null){//手机字段不可见，同时隐藏手机1-5
            ContactUtil.deleteLayout(newResult.getLayout(),"mobile");
            ContactUtil.hideTelAndMobile(Lists.newArrayList(newResult.getData()),"mobile");
        }
        if(newResult.getData().get("tel__s")!=null){//电话字段不可见，同时隐藏电话1-5
            ContactUtil.deleteLayout(newResult.getLayout(),"tel");
            ContactUtil.hideTelAndMobile(Lists.newArrayList(newResult.getData()),"tel");
        }
        if (RequestUtil.isMobileRequest()) {
            AccountUtil.removeRFMResultCard(layout);
            //移动端屏蔽人脉关系雷达卡片
            LayoutUtils.removeContactMemberRelationshipCard(layout);
        }
        return newResult;
    }


    protected void doSupportPartnerRelateTab(ILayout layout) {
        LayoutExt.of(layout).getRelatedComponent().ifPresent(relatedComponent -> {
            try {
                List<IComponent> childComponents = relatedComponent.getChildComponents();
                childComponents.removeIf(childComponent -> {
                    String relatedListName = childComponent.get("related_list_name", String.class);
                    return StringUtils.isNotBlank(relatedListName) && !relatedListName.endsWith("__c");
                });
                relatedComponent.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.warn("ContactDetailController getChildComponents error", e);
            }
        });
    }

    public void modifyLayoutButtonAddPartnerViewBtn(ILayout layout, IObjectData objectData) {
        if (objectData == null || layout == null) {
            return;
        }
        if (ObjectDataExt.of(objectData).isLock()) {
            return;
        }
        if (!Strings.isNullOrEmpty(objectData.getRecordType()) && objectData.get("record_type", String.class).equals(PartnerConstants.RECORD_TYPE_CONTACT_PARTNER)) {
            List<IButton> buttons = layout.getButtons();
            if (CollectionUtils.notEmpty(buttons)) {
                buttons.removeIf(f -> "enable_partner_view__c".equals(f.getAction()));
                layout.setButtons(buttons);
            }
        }

    }

}
