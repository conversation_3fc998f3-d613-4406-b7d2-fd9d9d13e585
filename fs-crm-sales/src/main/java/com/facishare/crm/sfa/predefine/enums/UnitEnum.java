package com.facishare.crm.sfa.predefine.enums;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public enum UnitEnum {
    SPU(Utils.SPU_API_NAME, "unit"),
    Product(Utils.PRODUCT_API_NAME, "unit"),
    SalesOrderProduct(Utils.SALES_ORDER_PRODUCT_API_NAME, "actual_unit"),
    CommonUnit(SFAPreDefineObject.CommonUnit.getApiName(), "common_unit"),
    QuoteLines(Utils.QUOTE_LINES_API_NAME, "actual_unit"),
    SaleContractLine(Utils.SALE_CONTRACT_LINE_API_NAME, "actual_unit"),
    MultiUnitRelated(Utils.MULTI_UNIT_RELATED_API_NAME, "unit_id"),
    DeliveryNoteProduct(Utils.DELIVERY_NOTE_PRODUCT_API_NAME, "actual_unit"),
    GoodsReceivedNoteProduct(Utils.GOODS_RECEIVED_NOTE_PRODUCT_API_NAME, "actual_unit"),
    OutboundDeliveryNoteProduct(Utils.OUTBOUND_DELIVER_NOTE_PRODUCT_API_NAME, "actual_unit"),
    PurchaseOrderProduct(Utils.PURCHASE_ORDER_PRODUCT_API_NAME, "actual_unit"),
    Stock(Utils.STOCK_API_NAME, "auxiliary_unit"),
    RequisitionNoteProduct(Utils.REQUISTITION_NOTE_PRODUCT_API_NAME, "actual_unit"),
    StockCheckNoteProduct(Utils.STOCK_CHECK_NOTE_PRODUCT_API_NAME, "actual_unit"),
    ExchangeReturnNoteProduct("ExchangeReturnNoteProductObj", "actual_unit"),
    PriceBookProduct("PriceBookProductObj", "actual_unit"),
    DealerReturnProduct("DealerReturnProductObj","actual_unit"),
    PurchaseReturnNoteProduct("PurchaseReturnNoteProductObj","actual_unit"),
    ReturnedGoodsInvoiceProduct("ReturnedGoodsInvoiceProductObj","actual_unit");

    UnitEnum(String describeApiName, String unitFieldName) {
        this.describeApiName = describeApiName;
        this.unitFieldName = unitFieldName;
    }

    private final String describeApiName;
    private final String unitFieldName;
    private static Set<String> grayDisable;
    ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    ObjectDescribeServiceImpl objectDescribeService = SpringUtil.getContext().getBean(ObjectDescribeServiceImpl .class);

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-java-console", config -> {
            grayDisable = Sets.newHashSet(config.get("grayDisable", "").split(","));
        });


    }

    private static final Map<String, UnitEnum> map;

    static {
        map = Arrays.stream(values()).collect(Collectors.toMap(UnitEnum::getDescribeApiName, o -> o));
    }

    public String getDescribeApiName() {
        return describeApiName;
    }

    public String getUnitFieldName() {
        return unitFieldName;
    }

    public static List<String> describeApiNames() {
        return Arrays.stream(values()).filter(o -> !grayDisable.contains(o.getDescribeApiName())).map(UnitEnum::getDescribeApiName).collect(Collectors.toList());
    }

    public static UnitEnum of(String describeApiName) {
        return map.get(describeApiName);
    }

    public void synchronize(String tenantId,IObjectDescribe iObjectDescribe, List<ISelectOption> newSelectOptions,boolean isForceRefresh) {
        SelectOneFieldDescribe unitField = (SelectOneFieldDescribe) iObjectDescribe.getFieldDescribe(this.getUnitFieldName());
        if (Objects.isNull(unitField)) return;
        unitField.setSelectOptions(newSelectOptions);
        if (isForceRefresh) {
            try {
                objectDescribeService.updateOrInsertFieldsForOnline(tenantId, iObjectDescribe.getApiName(), Lists.newArrayList(unitField));
                if (iObjectDescribe.getApiName().equals(Utils.SALES_ORDER_PRODUCT_API_NAME) && getUnitFieldName().equals("actual_unit")) {
                    unitField = (SelectOneFieldDescribe) iObjectDescribe.getFieldDescribe("other_unit");
                    if (Objects.isNull(unitField)) return;
                    unitField.setSelectOptions(newSelectOptions);
                    objectDescribeService.updateOrInsertFieldsForOnline(tenantId, iObjectDescribe.getApiName(), Lists.newArrayList(unitField));
                }
            } catch (MetadataServiceException e) {
                log.error("refresh unit error %s: ex:%s", iObjectDescribe.getApiName(), e);
            }
        }else{
            if (Utils.SPU_API_NAME.equals(iObjectDescribe.getApiName()) || Utils.PRODUCT_API_NAME.equals(iObjectDescribe.getApiName())) {
                serviceFacade.update(iObjectDescribe);
            } else {
                serviceFacade.updateFieldDescribe(iObjectDescribe, Lists.newArrayList(unitField));
            }
            if (iObjectDescribe.getApiName().equals(Utils.SALES_ORDER_PRODUCT_API_NAME)&&getUnitFieldName().equals("actual_unit")) {
                unitField = (SelectOneFieldDescribe) iObjectDescribe.getFieldDescribe("other_unit");
                if (Objects.isNull(unitField)) return;
                unitField.setSelectOptions(newSelectOptions);
                serviceFacade.updateFieldDescribe(iObjectDescribe, Lists.newArrayList(unitField));
            }
        }
    }

}
