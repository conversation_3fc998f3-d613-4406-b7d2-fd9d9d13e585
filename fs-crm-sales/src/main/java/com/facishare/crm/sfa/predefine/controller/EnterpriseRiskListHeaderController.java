package com.facishare.crm.sfa.predefine.controller;

import com.google.common.collect.Lists;

import java.util.List;

public class EnterpriseRiskListHeaderController extends BaseSFAListHeaderController {

    @Override
    public List<String> getAuthorizedFields() {
        return Lists.newArrayList("name", "refer_to_obj_name", "member_list_name", "status", "created_by", "create_time", "last_modified_by", "last_modified_time");
    }

}
