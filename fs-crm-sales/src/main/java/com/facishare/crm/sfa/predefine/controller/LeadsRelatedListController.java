package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.transfer.constant.TransferConstant;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISpecifiedTableParameter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.SpecifiedTableParameter;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.constant.PartnerConstants.TARGET_RELATED_LIST_NAME;

/**
 * Created by renlb on 2018/11/28.
 */
public class LeadsRelatedListController extends SFARelatedListController {
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final BizConfigThreadLocalCacheService configCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private static final MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        ObjectDataDocument document = arg.getObjectData();
        List<IFilter> filters = query.getFilters();
        if (document != null && document.size() > 0) {
            String accountId = document.getOrDefault("associate_account_id", "").toString();
            if (!Strings.isNullOrEmpty(accountId)) {
                List<String> intersectionLeadsIds = getAccountAssociatedLeadsIdList(accountId);
                if (!CollectionUtils.isEmpty(intersectionLeadsIds)) {
                    SearchUtil.fillFilterIn(filters, DBRecord.ID, intersectionLeadsIds);
                } else {
                    SearchUtil.fillFilterIn(filters, DBRecord.ID, Lists.newArrayList("null"));
                }
            }
        }
        if (!arg.isIncludeAssociated()) {
            ISpecifiedTableParameter specifiedTableParameter = getSpecifiedTableParameter(arg.getTargetObjectApiName());
            query.setSpecifiedTableParameter(specifiedTableParameter);
        }
        return query;
    }

    @Override
    protected Query defineQuery() {
        Query query = super.defineQuery();
        List<IFilter> filters = query.getFilters();
        // addSourceLeadsFilter(controllerContext.getUser(), filters, arg.getRelatedListName());
        return query;
    }

    /**
     * 合作伙伴下源线索字段查线索相关列表
     * 增加条件：过滤掉已被其他合作伙伴选过的线索
     *
     * @param user
     * @param filters
     * @param relatedListNameParam
     */
    private void addSourceLeadsFilter(User user, List<IFilter> filters, String relatedListNameParam) {
        if (StringUtils.isBlank(relatedListNameParam)) {
            return;
        }
        if (!configCacheService.isPartnerEnabled(user.getTenantId())) {
            return;
        }
        IObjectDescribe partnerDescribe = serviceFacade.findObject(user.getTenantId(), SFAPreDefineObject.Partner.getApiName());
        if (partnerDescribe == null) {
            return;
        }
        IFieldDescribe leadsFieldDescribe = partnerDescribe.getFieldDescribe(TransferConstant.TransferKey.LEADS_ID);
        if (leadsFieldDescribe == null) {
            return;
        }
        Object targetRelatedListName = leadsFieldDescribe.get(TARGET_RELATED_LIST_NAME);
        if (!relatedListNameParam.equals(targetRelatedListName)) {
            return;
        }
        // 所有被其他合作伙伴选为源线索的线索id
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> innerFilters = query.getFilters();
        SearchUtil.fillFilterIsNotNull(innerFilters, "leads_id");
        query.setLimit(1000);
        List<IObjectData> sourceLeadsIdList = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.Partner.getApiName(), query, Lists.newArrayList("leads_id"));
        if (CollectionUtils.isEmpty(sourceLeadsIdList)) {
            return;
        }
        Set<String> sourceIdSet = sourceLeadsIdList.stream()
                .filter(data -> StringUtils.isNotEmpty(ObjectDataUtils.getValueOrDefault(data, "leads_id", "")))
                .map(data -> ObjectDataUtils.getValueOrDefault(data, "leads_id", ""))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(sourceIdSet)) {
            return;
        }
        SearchUtil.fillFilterNotIn(filters, DBRecord.ID, sourceIdSet);
    }

    private List<String> getAccountAssociatedLeadsIdList(String accountId) {
        String querySql = String.format("select  leads_id  from  biz_leads_transfer_log " +
                "where tenant_id='%s' and " +
                "account_id ='%s' and is_deleted=0", controllerContext.getTenantId(), accountId);
        List<Map> queryResult;
        try {
            queryResult = objectDataService.findBySql(controllerContext.getTenantId(), querySql);
        } catch (Exception e) {
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        List<String> result = Lists.newArrayList();
        if (!queryResult.isEmpty()) {
            result = queryResult.stream().map(r -> r.get("leads_id").toString()).collect(Collectors.toList());
        }
        return result;
    }

    private ISpecifiedTableParameter getSpecifiedTableParameter(String apiName) {
        ISpecifiedTableParameter specifiedTableParameter = new SpecifiedTableParameter();
        ISpecifiedTableParameter.JoinCondition joinCondition1 = ISpecifiedTableParameter.JoinCondition.builder().mainTableColumn("id").joinTableColumn("leads_id").build();
        ISpecifiedTableParameter.JoinCondition joinCondition2 = ISpecifiedTableParameter.JoinCondition.builder().mainTableColumn("tenant_id").joinTableColumn("tenant_id").build();
        specifiedTableParameter.setJoinConditions(Lists.newArrayList(joinCondition1, joinCondition2));
        specifiedTableParameter.setJoinPattern(SpecifiedTableParameter.JoinPatternNotExists);
        if (SFAPreDefineObject.Contact.getApiName().equals(apiName)) {
            specifiedTableParameter.setTableName("biz_leads_transfer_log");
            specifiedTableParameter.setWhereConditions(Lists.newArrayList(String.format("biz_leads_transfer_log.tenant_id='%s'", controllerContext.getTenantId()), "biz_leads_transfer_log.contact_id !=''", "biz_leads_transfer_log.is_deleted =0 "));
        } else if (SFAPreDefineObject.Opportunity.getApiName().equals(apiName)) {
            specifiedTableParameter.setTableName("biz_opportunity");
            specifiedTableParameter.setWhereConditions(Lists.newArrayList(String.format("biz_opportunity.tenant_id='%s'", controllerContext.getTenantId()), "biz_opportunity.leads_id !=''", "biz_opportunity.is_deleted =0 "));
        }
        // 新需求线索可以一对多转换为商机，取消此条件
//        else if (SFAPreDefineObject.NewOpportunity.getApiName().equals(apiName)) {
//            specifiedTableParameter.setTableName("new_opportunity");
//            specifiedTableParameter.setWhereConditions(Lists.newArrayList(String.format("new_opportunity.tenant_id='%s'", controllerContext.getTenantId()), "new_opportunity.leads_id !=''", "new_opportunity.is_deleted =0 "));
//        }
        else if (SFAPreDefineObject.Account.getApiName().equals(apiName)) {
            specifiedTableParameter.setTableName("biz_account");
            specifiedTableParameter.setWhereConditions(Lists.newArrayList(String.format("biz_account.tenant_id='%s'", controllerContext.getTenantId()), "biz_account.leads_id !=''", "biz_account.is_deleted =0 "));
        } else {
            specifiedTableParameter = null;
        }
        return specifiedTableParameter;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
        ObjectDataDocument document = arg.getObjectData();
        if (document != null && document.size() > 0) {
            String accountId = document.getOrDefault("associate_account_id", "").toString();
            if (!Strings.isNullOrEmpty(accountId)) {
                List<IFilter> filters = Lists.newArrayList();
                List<String> intersectionLeadsIds = getAccountAssociatedLeadsIdList(accountId);
                if (!CollectionUtils.isEmpty(intersectionLeadsIds)) {
                    SearchUtil.fillFilterIn(filters, DBRecord.ID, intersectionLeadsIds);
                } else {
                    SearchUtil.fillFilterIn(filters, DBRecord.ID, Lists.newArrayList("null"));
                }
                SearchListUtil.setSearchTemplateQueryPattern(searchQuery, filters);
            }
        }
        if (!arg.isIncludeAssociated()) {
            ISpecifiedTableParameter specifiedTableParameter = getSpecifiedTableParameter(arg.getTargetObjectApiName());
            searchQuery.setSpecifiedTableParameter(specifiedTableParameter);
        }
        addSourceLeadsFilter(controllerContext.getUser(), searchQuery.getFilters(), arg.getRelatedListName());
        return searchQuery;
    }

    @Override
    protected BaseListController.Result doService(Arg arg) {
        return super.doService(arg);
    }


}
