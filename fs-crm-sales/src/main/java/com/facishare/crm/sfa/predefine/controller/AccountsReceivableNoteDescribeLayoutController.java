package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.LayoutConstants;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableNoteObjConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class AccountsReceivableNoteDescribeLayoutController extends SFADescribeLayoutController {
    /**
     * 编辑时，客户只读
     */
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        String layoutType = arg.getLayout_type();
        if (layoutType.equals(LayoutTypes.EDIT)) {
            List<FormComponent> list = LayoutExt.of(result.getLayout()).getFormComponents().stream().map(FormComponentExt::getFormComponent).filter(FormComponent.class::isInstance).map(FormComponent.class::cast).collect(Collectors.toList());
            for (FormComponent component : list) {
                PreDefLayoutUtil.setFormComponentFieldReadOnly(component
                        , Lists.newArrayList("account_id", AccountsReceivableNoteObjConstants.Field.OpeningBalance.apiName));
                PreDefLayoutUtil.removeSomeFields(component, Sets.newHashSet("object_receivable"));
            }
        }
        if (layoutType.equals(LayoutTypes.ADD)) {
            List<FormComponent> list = LayoutExt.of(result.getLayout()).getFormComponents().stream().map(FormComponentExt::getFormComponent).filter(FormComponent.class::isInstance).map(FormComponent.class::cast).collect(Collectors.toList());
            for (FormComponent component : list) {
                PreDefLayoutUtil.removeSomeFields(component, Sets.newHashSet("object_receivable"));
            }
        }
    }
}
