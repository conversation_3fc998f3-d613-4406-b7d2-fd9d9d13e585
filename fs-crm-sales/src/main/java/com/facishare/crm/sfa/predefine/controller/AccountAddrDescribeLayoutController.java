package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Optional;

/**
 * 地址布局 class
 *
 * <AUTHOR>
 * @date 2019/2/19
 */
public class AccountAddrDescribeLayoutController extends SFADescribeLayoutController {
    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        switch (arg.getLayout_type()) {
            case LayoutExt.Add_LAYOUT_TYPE:
                //编辑时需要将默认、主地址移除 置为不可编辑
                handleDefaultAddrAndShipAddr();
                break;
            case LayoutExt.Edit_LAYOUT_TYPE:
                //编辑时需要将添加客户  并且置为只读
                IFormField formField = new FormField();
                formField.setFieldName("account_id");
                formField.setRenderType(IFieldType.MASTER_DETAIL);
                formField.setRequired(true);
                formField.setReadOnly(true);
                Optional<IFieldSection> fieldSection = formComponent.getFieldSections().stream()
                        .filter(it -> "base_field_section__c".equals(it.getName()))
                        .findFirst();
                if (fieldSection.isPresent()) {
                    List<IFormField> fieldsList = fieldSection.get().getFields();
                    fieldsList.add(0, formField);
                    fieldSection.get().setFields(fieldsList);
                }
                //编辑时需要将默认、主地址移除 置为不可编辑
                handleDefaultAddrAndShipAddr();
                break;
            default:
                break;
        }
    }

    public void handleDefaultAddrAndShipAddr(){
        if(!AccountUtil.isOpenAccountAddrConfig(controllerContext.getUser())){
            PreDefLayoutUtil.removeSomeFields(formComponent, Sets.newHashSet("is_default_add", "is_ship_to_add"));
        }
    }
}
