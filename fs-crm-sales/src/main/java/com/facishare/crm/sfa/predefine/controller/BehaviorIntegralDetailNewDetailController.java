package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.GroupComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 行为积分明细 new detail
 *
 * <AUTHOR>
 */
public class BehaviorIntegralDetailNewDetailController extends StandardNewDetailController {
    @Override
    protected void doFunPrivilegeCheck() {
        //Don't check function privilege
    }

    @Override
    protected void doDataPrivilegeCheck(Arg arg) {
        //Don't check data privilege
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        //清空详情页按钮
        layout.setButtons(Lists.newArrayList());
        //只保存详细信息卡片
        LayoutExt.of(layout).getComponentByApiName(ComponentExt.MIDDLE_COMPONENT_NAME).ifPresent(
                middleComponent -> {
                    List<IComponent> childComponents = GroupComponentExt.of(((GroupComponent) middleComponent))
                            .getChildComponentsSilently();
                    childComponents.removeIf(component -> !ComponentExt.FORM_COMPONENT.equals(component.getName()));
                    ((GroupComponent) middleComponent).setChildComponents(childComponents);
                    layout.setComponents(Lists.newArrayList(middleComponent));
                });
        return layout;
    }
}
