package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.lto.rest.models.IndustryCompanyAdvance;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.enums.BizCurrencyEnum;
import com.facishare.crm.sfa.predefine.enums.CompanyStatusEnum;
import com.facishare.crm.sfa.predefine.enums.CompanyTypeEnum;
import com.facishare.crm.sfa.predefine.enums.StockTypeEnum;
import com.facishare.crm.sfa.predefine.service.bizquery.BizQuerySearchService;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardListController;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gongchunru
 * @date : 2024/3/28 14:36
 * @description:
 */
@Slf4j
public class BizQuerySearchListController extends AbstractStandardListController<BizQuerySearchListController.BizQuerySearchListArg> {

    private static final BizQuerySearchService bizQuerySearchService = SpringUtil.getContext().getBean(BizQuerySearchService.class);

    List<IndustryCompanyAdvance.IndustryCompany> companyList;

    BizQuerySearchListResult bizQuerySearchListResult = new BizQuerySearchListResult();

    @Override
    protected void before(BizQuerySearchListArg arg) {
        init();
        initRequestContext();
        IndustryCompanyAdvance.Arg queryArg = buildQueryArg();
        IndustryCompanyAdvance.Result companyAdvanceResult = bizQuerySearchService.getCompanyAdvance(controllerContext.getTenantId(), queryArg);
        companyList = companyAdvanceResult.getData();
        bizQuerySearchListResult.setTotal(companyAdvanceResult.getTotal());
    }


    @Override
    protected BizQuerySearchListResult doService(BizQuerySearchListArg arg) {
        List<ObjectDataDocument> dataList = getDataList(companyList);
        bizQuerySearchListResult.setDataList(dataList);
        buildButtonInfo(dataList);
        return bizQuerySearchListResult;
    }

    private void buildButtonInfo(List<ObjectDataDocument> dataList) {
        List<IObjectData> collect = dataList.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        queryResult.setData(collect);
        List<IUdefButton> udefButtonList = serviceFacade.findCustomButtonByUsePage(objectDescribe.getApiName(), getUsePageType(),
                controllerContext.getUser());
        List<IButton> iButtons = serviceFacade.filterFunPrivilege(controllerContext.getUser(), objectDescribe, udefButtonList);
        List<String> showButton = iButtons.stream().map(IButton::getName).collect(Collectors.toList());
        udefButtonList.removeIf(udefButton -> !showButton.contains(udefButton.getApiName()));

        Map<String, List<String>> buttonMap = Maps.newHashMap();
        for (IObjectData data : queryResult.getData()) {
            buttonMap.put(data.getId(), udefButtonList.stream().map(IUdefButton::getApiName).collect(Collectors.toList()));
        }
        ButtonInfo buttonInfo = ButtonInfo.builder()
                .buttons(ButtonDocument.ofList(udefButtonList))
                .buttonMap(buttonMap)
                .build();
        result = buildResult(listLayouts, buildSearchTemplateQuery(), queryResult);
        bizQuerySearchListResult.setButtonInfo(buttonInfo);
    }


    private List<ObjectDataDocument> getDataList(List<IndustryCompanyAdvance.IndustryCompany> companyList) {
        List<ObjectDataDocument> dataList = Lists.newArrayList();

        if (companyList == null || companyList.isEmpty()) {
            return dataList;
        }

        for (IndustryCompanyAdvance.IndustryCompany company : companyList) {
            ObjectDataDocument dataDocument = new ObjectDataDocument();
            dataDocument.put("_id", company.getCompanyId());
            dataDocument.put("company_id", company.getCompanyId());
            dataDocument.put("company_name", company.getCompanyName());
            dataDocument.put("name", company.getCompanyName());
            dataDocument.put("company_type", company.getCompanyType());
            dataDocument.put("company_type_label", company.getCompanyType());
            dataDocument.put("labels", StringUtils.isBlank(company.getLabels()) ? null : company.getLabels());
            dataDocument.put("logo", company.getLogo());
            dataDocument.put("simple_name", company.getSimpleName());
            dataDocument.put("legal_person", company.getLegalPerson());
            dataDocument.put("started_date", company.getStartedDate());
            dataDocument.put("reg_capital", company.getRegCapital());
            dataDocument.put("employee_num", company.getEmployeeNum());
            dataDocument.put("province", company.getProvince());
            dataDocument.put("city", company.getCity());
            dataDocument.put("district", company.getDistrict());
            dataDocument.put("category_first", company.getCategoryFirst());
            dataDocument.put("category_second", company.getCategorySecond());
            dataDocument.put("phone", bizQuerySearchService.fillElementInfo(company.getPhones()));
            dataDocument.put("email", bizQuerySearchService.fillElementInfo(company.getEmails()));
            dataDocument.put("website", bizQuerySearchService.fillElementInfo(company.getWebsites()));
            dataDocument.put("address", company.getAddresses());
            dataDocument.put("object_describe_api_name", SFAPreDefineObject.BizQuerySearch.getApiName());
            // company.getProducts() 如果全空字符串，则输入 Lists.newArrayList()
            if (company.getProducts() != null) {
                company.getProducts().removeIf(StringUtils::isBlank);
            }
            dataDocument.put("products", company.getProducts());
            String area = "";
            if (company.getProvince() != null) {
                area = company.getProvince();
            }
            if (company.getCity() != null) {
                area = area + "-" + company.getCity();
            }
            if (company.getDistrict() != null) {
                area = area + "-" + company.getDistrict();
            }
            dataDocument.put("area", area);
            String category = "";
            if (StringUtils.isNotBlank(company.getCategoryFirst())) {
                category = company.getCategoryFirst();
            }
            if (StringUtils.isNotBlank(company.getCategorySecond())) {
                category = category + "->" + company.getCategorySecond();
            }
            if (StringUtils.isNotBlank(company.getCategoryThird())) {
                category = category + "->" + company.getCategoryThird();
            }
            if (StringUtils.isNotBlank(company.getCategoryFourth())) {
                category = category + "->" + company.getCategoryFourth();
            }
            dataDocument.put("category", category);
            dataList.add(dataDocument);
        }

        return dataList;
    }

    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        super.beforeQueryData(query);
    }

    IndustryCompanyAdvance.Arg buildQueryArg() {
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
        int limit = query.getLimit() == 0 ? 10 : query.getLimit();
        int page = (query.getOffset() / limit) == 0 ? 1 : (query.getOffset() / limit) + 1;
        IndustryCompanyAdvance.Arg companyArg = IndustryCompanyAdvance.Arg.builder()
                .pageNumber(page)
                .pageSize(limit)
                .sortWay(0)
                .build();

        for (IFilter filter : query.getFilters()) {
            if (filter.getFieldValues() == null ||
                    filter.getFieldValues().isEmpty() ||
                    "-1".equals(filter.getFieldValues().get(0))) {
                continue;
            }
            switch (filter.getFieldName()) {
                case "companyStatus":
                    int status = Integer.parseInt(filter.getFieldValues().get(0));
                    CompanyStatusEnum statusEnum = CompanyStatusEnum.fromValue(status);
                    if (statusEnum == null) {
                        break;
                    }
                    companyArg.setCompanyStatus(statusEnum.getLabel());
                    break;
                case "reg_capital":
                    if (filter.getFieldValues().get(0) != null && !"".equals(filter.getFieldValues().get(0))) {
                        companyArg.setRegCapitalMin(Long.parseLong(filter.getFieldValues().get(0)));
                    }
                    if (filter.getFieldValues().get(1) != null && !"".equals(filter.getFieldValues().get(1))) {
                        companyArg.setRegCapitalMax(Long.parseLong(filter.getFieldValues().get(1)));
                    }
                    break;
                case "categoryFirst":
                    companyArg.setCategoryFirst(filter.getFieldValues().get(0));
                    break;
                case "categorySecond":
                    companyArg.setCategorySecond(filter.getFieldValues().get(0));
                    break;
                case "categoryThird":
                    companyArg.setCategoryThird(filter.getFieldValues().get(0));
                    break;
                case "categoryFourth":
                    companyArg.setCategoryFourth(filter.getFieldValues().get(0));
                    break;
                case "province_s":
                    companyArg.setProvince(filter.getFieldValues().get(0));
                    break;
                case "city_s":
                    companyArg.setCity(filter.getFieldValues().get(0));
                    break;
                case "district_s":
                    companyArg.setDistrict(filter.getFieldValues().get(0));
                    break;
                case "employee_num":
                    if (filter.getFieldValues().get(0) != null && !"".equals(filter.getFieldValues().get(0))) {
                        companyArg.setEmployeeNumMin(Long.parseLong(filter.getFieldValues().get(0)));
                    }
                    if (filter.getFieldValues().get(1) != null && !"".equals(filter.getFieldValues().get(1))) {
                        companyArg.setEmployeeNumMax(Long.parseLong(filter.getFieldValues().get(1)));
                    }
                    break;
                case "stockType":
                    int stockType = Integer.parseInt(filter.getFieldValues().get(0));
                    StockTypeEnum stockTypeEnum = StockTypeEnum.fromValue(stockType);
                    if (stockTypeEnum == null) {
                        break;
                    }
                    companyArg.setStockType(stockTypeEnum.getLabel());
                    break;
                case "currency":
                    int currency = Integer.parseInt(filter.getFieldValues().get(0));
                    BizCurrencyEnum currencyEnum = BizCurrencyEnum.fromValue(currency);
                    companyArg.setCurrency(currencyEnum == null ? BizCurrencyEnum.RMB.getLabel() : currencyEnum.getLabel());
                    break;
                case "hasProduct":
                    companyArg.setHasProduct(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasZhixing":
                    companyArg.setHasZhixing(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasBid":
                    companyArg.setHasBid(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasPunish":
                    companyArg.setHasPunish(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasXianxiao":
                    companyArg.setHasXianxiao(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasAbnormal":
                    companyArg.setHasAbnormal(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasPhone":
                    companyArg.setHasPhone(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasEmail":
                    companyArg.setHasEmail(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "hasRegHg":
                    companyArg.setHasRegHg(Integer.parseInt(filter.getFieldValues().get(0)));
                    break;
                case "keywords":
                    companyArg.setKeyword(filter.getFieldValues().get(0));
                    break;
                case "company_type_simple":
                    int companyType = Integer.parseInt(filter.getFieldValues().get(0));
                    CompanyTypeEnum typeEnum = CompanyTypeEnum.fromValue(companyType);
                    if (typeEnum == null) {
                        break;
                    }
                    companyArg.setCompanyType(typeEnum.getLabel());
                    break;
                case "sortWay":
                    int sortWay = filter.getFieldValues().get(0) == null ? 0 : Integer.parseInt(filter.getFieldValues().get(0));
                    // 兼容前端组件，为0的时候前端识别不到 前端从1开始，但是 bi 是从0开始
                    companyArg.setSortWay(sortWay - 1);
                    break;
            }
        }
        return companyArg;
    }

    @Override
    protected BizQuerySearchListResult after(BizQuerySearchListArg arg, Result result) {
        bizQuerySearchListResult.setObjectDescribe(ObjectDescribeDocument.of(super.findObject()));
        bizQuerySearchListResult.setListLayouts(result.getListLayouts());
        bizQuerySearchListResult.setLimit(buildSearchTemplateQuery().getLimit());
        bizQuerySearchListResult.setOffset(buildSearchTemplateQuery().getOffset());
        Long memberRuleLimit = bizQuerySearchService.getMemberRuleLimit(controllerContext.getTenantId(), controllerContext.getUser().getUpstreamOwnerIdOrUserId());
        bizQuerySearchListResult.setQuotaTotal(memberRuleLimit);
        bizQuerySearchListResult.setQuotaUsed(bizQuerySearchService.getUserTransferCount(controllerContext.getTenantId(), controllerContext.getUser().getUpstreamOwnerIdOrUserId()));
        bizQuerySearchListResult.setTotal(result.getTotal());
        return bizQuerySearchListResult;
    }

    public static class BizQuerySearchListArg extends StandardListController.Arg {

    }


    @Data
    public static class BizQuerySearchListResult extends BaseListController.Result {


        @JSONField(
                name = "quotaTotal"
        )
        Long quotaTotal;

        @JSONField(
                name = "quotaUsed"
        )
        Long quotaUsed;

        @Override
        public boolean equals(Object o) {
            return super.equals(o);
        }

        @Override
        public int hashCode() {
            return super.hashCode();
        }
    }
}
