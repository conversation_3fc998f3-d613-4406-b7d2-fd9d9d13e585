package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

public class ContactListController extends StandardListController {

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        //keyword搜索，搜name or mobile
        String keyword = arg.getKeyword();

        if (!Strings.isNullOrEmpty(keyword)) {
            int index = 1;
            StringBuffer pattern = new StringBuffer();
            for (IFilter item : query.getFilters()) {
                pattern.append(index).append(" AND ");
                index = index + 1;
            }
            List<IFilter> filters = Lists.newArrayList();
            IFilter filter = new Filter();
            filter.setFieldName("name");
            filter.setOperator(Operator.LIKE);
            filter.setFieldValues(Lists.newArrayList(keyword));
            filters.add(filter);
            filter = new Filter();
            filter.setFieldName("mobile");
            filter.setOperator(Operator.LIKE);
            filter.setFieldValues(Lists.newArrayList(keyword));
            filters.add(filter);
            filter = new Filter();
            filter.setFieldName("is_deleted");
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList("0"));
            filters.add(filter);
            pattern.append(" (( " + (index++) + " OR " + (index++)+ ")) ");
            query.setFilters(filters);
            query.setPattern(pattern.toString());
        }
        return query;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if(RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()){
            ContactUtil.addButton(controllerContext.getUser(), result);
        }
        if (CollectionUtils.empty(result.getDataList())) {
            return result;
        }
        //列表需要下发手机号归属地信息
        if ((RequestUtil.isCepRequest() && (RequestUtil.isMobileOrH5Request()  || RequestUtil.isWXMiniProgram())) || AccountUtil.isGrayContactListFindBelonging(controllerContext.getTenantId())) {
            serviceFacade.fillPhoneNumberInformation(objectDescribe, ObjectDataDocument.ofDataList(result.getDataList()));
        }
        ContactUtil.fillPhoneNumberInformation(result, objectDescribe);

        if (CollectionUtils.empty(arg.getFieldProjection())) {
            ContactUtil.handlePredefinePhoneField(ObjectDataDocument.ofDataList(result.getDataList()));
        }
        if (CollectionUtils.empty(arg.getFieldProjection()) || arg.getFieldProjection().contains("date_of_birth")) {
            ContactUtil.concatenateBirthDay(ObjectDataDocument.ofDataList(result.getDataList()));
        }

        return result;
    }

    @Override
    protected Set<String> getUnauthorizedFields() {
        Set<String> unauthorizedFields = super.getUnauthorizedFields();
        if (unauthorizedFields != null && unauthorizedFields.contains("tel")) {
            unauthorizedFields.addAll(Sets.newHashSet("tel1", "tel2", "tel3", "tel4", "tel5"));
        }
        if (unauthorizedFields != null && unauthorizedFields.contains("mobile")) {
            unauthorizedFields.addAll(Sets.newHashSet("mobile1", "mobile2", "mobile3", "mobile4", "mobile5"));
        }
        return unauthorizedFields;
    }

}
