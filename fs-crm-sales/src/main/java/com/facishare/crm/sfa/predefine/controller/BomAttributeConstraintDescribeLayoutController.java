package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.LayoutConstants;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;


@Slf4j
public class BomAttributeConstraintDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        if (Objects.equals(arg.getLayout_type(), LAYOUT_TYPE_EDIT)) {
            PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, Lists.newArrayList("product_id", "bom_id"));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (GrayUtil.bomMasterSlaveMode(controllerContext.getTenantId()) && StringUtils.equalsAny(arg.getLayout_type(), LAYOUT_TYPE_EDIT, LAYOUT_TYPE_ADD)) {
            ILayout newLayout = Optional.ofNullable(after).map(Result::getLayout).map(LayoutDocument::toLayout).orElse(null);
            if (Objects.isNull(newLayout)) {
                return after;
            }
            dealField(newLayout, arg.getLayout_type());
        }
        return after;
    }

    private void dealField(ILayout layout, String type) {
        IFormField formField = new FormField();
        formField.setFieldName(BomConstants.FIELD_CORE_ID);
        formField.setRenderType(IFieldType.OBJECT_REFERENCE);
        formField.setRequired(true);
        formField.setReadOnly(false);
        if (Objects.equals(LAYOUT_TYPE_EDIT, type)) {
            formField.setReadOnly(true);
        }
        try {
            layout.getComponents().forEach(iComponent -> {
                if (Objects.equals(iComponent.getName(), LayoutConstants.FORM_COMPONENT_API_NAME)) {
                    FormComponent fc = (FormComponent) iComponent;
                    Optional<IFieldSection> fieldSection = fc.getFieldSections().stream()
                            .filter(it -> it.getName().equals(LayoutConstants.BASE_FIELD_SECTION_API_NAME))
                            .findFirst();
                    if (!fieldSection.isPresent()) {
                        return;
                    }
                    IFieldSection fieldSectionData = fieldSection.get();
                    List<IFormField> fieldSectionDataFields = fieldSectionData.getFields();
                    if (CollectionUtils.notEmpty(fieldSectionDataFields)) {
                        fieldSectionDataFields.removeIf(o -> StringUtils.equalsAny(o.getFieldName(), BomConstants.FIELD_CORE_ID, BomConstants.FIELD_PRODUCT_ID));
                        fieldSectionDataFields.add(formField);
                    }
                    fieldSectionData.setFields(fieldSectionDataFields);
                }
            });
        } catch (MetadataServiceException e) {
            log.error("get components error", e);
        }
    }


}