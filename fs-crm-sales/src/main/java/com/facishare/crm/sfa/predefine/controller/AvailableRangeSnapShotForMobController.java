package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForMobController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/6
 */
public class AvailableRangeSnapShotForMobController extends StandardSnapShotForMobController {

    private static final UseRangeFieldDataRender useRangeFieldDataRender = SpringUtil.getContext().getBean(UseRangeFieldDataRender.class);

    @Override
    protected void fillFieldInfo(User user, IObjectDescribe objectDescribe, Map<String, Object> objData) {
        super.fillFieldInfo(user, objectDescribe, objData);
        useRangeFieldDataRender.userRangeSnapShotHandle(controllerContext.getTenantId(), Utils.AVAILABLE_RANGE_OBJ_API_NAME, objData);
    }
}
