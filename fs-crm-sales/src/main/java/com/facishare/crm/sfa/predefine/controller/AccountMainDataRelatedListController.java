package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

public class AccountMainDataRelatedListController extends SFARelatedListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        return query;
    }
    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
        if (SFAPreDefineObject.AccountMainData.getApiName().equals(arg.getObjectApiName()) || "associated_object_field_related_list_name".equals(arg.getRelatedListName())) {
            if(!AccountMainDataUtil.enableCommonPrivilege(controllerContext.getRequestContext())) {
                searchQuery.setPermissionType(0);
            }
        }
        return searchQuery;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        MaskUtil.maskEncodeOfDataListByFiledList(controllerContext.getUser(), ObjectDataDocument.ofDataList(result.getDataList()),objectDescribe);
        return result;
    }
}
