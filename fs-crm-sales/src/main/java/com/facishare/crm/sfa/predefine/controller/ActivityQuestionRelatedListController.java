package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.ActivityQuestionConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.utils.CollectionUtil;
import com.facishare.crm.sfa.lto.utils.DateUtil;
import com.facishare.crm.sfa.lto.utils.SearchUtil;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crm.util.ActivityQuestionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SelectOneExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ActivityQuestionRelatedListController extends SFARelatedListController {
    private boolean isSkipAuthority = false;
    private String linkField = "";
    private String linkFieldValue = "";

    //建议话题类型
    public static final String QUESTION_TYPE_SUGGESTION = "2";
    //建议话题标签过滤的值
    public List<String> SUGGESTION_TAGS_FILTER_VALUE = Lists.newArrayList();

    public static final List<String> INVALID_VALUE = Lists.newArrayList("1","-1");
    @Override
    protected void before(Arg arg) {
        checkSkipAuthority();
        super.before(arg);
    }
    @Override
    protected void doFunPrivilegeCheck() {
        if (isSkipAuthority) {
            return;
        }
        super.doFunPrivilegeCheck();
    }
    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
        if(ObjectUtils.allNotEmpty(arg.getExtraData()) && arg.getExtraData().containsKey("active_record_id")){
            linkField = "active_record_id";
            linkFieldValue = arg.getExtraData().get("active_record_id").toString();
        }else{
            if(CollectionUtils.notEmpty(searchQuery.getFilters())){
                for(IFilter filters : searchQuery.getFilters()){
                    if(filters.getFieldName().equals("account_id")){
                        linkField = filters.getFieldName();
                        linkFieldValue = filters.getFieldValues().get(0);
                        break;
                    }
                }
            }
        }
        //处理建议话题的是否根据类型搜索
        ISearchTemplateQuery query = SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
        if(CollectionUtils.notEmpty(query.getFilters())){
            Optional<IFilter> filters = query.getFilters().stream().filter(f->f.getFieldName().equals("library_tags")).findFirst();
            if(filters.isPresent()){
                SUGGESTION_TAGS_FILTER_VALUE = filters.get().getFieldValues();
            }

        }
        if(isSkipAuthority){
            searchQuery.setPermissionType(0);
            arg.setSearchRichTextExtra(true);
        }
        if (linkFieldValue != null && searchQuery.getFilters().stream().anyMatch(f -> "question_type".equals(f.getFieldName()) && QUESTION_TYPE_SUGGESTION.equals(f.getFieldValues().get(0)))) {
            ActivityQuestionUtil.addFilterByAplFunction(controllerContext.getUser(), serviceFacade, linkFieldValue, searchQuery);
        }
        return searchQuery;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        handleSuggestionTopic(result, controllerContext.getUser());
        markTheSuggestedTopic(result, controllerContext.getUser());
        fillLibraryContent(result);
        filterInvalidActiveRecord(result.getDataList());
        return result;
    }

    public void handleSuggestionTopic(Result result, User user){
        if(CollectionUtils.empty(result.getDataList()) || !isSkipAuthority){
            return;
        }
        List<String>  suggestionTopicList = result.getDataList()
                .stream()
                .filter(data -> ObjectUtils.allNotEmpty(data.get(ActivityQuestionConstants.Field.MATCH_SUGGEST_TOPIC)))
                .map(data -> (List<String>)data.get(ActivityQuestionConstants.Field.MATCH_SUGGEST_TOPIC))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        if(CollectionUtils.empty(suggestionTopicList)){
            return;
        }
        StopWatch stopWatch = StopWatch.create("handleSuggestionTopic");
        List<IObjectData> suggestionTopicDataList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(),suggestionTopicList,SFAPreDefineObject.ActivityQuestion.getApiName());
        List<String> libraryIds = suggestionTopicDataList.stream().filter(data -> ObjectUtils.allNotEmpty(data.get("library_id"))).map(data -> data.get("library_id").toString()).collect(Collectors.toList());
        if(CollectionUtils.empty(libraryIds)){
           return;
        }
        stopWatch.lap("suggestionTopicDataList");
        List<IObjectData> libraryList = serviceFacade.findObjectDataByIdsIgnoreAll(user.getTenantId(),libraryIds,SFAPreDefineObject.SalesTopicLibrary.getApiName());
        stopWatch.lap("libraryList");
        Map<String,String> libraryMap = libraryList.stream().collect(Collectors.toMap(data -> data.getId(), data -> data.get("content",String.class)));
        Map<String,String> suggestionTopicMap = suggestionTopicDataList.stream().filter(data -> ObjectUtils.allNotEmpty(data.get("library_id"))).collect(Collectors.toMap(data -> data.getId(), data -> getLibraryContext(libraryMap,data)));
        Map<String,Integer> suggestionOrderFieldMap = suggestionTopicDataList.stream().collect(Collectors.toMap(data -> data.getId(), data -> data.get("order_field",Integer.class)));
        String match_suggest_topic__r=ActivityQuestionConstants.Field.MATCH_SUGGEST_TOPIC+"__r";
        result.getDataList().stream().forEach(x->{
            if(ObjectUtils.allNotEmpty(x.get(ActivityQuestionConstants.Field.MATCH_SUGGEST_TOPIC))){
                List<Map<String,Object>> matchSuggestTopicMapList = (List<Map<String, Object>>) x.get(match_suggest_topic__r);
                matchSuggestTopicMapList.stream().forEach(m->{
                    if(suggestionTopicMap.containsKey(m.get("_id").toString())){
                        m.put("name__r",suggestionTopicMap.get(m.get("_id").toString()));
                        m.put("order_field",suggestionOrderFieldMap.get(m.get("_id").toString()));
                    }else{
                        m.put("name__r","");
                        m.put("order_field",null);
                    }
                });
            }
        });
        stopWatch.logSlow(0);
    }

    public String getLibraryContext(Map<String,String> libraryMap ,IObjectData data){
        if(ObjectUtils.allIsEmpty(libraryMap) || !libraryMap.containsKey(data.get("library_id",String.class))){
            return "";
        }
        return libraryMap.get(data.get("library_id",String.class));
    }

    /**
     * 互动话题组件，显示的建议话题处理未应答的话题
     * @param result
     * @param user
     */
    public void markTheSuggestedTopic(Result result, User user){
        if(CollectionUtils.empty(result.getDataList()) || !isSkipAuthority){
            return;
        }
        if(ObjectUtils.allIsEmpty(linkField)){
            return;
        }
        List<String> suggestionIdList = result.getDataList()
                .stream()
                .filter(x -> QUESTION_TYPE_SUGGESTION.equals(x.get("question_type").toString()))
                .map(ObjectDataDocument::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.empty(suggestionIdList)){
            return;
        }
        StopWatch stopWatch = StopWatch.create("handleSuggestionTopic");
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setFindExplicitTotalNum(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, ActivityQuestionConstants.Field.MATCH_SUGGEST_TOPIC, suggestionIdList);
        SearchUtil.fillFilterEq(filters, linkField, linkFieldValue);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.ActivityQuestion.getApiName(), searchTemplateQuery,Lists.newArrayList("_id",ActivityQuestionConstants.Field.MATCH_SUGGEST_TOPIC));
        String suggested_topic_mark__r = ActivityQuestionConstants.SUGGESTED_TOPIC_MARK+"__r";
        if(ObjectUtils.allIsEmpty(queryResult) || CollectionUtil.isEmpty(queryResult.getData())){
            result.getDataList().stream().forEach(x->{
                x.put(ActivityQuestionConstants.SUGGESTED_TOPIC_MARK,ActivityQuestionConstants.SuggestedMarkEnum.NOT_ASKED.getCode());
                x.put(suggested_topic_mark__r, I18N.text(ActivityQuestionConstants.SuggestedMarkEnum.NOT_ASKED.getI18Key()));
            });
            return;
        }
        stopWatch.lap("suggested_queryResult");
        Set<String> suggestionTopicIdSet = queryResult.getData().stream().map(data -> (List<String>)data.get(ActivityQuestionConstants.Field.MATCH_SUGGEST_TOPIC)).flatMap(List::stream).collect(Collectors.toSet());
        result.getDataList().stream().forEach(x->{
            if(suggestionTopicIdSet.contains(x.getId())){
                x.put(ActivityQuestionConstants.SUGGESTED_TOPIC_MARK,ActivityQuestionConstants.SuggestedMarkEnum.ALREADY_ASKED.getCode());
                x.put(suggested_topic_mark__r, I18N.text(ActivityQuestionConstants.SuggestedMarkEnum.ALREADY_ASKED.getI18Key()));
            }else{
                x.put(ActivityQuestionConstants.SUGGESTED_TOPIC_MARK,ActivityQuestionConstants.SuggestedMarkEnum.NOT_ASKED.getCode());
                x.put(suggested_topic_mark__r, I18N.text(ActivityQuestionConstants.SuggestedMarkEnum.NOT_ASKED.getI18Key()));
            }
        });
        stopWatch.logSlow(10);
    }
    public void checkSkipAuthority(){
        if(ObjectUtils.allNotEmpty(arg.getExtraData()) && arg.getExtraData().containsKey("skip_authority") && (Boolean)arg.getExtraData().get("skip_authority")){
            isSkipAuthority = true;
        }
    }
    @Override
    protected boolean needButtonInfo() {
        if(isSkipAuthority){
            return false;
        }
        return super.needButtonInfo();
    }

    /**
     * 处理建议话题移除作废的销售记录 && 正常的销售记录name字段替换成创建时间+互动类型
     *
     * @param dataList
     */
    private void filterInvalidActiveRecord(List<ObjectDataDocument> dataList) {
        if(!isSkipAuthority){
            return;
        }
        Set<String> activeRecordIds = dataList.stream()
                .filter(x -> QUESTION_TYPE_SUGGESTION.equals(x.get("question_type").toString()) && ObjectUtils.allNotEmpty(x.get("active_record_id")))
                .flatMap(d -> {
                    @SuppressWarnings("unchecked")
                    List<String> list = (List<String>) d.get("active_record_id");
                    return list.stream();
                }).collect(Collectors.toSet());
        if (activeRecordIds.isEmpty()) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("fillLibraryContent");
        IObjectDescribe activeRecordDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.ACTIVE_RECORD_API_NAME);
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, DBRecord.IS_DELETED, Lists.newArrayList("0","1","-1"));
        SearchUtil.fillFilterHasAnyOf(filters, DBRecord.ID, new ArrayList<>(activeRecordIds));
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(activeRecordIds.size());
        List<IObjectData> res = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(controllerContext.getUser(), Utils.ACTIVE_RECORD_API_NAME, query, Lists.newArrayList("_id", IObjectData.IS_DELETED,IObjectData.CREATE_TIME,"interactive_types")).getData();
        stopWatch.lap("res");
        Set<String> invalidIds = res.stream().filter(x->INVALID_VALUE.contains(x.get(DBRecord.IS_DELETED).toString()))
                .map(IObjectData::getId).collect(Collectors.toSet());
        Map<String,IObjectData> normalDataMap = res.stream().filter(x->!INVALID_VALUE.contains(x.get(DBRecord.IS_DELETED).toString()))
                .collect(Collectors.toMap(IObjectData::getId,Function.identity()));

        SelectOneExt selectOneExt = SelectOneExt.of((SelectOne) activeRecordDescribe.getFieldDescribe("interactive_types"));

        for (ObjectDataDocument data : dataList) {
            if(ObjectUtils.allIsEmpty(data.get(ActivityQuestionConstants.ACTIVE_RECORD_ID))){
                continue;
            }
            @SuppressWarnings("unchecked")
            List<String> list =  (List<String>)data.get(ActivityQuestionConstants.ACTIVE_RECORD_ID);
            Optional.ofNullable(list).ifPresent(l -> l.removeIf(invalidIds::contains));
            List<Map<String,String>> list_r =  (List<Map<String,String>>)data.get(ActivityQuestionConstants.ACTIVE_RECORD_ID+"__r");
            Optional.ofNullable(list_r).ifPresent(l -> l.removeIf(m->invalidIds.contains(m.get(DBRecord.ID))));
            list_r.stream().forEach(x->{
                String id =  x.get(DBRecord.ID);
                if(normalDataMap.containsKey(id)){
                    IObjectData  activeRecordData =  normalDataMap.get(id);
                    String creatTime = DateUtil.getDataTimeStrByTimeStamp(activeRecordData.getCreateTime());
                    String label = I18N.text("ActiveRecordObj.attribute.self.display_name");
                    if(ObjectUtils.allNotEmpty(activeRecordData.get("interactive_types"))){
                         label = selectOneExt.getLabelByValue(activeRecordData.get("interactive_types").toString());
                    }
                    x.put("name",creatTime+" "+label);
                }
            });
        }
        stopWatch.logSlow(10);
    }

    private void fillLibraryContent( Result result) {
        if (!isSkipAuthority) {
            return;
        }
        List<ObjectDataDocument> dataList =  result.getDataList();
        List<String> libraryIds = dataList.stream().map(d -> d.get(ActivityQuestionConstants.LIBRARY_ID)).filter(Objects::nonNull).map(String.class::cast).collect(Collectors.toList());
        if (libraryIds.isEmpty()) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("fillLibraryContent");
        String describeApiName = SFAPreDefineObject.SalesTopicLibrary.getApiName();
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterHasAnyOf(filters, DBRecord.ID, libraryIds);
        if(CollectionUtil.isNotEmpty(SUGGESTION_TAGS_FILTER_VALUE)){
            //前台标签过滤使用，查询话题库，按标签过滤
            log.warn("ActivityQuestionRelatedListController fillLibraryContent filter is not  tags:{}",SUGGESTION_TAGS_FILTER_VALUE);
            SearchUtil.fillFilterIn(filters, "tags", SUGGESTION_TAGS_FILTER_VALUE);
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(libraryIds.size());
        Map<String, IObjectData> map = serviceFacade.findBySearchQuery(controllerContext.getUser(), describeApiName, query)
                .getData().stream().collect(Collectors.toMap(DBRecord::getId, Function.identity(), (v1, v2) -> v1));
        stopWatch.lap("findBySearchQuery");
        //前台标签过滤使用，查询所有的建议话题，标签过滤
        if(CollectionUtil.isNotEmpty(SUGGESTION_TAGS_FILTER_VALUE)){
            dataList = dataList.stream().filter(d->map.containsKey(d.get(ActivityQuestionConstants.LIBRARY_ID).toString())).collect(Collectors.toList());
        }
        for (ObjectDataDocument data : dataList) {
            IObjectData d = map.get(String.valueOf(data.get(ActivityQuestionConstants.LIBRARY_ID)));
            String content = d.get("content__r", String.class, d.get("content", String.class, ""));
            if (StringUtils.isNotBlank(content)) {
                data.put(ActivityQuestionConstants.LIBRARY_ID + "__r", content);
            }
            data.put("library_tags", d.get("tags__r", String.class, d.get("tags", String.class, "")));
        }
        result.setDataList(dataList);
        result.setTotal(dataList.size());
        stopWatch.logSlow(10);
    }
}
