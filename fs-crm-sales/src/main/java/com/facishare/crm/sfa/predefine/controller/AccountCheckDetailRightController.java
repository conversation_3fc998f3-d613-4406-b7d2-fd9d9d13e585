package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardSearchListController;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by yuanjl on 2019/1/11.
 */
public class AccountCheckDetailRightController extends StandardSearchListController {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<IObjectData> dataList = result.getDataList().stream().map(x -> x.toObjectData()).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(dataList)) {
            Map<String, Permissions> permissionsMap = serviceFacade.checkDataPrivilege(controllerContext.getUser(),
                    dataList, objectDescribe, ObjectAction.VIEW_DETAIL.getActionCode());
            if (CollectionUtils.empty(permissionsMap)) {
                return Result.builder().build();
            }
            List<ObjectDataDocument> finalDataList = Lists.newArrayList(result.getDataList());
            for (ObjectDataDocument item : result.getDataList()) {
                if (!permissionsMap.containsKey(item.getId()) ||
                        permissionsMap.get(item.getId()).equals(Permissions.NO_PERMISSION)) {
                    finalDataList.removeIf(x -> x.getId().equals(item.getId()));
                }
            }
            result.setDataList(finalDataList);
        }
        return result;
    }
}
