package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * @描述说明：移动端屏蔽按钮
 * @作者：chench
 * @创建日期：2024-01-04
 */
public class AttributeConstraintListController extends StandardListController {
    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> mobileLayouts = super.findMobileLayouts();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(mobileLayouts)) {
            return mobileLayouts;
        }
        for (ILayout layout : mobileLayouts) {
            layout.set("buttons", Lists.newArrayList());
        }
        return mobileLayouts;
    }
}
