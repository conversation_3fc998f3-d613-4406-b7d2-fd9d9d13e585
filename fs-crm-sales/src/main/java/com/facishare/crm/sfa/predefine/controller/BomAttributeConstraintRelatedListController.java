package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.BomConstraintConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @描述说明：
 * @作者：chench
 * @创建日期：2024-06-17
 */
public class BomAttributeConstraintRelatedListController extends StandardRelatedListController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        if(CollectionUtils.isNotEmpty(rst.getDataList())) {
            rst.getDataList().stream().filter(x-> StringUtils.isEmpty(x.toObjectData().get(BomConstraintConstants.CHECK_MODE, String.class)))
                    .forEach(item -> item.put(BomConstraintConstants.CHECK_MODE, BomConstraintConstants.CheckMode.PRODUCT.getMode()));
        }
        return rst;
    }
}
