package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardListCountController;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;


public class LeadsListCountController extends StandardListCountController {
    private ISearchTemplate searchTemplate;
	private ObjectPoolPermission.ObjectPoolPermissions poolPermissions;
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        searchTemplate = serviceFacade.findSearchTemplateByIdAndType(controllerContext.getUser(), this.getSearchTemplateId(), objectDescribe.getApiName(), arg.getSearchTemplateType());
		poolPermissions = SearchListUtil.getLeadsPoolPermissionByUserId(controllerContext.getUser(), SearchListUtil.getLeadsPoolIdByQuery(query, searchTemplate.getExtendAttribute()));
        SearchListUtil.handleLeadsSearchQuery(controllerContext.getUser(), query, searchTemplate, poolPermissions);
        return query;
    }

    @Override
    protected Query defineQuery() {
        Query searchQuery = super.defineQuery();
        searchTemplate = searchQuery.getSearchTemplate();
        return searchQuery;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
		String extendAttribute = "";
		if (searchTemplate != null) {
			extendAttribute = searchTemplate.getExtendAttribute();
		}
		String leadsPoolId = SearchListUtil.getLeadsPoolIdByQuery(searchQuery, extendAttribute);
		if (poolPermissions == null) {
			poolPermissions = SearchListUtil.getLeadsPoolPermissionByUserId(controllerContext.getUser(), leadsPoolId);
		}
        SearchListUtil.handleLeadsSupportOrSearchQuery(controllerContext.getUser(), searchQuery, searchTemplate, poolPermissions);
        return searchQuery;
    }
}
