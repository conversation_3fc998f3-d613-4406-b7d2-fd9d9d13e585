package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.enums.ReceivedPaymentStatusEnum;
import com.facishare.crm.sfa.predefine.enums.SettledStatusEnum;
import com.facishare.crm.sfa.utilities.constant.AccountsReceivableNoteObjConstants;
import com.facishare.crm.sfa.utilities.constant.PartnerConstants;
import com.facishare.crm.sfa.utilities.constant.ReceivedPaymentObjConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/9/13 1:52 下午
 * @illustration
 */
public class AccountsReceivableNoteWebDetailController extends SFAWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if(newResult.getLayout() != null){
            ILayout layout = new Layout(newResult.getLayout());
            removeDetailInfoFields(layout, Lists.newArrayList("object_receivable"));
            processButton(layout, controllerContext.getTenantId());
        }
        return newResult;
    }
    private void processButton(ILayout layout, String tenantId) {
        //List<IButton> buttons = headInfoComponentOp.get().getButtons();
        BigDecimal noSettledAmount = data.get(AccountsReceivableNoteObjConstants.Field.NoSettledAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
        WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CLONE.getActionCode()));
        //buttons.removeIf(b -> b.getAction().equals(ObjectAction.CLONE.getActionCode()));
        if (!bizConfigThreadLocalCacheService.isOpenAutoMatch(tenantId)|| noSettledAmount.compareTo(BigDecimal.ZERO) == 0) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.AUTO_MATCH.getActionCode()));
            //buttons.removeIf(b -> b.getAction().equals(ObjectAction.AUTO_MATCH.getActionCode()));
        }

        String completionStatus = data.get(AccountsReceivableNoteObjConstants.Field.CompletionStatus.apiName, String.class);
        String settledStatus = data.get(AccountsReceivableNoteObjConstants.Field.SettledStatus.apiName, String.class);
        String periodicSource = data.get(AccountsReceivableNoteObjConstants.Field.PeriodicSource.apiName, String.class, "");

        if (Objects.equals(completionStatus, AccountsReceivableNoteObjConstants.CompletionStatus.Completed.getStatus())) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.COMPLETE.getActionCode()));
            //buttons.removeIf(b -> b.getAction().equals(ObjectAction.COMPLETE.getActionCode()));
        }
        if (!Objects.equals(settledStatus, SettledStatusEnum.No_Settled.getValue())
                || Objects.equals(completionStatus, AccountsReceivableNoteObjConstants.CompletionStatus.Completed.getStatus())
                || !periodicSource.startsWith("PA_")) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.MODIFY_SETTLEMENT_RULES.getActionCode()));
            //buttons.removeIf(b -> b.getAction().equals(ObjectAction.MODIFY_SETTLEMENT_RULES.getActionCode()));
        }
        //WebDetailLayout.of(layout).setButtons(buttons);
    }
}
