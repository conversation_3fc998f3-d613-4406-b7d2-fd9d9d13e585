package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.PoolEmptyRule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/3 16:51
 */
public class HighSeasListController extends StandardListController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        List<ObjectDataDocument> documents = rst.getDataList();
        List<IObjectData> dataList = documents.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        PoolEmptyRule emptyRule = new PoolEmptyRule.Builder().poolData(dataList).build();
        emptyRule.fillEmptyRule();
        List<ObjectDataDocument> newDocuments = ObjectDataDocument.ofList(dataList);
        rst.setDataList(newDocuments);
        return rst;
    }
}
