package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/6/22 1:57 下午
 * @illustration
 */
public class BomWebDetailController extends StandardWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        ILayout iLayout = Optional.ofNullable(newResult).map(x->x.getLayout()).map(x->x.toLayout()).orElse(null);
        if (Objects.isNull(iLayout)) {
            return newResult;
        }
        WebDetailLayout.of(iLayout).setButtons(Lists.newArrayList(), "head_info");
        return newResult;
    }
}
