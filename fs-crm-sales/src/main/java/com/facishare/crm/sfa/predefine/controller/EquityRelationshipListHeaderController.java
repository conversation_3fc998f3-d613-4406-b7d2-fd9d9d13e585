package com.facishare.crm.sfa.predefine.controller;


import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.service.equityrelationship.EquityRelationshipByBIService;
import com.facishare.crm.sfa.utilities.util.EquityRelationshipRelatedUtil;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;

/**
 * <AUTHOR> lik
 * @date : 2024/9/2 18:59
 */

public class EquityRelationshipListHeaderController extends BaseSFAListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        if(!EquityRelationshipByBIService.IS_ZSJ_ENV){
            return result;
        }
        Result res = super.after(arg, result);
        return res;
    }
    @Override
    protected Result doService(Arg arg) {
        if(!EquityRelationshipByBIService.IS_ZSJ_ENV){
            boolean isMobile = RequestUtil.isMobileOrH5Request() || RequestUtil.isMobileOrMobileDeviceRequest() || RequestUtil.isWXMiniProgram();
            result = JSONObject.parseObject(EquityRelationshipRelatedUtil.MockDataOfEquityRelationshipObjListHeader(isMobile),Result.class);
            return result;
        }
        Result res = super.doService(arg);
        return res;
    }
    @Override
    protected void before(Arg arg) {
        if(!EquityRelationshipByBIService.IS_ZSJ_ENV){
            return ;
        }
        super.before(arg);
    }

    @Override
    protected void finallyDo() {
        if(!EquityRelationshipByBIService.IS_ZSJ_ENV){
            return ;
        }
        super.finallyDo();
    }
}
