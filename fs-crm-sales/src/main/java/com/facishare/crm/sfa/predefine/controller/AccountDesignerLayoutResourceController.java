package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.RiskBrainConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutResourceController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.util.StringUtils;

import java.util.List;

public class AccountDesignerLayoutResourceController extends StandardDesignerLayoutResourceController {

    @Override
    protected List<IButton> filterDetailPageButton(List<IButton> buttons, User user, ObjectDescribeExt describeExt) {
        List<IButton> list = super.filterDetailPageButton(buttons, user, describeExt);
        if (!hasRiskBrain()) {
            RiskBrainConstants.buttons.forEach(button -> list.removeIf(e -> button.getButtonApiName().equals(e.get("api_name"))));
        }
        return list;
    }

    public boolean hasRiskBrain() {
        String tenantId = controllerContext.getTenantId();
        if (StringUtils.isEmpty(tenantId)) {
            return false;
        }
        IUdefButton button = serviceFacade.findButtonByApiName(User.systemUser(tenantId), ObjectAction.RISK_PORTRAIT_ENABLE.getButtonApiName(), SFAPreDefineObject.Account.getApiName());
        return button != null;
    }
}
