package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lik
 * @date : 2024/3/28 16:23
 */
@Slf4j
public class BusinessRiskInformationWebDetailController extends SFAWebDetailController {

    @Override
    protected StandardDetailController.Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        super.after(arg, result);
        ILayout layout = new Layout(result.getLayout());
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("BPM_related_list","Approval_related_list"));
        return result;
    }
}
