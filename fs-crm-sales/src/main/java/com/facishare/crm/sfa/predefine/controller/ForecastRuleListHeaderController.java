package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.ForecastRuleConstants;
import com.google.common.collect.Sets;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
public class ForecastRuleListHeaderController extends SFAListHeaderController {
    private final Set<String> needRemoveFieldName
            = Sets.newHashSet(ForecastRuleConstants.Field.FORECAST_DATA_CONDITION
            , ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL1
            , ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL2
            , ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL3
            , ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL4
            , ForecastRuleConstants.Field.BEST_PRACTICES_FORECAST_MODEL5
            , ForecastRuleConstants.Field.AI_WEIGHT_FORECAST_MODEL
            , ForecastRuleConstants.Field.FORECAST_OBJECT_FIELDS_SHOW
            , ForecastRuleConstants.Field.FORECAST_APPLY_RANGE
            , "data_own_department"
            , "relevant_team"
            , "record_type"
            , "out_owner"
            , "lock_status"
            , "life_status"
            , "owner_department"
            , "owner"
            , "forecast_object_api_name"
            , "artificial_commitment_forecast_model"
            , "stage_weight_forecast_model"
            , "artificial_weight_forecast_model"
            , "auto_lock_after_end"
            , "auto_remind_before_end"
            , "remind_days_before_end"
    );
    @Override
    protected List<String> getAuthorizedFields() {
        List<String> authorizedFields = super.getAuthorizedFields();
        authorizedFields.removeIf(needRemoveFieldName::contains);
        return authorizedFields;
    }
}
