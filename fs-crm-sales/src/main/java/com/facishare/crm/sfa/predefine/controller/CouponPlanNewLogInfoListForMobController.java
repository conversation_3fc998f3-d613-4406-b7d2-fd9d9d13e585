package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.constants.CouponConstants;
import com.facishare.crm.sfa.predefine.service.rebatecoupon.util.CouponUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetNewLogInfoListForMod;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;

import java.util.List;

public class CouponPlanNewLogInfoListForMobController extends StandardNewLogInfoListForMobController {

    @Override
    protected GetNewLogInfoListForMod.Result doService(GetNewLogInfoListForMod.Arg arg) {
        GetNewLogInfoListForMod.Result result = super.doService(arg);
        List<LogRecord> recordList = result.getModifyRecordList();
        if (CollectionUtils.empty(recordList)) {
            return result;
        }
        CouponUtils.renderRecordList(recordList, controllerContext.getUser(),
                CouponConstants.COUPON_PLAN_API_NAME,
                CouponConstants.CouponPlanField.PRODUCT_CONDITION_TYPE.getApiName(),
                CouponConstants.CouponPlanField.PRODUCT_CONDITION_CONTENT.getApiName());
        return result;
    }
}
