package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.crm.sfa.utilities.enums.LifeStatusEnum;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

public class InvoiceApplicationLinesRelatedListController extends StandardRelatedListController {

    private final InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> result = super.getQueryResult(query);
        handleMaxInvoiceQuantityField(result);
        return result;
    }

    private void handleMaxInvoiceQuantityField(QueryResult<IObjectData> dataQueryResult) {
        List<IObjectData> dataList = dataQueryResult.getData();

        if (CollectionUtils.isNotEmpty(dataList)) {
            InvoiceApplicationConstants.InvoiceApplicationMode invoiceApplicationMode = invoiceService.getInvoiceMode(controllerContext.getTenantId());

            if(invoiceApplicationMode.equals(InvoiceApplicationConstants.InvoiceApplicationMode.SALES_ORDER_PRODUCT)){
                doMaxInvoiceQuantity(dataList);
            }
            if(invoiceApplicationMode.equals(InvoiceApplicationConstants.InvoiceApplicationMode.SALES_ORDER)){
                doMaxInvoiceAmount(dataList);
            }
        }
    }

    private void doMaxInvoiceQuantity(List<IObjectData> dataList){
        for (IObjectData data : dataList) {
            List<String> skipLifeStatus = Lists.newArrayList(LifeStatusEnum.UNDER_REVIEW.value, LifeStatusEnum.INEFFECTIVE.value);
            BigDecimal maxInvoiceQuantity = BigDecimal.ZERO;
            Object noInvoiceQuantity = data.get("no_invoice_quantity");
            Object invoicedQuantity = data.get("invoiced_quantity");
            if (noInvoiceQuantity != null) {
                maxInvoiceQuantity = maxInvoiceQuantity.add(new BigDecimal(noInvoiceQuantity.toString()));
            }

            if (invoicedQuantity != null && !skipLifeStatus.contains(data.get("life_status",String.class))) {
                maxInvoiceQuantity = maxInvoiceQuantity.add(new BigDecimal(invoicedQuantity.toString()));
            }
            data.set("max_invoice_quantity", maxInvoiceQuantity.toString());
            BigDecimal maxInvoiceAmount = BigDecimal.ZERO;
            Object invoicedAmount = data.get("invoiced_amount");
            Object noInvoiceAmount = data.get("no_invoice_amount");
            if (invoicedAmount != null && !skipLifeStatus.contains(data.get("life_status",String.class))) {
                maxInvoiceAmount = maxInvoiceAmount.add(new BigDecimal(invoicedAmount.toString()));
            }
            if (noInvoiceAmount != null) {
                maxInvoiceAmount = maxInvoiceAmount.add(new BigDecimal(noInvoiceAmount.toString()));
            }
            data.set("max_invoice_amount", maxInvoiceAmount.toString());
        }

    }
    private void doMaxInvoiceAmount(List<IObjectData> dataList){
        //订单的待开票金额 +  这条数据的金额  如果审核中 未生效 过滤掉

        HashSet<String> orderIds = Sets.newHashSet();
        HashSet<String> invoiceLineIds = Sets.newHashSet();
        List<String> skipLifeStatus = Lists.newArrayList(LifeStatusEnum.UNDER_REVIEW.value, LifeStatusEnum.INEFFECTIVE.value);
        dataList.forEach(x-> {
            orderIds.add(x.get("order_id",String.class));
            invoiceLineIds.add(x.getId());
        });
        List<IObjectData> orderDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(),
                Lists.newArrayList(orderIds), SFAPreDefineObject.SalesOrder.getApiName());
        Map<String,BigDecimal> orderMaxInvoice = Maps.newHashMap();
        if(orderDataList != null){
            orderDataList.forEach(o->{
                BigDecimal amount = BigDecimal.ZERO;
                if(o.get("no_invoice_amount") != null && !Strings.EMPTY.equals(o.get("no_invoice_amount",String.class))){
                    amount = new BigDecimal(o.get("no_invoice_amount",String.class));
                }
                orderMaxInvoice.put(o.getId(), amount);
            });
        }
        dataList.forEach(invoice->{
            if(!skipLifeStatus.contains(invoice.get("life_status",String.class))){
                String orderId = invoice.get("order_id",String.class);
                BigDecimal invoiceAmount = BigDecimal.ZERO;
                if((invoice.get("invoiced_amount") != null && !Strings.EMPTY.equals(invoice.get("invoiced_amount",String.class)))){
                    invoiceAmount = new BigDecimal(invoice.get("invoiced_amount",String.class));
                }
                BigDecimal orderMaxAmount = orderMaxInvoice.get(orderId);
                if(orderMaxAmount == null){
                    orderMaxAmount = BigDecimal.ZERO;
                }
                orderMaxAmount = orderMaxAmount.add(invoiceAmount);
                orderMaxInvoice.put(orderId,orderMaxAmount);
            }
        });
        dataList.forEach(invoice->{
            String orderId = invoice.get("order_id",String.class);
            invoice.set("max_invoice_amount",orderMaxInvoice.getOrDefault(orderId, BigDecimal.ZERO).toPlainString());
        });
    }
}
