package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.ForecastRuleService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ForecastRuleConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * Created by luxin on 2018/11/30.
 */
public class ForecastRuleListController extends StandardListController {
    private static final ForecastRuleService forecastRuleService = SpringUtil.getContext().getBean(ForecastRuleService.class);

    private boolean isFromManagement = false;
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (Objects.isNull(arg.getSearchTemplateId())) {
            isFromManagement = true;
        }
    }
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        User user = controllerContext.getUser();
        if (!isFromManagement && !isCrmAdmin(user)) {
            List<IFilter> filters = Lists.newArrayList();
            String ownerDeptId = AccountUtil.getUserMainDepartId(controllerContext.getTenantId(), user.getUserId());
            SearchUtil.fillFilterHasAnyOf(filters, ForecastRuleConstants.Field.FORECAST_APPLY_RANGE, ownerDeptId);
            query.setFilters(filters);
            query.setPermissionType(0);
        }
        return query;
    }

    private boolean isCrmAdmin(User user) {
        return user.getIsCrmAdmin().orElse(serviceFacade.isAdmin(user));
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.findData(query);
        forecastRuleService.handAmountDateLabel(controllerContext.getTenantId(), queryResult.getData());
        return queryResult;
    }
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (!isFromManagement) {
            newResult.setButtonInfo(new ButtonInfo());
        }

        return newResult;
    }
}
