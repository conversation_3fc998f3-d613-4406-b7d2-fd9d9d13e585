package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

/**
 * Created by renlb on 2018/11/6.
 */
public class AccountListController extends StandardListController {

    private final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext()
            .getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    private final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private final AccountRemainingTimeReplace accountRemainingTimeUtil = SpringUtil.getContext().getBean(AccountRemainingTimeReplace.class);

    private String extendAttribute = "";
    private String highseasId = "";
    private ISearchTemplate searchTemplate;
	private ObjectPoolPermission.ObjectPoolPermissions poolPermissions;



    //特殊场景临时处理，待底层根据场景apiname设置DataRightsParameter改造完成后迁移过去
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        ISearchTemplate searchTemplate = serviceFacade.findSearchTemplate(controllerContext.getUser(), this.getSearchTemplateId(), objectDescribe.getApiName());
        if (searchTemplate != null) {
            extendAttribute = searchTemplate.getExtendAttribute();
        }
        highseasId = SearchListUtil.getHighSeaIdByQuery(query, extendAttribute);
		User user = controllerContext.getUser();
		poolPermissions = SearchListUtil.getHighSeasPermissionByUserId(user, highseasId);
		SearchListUtil.handleAccountSearchQuery(user, query, searchTemplate, poolPermissions);

        if (GrayUtil.isAccountMultiKeyWordSearchUtil(controllerContext.getTenantId())) {
            AccountMultiKeyWordSearchUtil.handleSearchQuery(controllerContext.getUser(), query, "or");
        }
        accountRemainingTimeUtil.replaceRemainingTimeQuery(query);
        return query;
    }

    @Override
    protected Query defineQuery() {
        Query searchQuery = super.defineQuery();
        searchTemplate = searchQuery.getSearchTemplate();
        return searchQuery;
    }

    @Override
    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchQuery) {
        searchQuery = super.customSearchTemplate(searchQuery);
        if (searchTemplate != null) {
            extendAttribute = searchTemplate.getExtendAttribute();
        }
		highseasId = SearchListUtil.getHighSeaIdByQuery(searchQuery, extendAttribute);
        if (poolPermissions == null) {
			poolPermissions = SearchListUtil.getHighSeasPermissionByUserId(controllerContext.getUser(), highseasId);
		}
        SearchListUtil.handleAccountSupportOrSearchQuery(controllerContext.getUser(), searchQuery, searchTemplate, poolPermissions);

        if (GrayUtil.isAccountMultiKeyWordSearchUtil(controllerContext.getTenantId())) {
            AccountMultiKeyWordSearchUtil.handleSearchQuery(controllerContext.getUser(), searchQuery, "or");
        }
        accountRemainingTimeUtil.replaceRemainingTimeQuery(searchQuery);
        return searchQuery;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        addButton(result);
        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        //handleHighSeasName(objectDataList);
        if (CollectionUtils.empty(arg.getFieldProjection()) || arg.getFieldProjection().contains("is_remind_recycling")) {
            AccountUtil.handleIsRemindRecycling(objectDataList);
        }

        if (CollectionUtils.empty(arg.getFieldProjection()) || arg.getFieldProjection().contains("remaining_time")) {
            AccountUtil.handleRemainingTime(objectDataList);
        }

        if (Objects.equals(extendAttribute, null) && CollectionUtils.empty(arg.getFieldProjection())) {
            countOpportunity(objectDataList);
        }
        handleMemberInvisibleFields(objectDataList);
        return result;
    }

    /**
     * 移动端列表增加扫名片按钮
     * 扫名片按钮权限利用新建权限
     *
     * @param result
     */
    private void addButton(Result result) {
        if (result == null || CollectionUtils.empty(result.getListLayouts())) {
            return;
        }
        Map<String, Boolean> actionFunMap = functionPrivilegeService.funPrivilegeCheck(controllerContext.getUser(),
                SFAPreDefineObject.Account.getApiName(),
                Lists.newArrayList(ObjectAction.CREATE.getActionCode()));
        ILayout layout;
        for (LayoutDocument layoutDocument : result.getListLayouts()) {
            layout = new Layout((layoutDocument));
            if (actionFunMap.get(ObjectAction.CREATE.getActionCode())) {
                //region 开启多组织逻辑 一期屏蔽扫名片
                if (!AccountUtil.isOpenManyOrganizations(controllerContext.getUser(), objectDescribe)) {
                    if (RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()) {
                        layout.addButtons(ObjectAction.SCAN_CARD.createButton());
                    }
                }
            }
        }
    }

    //处理公海普通成员不可见字段
    private void handleMemberInvisibleFields(List<IObjectData> objectDataList) {
        if (Objects.equals(poolPermissions, ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER)
				&& !StringUtils.isEmpty(highseasId)) {
            String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);
            InvisibleFieldUtil.handleMemberInvisibleFields(controllerContext.getUser(), objectDataList,
                    "18", highseasId, clientInfo);
        }
    }

    private void countOpportunity(List<IObjectData> objectDataList) {
        if (CollectionUtils.notEmpty(objectDataList)) {
            List<String> idList = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                if (!Strings.isNullOrEmpty(objectData.getId())) {
                    idList.add(objectData.getId());
                }
            }

            if (CollectionUtils.empty(idList)) {
                return;
            }

            boolean isAdmin = controllerContext.getUser().isSupperAdmin() || serviceFacade.isAdmin(controllerContext.getUser());
            ServiceContext serviceContext = ContextConvertUtils.controllerContext2ServiceContext(controllerContext);
            String sql;
            if (NewOpportunityUtil.getIsChangeToNewOpportunity(serviceContext.getUser())) {
                sql = ConcatenateSqlUtils.getCountNewOpportunityByAccountIdSql(controllerContext.getTenantId(), idList);
            } else {
                sql = ConcatenateSqlUtils.getCountOpportunityByAccountIdSql(controllerContext.getTenantId(), idList, isAdmin);
            }

            List<Map> resultMapList = Lists.newArrayList();
            try {
                resultMapList = objectDataService.findBySql(controllerContext.getTenantId(), sql);
            } catch (MetadataServiceException e) {
                log.error("get opportunity count by account_id error.");
            }

            if (CollectionUtils.notEmpty(resultMapList)) {
                Map map = Maps.newHashMap();
                for (Map countMap : resultMapList) {
                    map.put(countMap.get("account_id"), countMap.get("count"));
                }

                for (IObjectData objectData : objectDataList) {
                    if (map.containsKey(objectData.getId())) {
                        objectData.set("opportunity_count", map.get(objectData.getId()));
                    }
                }
            }
        }
    }


}
