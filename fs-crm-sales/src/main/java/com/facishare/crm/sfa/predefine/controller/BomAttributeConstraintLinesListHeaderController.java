package com.facishare.crm.sfa.predefine.controller;


import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by yuepy
 */
public class BomAttributeConstraintLinesListHeaderController extends SFAListHeaderController {
    private  List<String> invisible_buttons = Lists.newArrayList("Add_button_default","Import_button_default","IntelligentForm_button_default","ExportFile_button_default");

    @Override
    protected List<IButton> getButtons() {
        List<IButton> buttons = super.getButtons();
        buttons.removeIf(x -> invisible_buttons.contains(x.getName()));
        return buttons;
    }

    @Override
    protected boolean needRemoveMobileButton() {
        return true;
    }
}
