package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.i18n.BomI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.controller.StandardImportViewController;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class BomCoreImportViewController extends StandardImportViewController {
    private final List<String> filterFields = Lists.newArrayList("parent_bom_id", "bom_path", "root_id", BomConstants.FIELD_NODE_BOM_CORE_VERSION, BomConstants.FIELD_NODE_BOM_CORE_TYPE, BomConstants.FIELD_IS_PACKAGE);

    @Override
    protected void customFields(IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) {
        super.customFields(describe, fieldDescribes);
        if (StringUtils.equalsAny(describe.getApiName(), Utils.PRODUCT_GROUP_API_NAME, Utils.BOM_API_NAME) && arg.getImportType() == IMPORT_TYPE_ADD) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("label", I18N.text(BomI18NKeyUtil.SFA_BOM_PATH));
            map.put("api_name", BomConstants.FIELD_SFA_RELATED_PATH_MARK);
            fieldDescribes.add(1, ImportExportExt.createField(map, "SFA_RELATED_MARK", true));
            fieldDescribes.removeIf(field -> filterFields.contains(field.getApiName())
                    || (Objects.equals(field.getDescribeApiName(), Utils.PRODUCT_GROUP_API_NAME) && Objects.equals(field.getApiName(), IObjectData.RECORD_TYPE))
                    || Objects.equals(field.getApiName(), I18N.text("BOMObj.field.product_group_id.label") + "_" + I18N.text("paas.udobj.data_id")));
            fieldDescribes.stream().filter(x -> x.getApiName().equals("owner")).forEach(x -> x.setRequired(true));
        }
        if (StringUtils.equals(describe.getApiName(), Utils.BOM_CORE_API_NAME) && arg.getImportType() == IMPORT_TYPE_EDIT) {
            fieldDescribes.removeIf(field -> BomConstants.REMOVE_FIELD_FOR_BOM_CORE_UPDATE_IMPORT.contains(field.getApiName()));
        }
    }
}
